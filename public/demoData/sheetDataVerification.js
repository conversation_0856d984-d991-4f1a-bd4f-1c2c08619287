window.sheetDataVerification = {
    "name": "Data Verification",
    "index": "Sheet_pdolzzie5xwi_1600927444446",
    "celldata": [{"r":0,"c":0,"v":{"ct":{"fa":"General","t":"g"},"m":"Drop Down List","v":"Drop Down List","bl":1}},{"r":0,"c":1,"v":{"m":"Checkbox","ct":{"fa":"General","t":"g"},"v":"Checkbox","bl":1}},{"r":0,"c":2,"v":{"ct":{"fa":"General","t":"g"},"v":"Number between 1-10","bl":1,"m":"Number between 1-10"}},{"r":0,"c":3,"v":{"m":"Text content include Luckysheet","ct":{"fa":"General","t":"g"},"v":"Text content include Luckysheet","bl":1}},{"r":0,"c":4,"v":{"ct":{"fa":"General","t":"g"},"v":"Text length between 1-5","m":"Text length between 1-5","bl":1}},{"r":0,"c":5,"v":{"m":"Date","ct":{"fa":"General","t":"g"},"v":"Date","bl":1}},{"r":0,"c":6,"v":{"m":"Identification Number","ct":{"fa":"General","t":"g"},"v":"Identification Number","bl":1}},{"r":0,"c":7,"v":{"m":"Phone Number","ct":{"fa":"General","t":"g"},"v":"Phone Number","bl":1}},{"r":1,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Fix","m":"Fix"}},{"r":1,"c":1,"v":{"m":"Fail","ct":{"fa":"General","t":"g"},"v":"Fail"}},{"r":1,"c":2,"v":{"v":1,"ct":{"fa":"General","t":"n"},"m":"1"}},{"r":1,"c":3,"v":{"m":"Luckysheet is good","ct":{"fa":"General","t":"g"},"v":"Luckysheet is good"}},{"r":1,"c":4,"v":{"m":"Welcome","ct":{"fa":"General","t":"g"},"v":"Welcome"}},{"r":1,"c":5,"v":{"m":"2020-09-24","ct":{"fa":"yyyy-MM-dd","t":"d"},"v":44098}},{"r":1,"c":6,"v":{"v":"311414199009138910","ct":{"fa":"@","t":"s"},"m":"311414199009138910"}},{"r":1,"c":7,"v":{"v":13678765439,"ct":{"fa":"General","t":"n"},"m":"13678765439"}},{"r":2,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Done","m":"Done"}},{"r":2,"c":1,"v":{"m":"Pass","ct":{"fa":"General","t":"g"},"v":"Pass"}},{"r":2,"c":2,"v":{"v":2,"ct":{"fa":"General","t":"n"},"m":"2"}},{"r":2,"c":3,"v":{"m":"I am Luckysheet","ct":{"fa":"General","t":"g"},"v":"I am Luckysheet"}},{"r":2,"c":4,"v":{"m":"Good","ct":{"fa":"General","t":"g"},"v":"Good"}},{"r":2,"c":5,"v":{"ct":{"fa":"General","t":"g"},"v":"Time","m":"Time"}},{"r":2,"c":6,"v":{"v":"31141419900913891","ct":{"fa":"@","t":"s"},"m":"31141419900913891"}},{"r":2,"c":7,"v":{"v":1367876544,"ct":{"fa":"General","t":"n"},"m":"1367876544"}},{"r":3,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Develop","m":"Develop"}},{"r":3,"c":1,"v":{"m":"Fail","ct":{"fa":"General","t":"g"},"v":"Fail"}},{"r":3,"c":2,"v":{"v":5,"ct":{"fa":"General","t":"n"},"m":"5"}},{"r":3,"c":3,"v":{"ct":{"fa":"General","t":"g"},"v":"I am luckyDemo","m":"I am luckyDemo"}},{"r":3,"c":4,"v":{"m":"Nice","ct":{"fa":"General","t":"g"},"v":"Nice"}},{"r":3,"c":5,"v":{"m":"2020-09-26","ct":{"fa":"yyyy-MM-dd","t":"d"},"v":44100}},{"r":3,"c":6,"v":{"v":"3114141990091389102","ct":{"fa":"@","t":"s"},"m":"3114141990091389102"}},{"r":3,"c":7,"v":{"v":136787654412,"ct":{"fa":"##0","t":"n"},"m":"136787654412"}},{"r":4,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Doing","m":"Doing"}},{"r":4,"c":1,"v":{"m":"Fail","ct":{"fa":"General","t":"g"},"v":"Fail"}},{"r":4,"c":2,"v":{"v":11,"ct":{"fa":"General","t":"n"},"m":"11"}},{"r":4,"c":3,"v":{"ct":{"fa":"General","t":"g"},"v":"Luckysheet Documentation","m":"Luckysheet Documentation"}},{"r":4,"c":4,"v":{"ct":{"fa":"General","t":"g"},"v":"Morning","m":"Morning"}},{"r":4,"c":5,"v":{"m":"2020-09-27","ct":{"fa":"yyyy-MM-dd","t":"d"},"v":44101}},{"r":4,"c":6,"v":{"v":"31141419900913891X","ct":{"fa":"@","t":"s"},"m":"31141419900913891X"}},{"r":4,"c":7,"v":{"v":49865342456,"ct":{"fa":"General","t":"n"},"m":"49865342456"}},{"r":5,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Develop","m":"Develop"}},{"r":5,"c":1,"v":{"m":"Fail","ct":{"fa":"General","t":"g"},"v":"Fail"}},{"r":5,"c":2,"v":{"v":3,"ct":{"fa":"General","t":"n"},"m":"3"}},{"r":5,"c":3,"v":{"m":"Luckyexcel","ct":{"fa":"General","t":"g"},"v":"Luckyexcel"}},{"r":5,"c":4,"v":{"ct":{"fa":"General","t":"g"},"v":"Tomorrow","m":"Tomorrow"}},{"r":5,"c":5,"v":{"ct":{"fa":"yyyy-MM-dd","t":"d"},"v":44071,"m":"2020-08-28"}},{"r":5,"c":6,"v":{"v":"Number","ct":{"fa":"@","t":"s"},"m":"Number"}},{"r":5,"c":7,"v":{"v":"Number","ct":{"fa":"General","t":"g"},"m":"Number"}},{"r":6,"c":0,"v":{"ct":{"fa":"General","t":"g"},"v":"Done","m":"Done"}},{"r":6,"c":1,"v":{"m":"Pass","ct":{"fa":"General","t":"g"},"v":"Pass"}},{"r":6,"c":2,"v":{"v":0,"ct":{"fa":"General","t":"n"},"m":"0"}},{"r":6,"c":3,"v":{"m":"Luckysheet Online","ct":{"fa":"General","t":"g"},"v":"Luckysheet Online"}},{"r":6,"c":4,"v":{"m":"Three","ct":{"fa":"General","t":"g"},"v":"Three"}},{"r":6,"c":5,"v":{"m":"2020-09-29","ct":{"fa":"yyyy-MM-dd","t":"d"},"v":44103}},{"r":6,"c":6,"v":{"v":"311414199301118910","ct":{"fa":"@","t":"s"},"m":"311414199301118910"}},{"r":6,"c":7,"v":{"v":23309873564,"ct":{"fa":"General","t":"n"},"m":"23309873564"}},{"r":7,"c":8,"v":{"v":null,"ct":{"fa":"General","t":"g"},"bl":1}}],
    "row": 84,
    "column": 60,
    "config": {
        "merge": {},
        "rowlen": {},
        "columnlen": {
            "0": 109,
            "2": 143,
            "3": 200,
            "4": 180,
            "6": 178,
            "7": 125
        },
        "customWidth": {
            "2": 1,
            "3": 1,
            "4": 1,
            "6": 1,
            "7": 1
        }
    },
    "luckysheet_select_save": [
        {
            "left": 963,
            "width": 125,
            "top": 240,
            "height": 19,
            "left_move": 963,
            "width_move": 125,
            "top_move": 240,
            "height_move": 19,
            "row": [
                12,
                12
            ],
            "column": [
                7,
                7
            ],
            "row_focus": 12,
            "column_focus": 7
        }
    ],
    "dataVerification": {
        "1_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "2_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "3_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "4_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "5_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "6_0": {
            "type": "dropdown",
            "type2": null,
            "value1": "Develop,Fix,Done",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "1_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": false,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "2_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": true,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "3_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": false,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "4_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": false,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "5_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": false,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "6_1": {
            "type": "checkbox",
            "type2": null,
            "value1": "Pass",
            "value2": "Fail",
            "checked": true,
            "remote": true,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "1_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "2_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "3_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "4_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "5_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "6_2": {
            "type": "number",
            "type2": "bw",
            "value1": "1",
            "value2": "10",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "1_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "2_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "3_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "4_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "5_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "6_3": {
            "type": "text_content",
            "type2": "include",
            "value1": "Luckysheet",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": true,
            "hintText": "include Luckysheet"
        },
        "1_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "2_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "3_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "4_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "5_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "6_4": {
            "type": "text_length",
            "type2": "bw",
            "value1": "1",
            "value2": "5",
            "checked": false,
            "remote": false,
            "prohibitInput": true,
            "hintShow": false,
            "hintText": ""
        },
        "1_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "2_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "3_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "4_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "5_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "6_5": {
            "type": "date",
            "type2": "bw",
            "value1": "2020-09-23",
            "value2": "2020-10-10",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "1_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "2_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "3_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "4_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "5_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "6_6": {
            "type": "validity",
            "type2": "card",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "1_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "2_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "3_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "4_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "5_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        },
        "6_7": {
            "type": "validity",
            "type2": "phone",
            "value1": "",
            "value2": "",
            "checked": false,
            "remote": false,
            "prohibitInput": false,
            "hintShow": false,
            "hintText": ""
        }
    }
}
// export default sheetDataVerification;