NODE_ENV=production
# Whether to open mock
VITE_USE_MOCK = true

# public path
VITE_PUBLIC_PATH = /

# Delete console
VITE_DROP_CONSOLE = true

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS = 'none'

# Whether to delete origin files when using compress, default false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# Basic interface address SPA
VITE_GLOB_API_URL=/basic-api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL=/upload

# 文件预览接口  可选
VITE_GLOB_UPLOAD_PREVIEW = http://***************:8012/onlinePreview?url=

#外部url地址
VITE_GLOB_OUT_LINK_URL = http://localhost:4100,http://localhost:8827

# Interface prefix
VITE_GLOB_API_URL_PREFIX=

# Whether to enable image compression
VITE_USE_IMAGEMIN= true

# use pwa
VITE_USE_PWA = false

# Is it compatible with older browsers
VITE_LEGACY = false
