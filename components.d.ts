/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACol: typeof import('ant-design-vue/es')['Col']
    AddEvent: typeof import('./src/components/CreateCodeStep/src/components/AddEvent.vue')['default']
    ADirectoryTree: typeof import('ant-design-vue/es')['DirectoryTree']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    AdvancedTable: typeof import('./src/components/Table/src/components/advancedQuery/AdvancedTable.vue')['default']
    AInput: typeof import('ant-design-vue/es')['Input']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AModal: typeof import('ant-design-vue/es')['Modal']
    AntdFormConfig: typeof import('./src/components/Designer/src/components/AntdFormConfig.vue')['default']
    AntdHeader: typeof import('./src/components/Designer/src/components/AntdHeader.vue')['default']
    AntdWidgetForm: typeof import('./src/components/Designer/src/components/AntdWidgetForm.vue')['default']
    AntdWidgetFormItem: typeof import('./src/components/Designer/src/components/AntdWidgetFormItem.vue')['default']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    ApiAssoConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/ApiAssoConfig.vue')['default']
    ApiCascader: typeof import('./src/components/Form/src/components/ApiCascader.vue')['default']
    ApiCheckboxGroup: typeof import('./src/components/Form/src/components/ApiCheckboxGroup.vue')['default']
    ApiComplete: typeof import('./src/components/Form/src/components/ApiComplete.vue')['default']
    ApiConfig: typeof import('./src/components/ApiConfig/src/ApiConfig.vue')['default']
    ApiConfigSelect: typeof import('./src/components/ApiConfig/src/components/ApiConfigSelect.vue')['default']
    ApiRadioGroup: typeof import('./src/components/Form/src/components/ApiRadioGroup.vue')['default']
    ApiSelect: typeof import('./src/components/ApiConfig/src/components/ApiSelect.vue')['default']
    ApiTree: typeof import('./src/components/Form/src/components/ApiTree.vue')['default']
    ApiTreeSelect: typeof import('./src/components/Form/src/components/ApiTreeSelect.vue')['default']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AppDarkModeToggle: typeof import('./src/components/Application/src/AppDarkModeToggle.vue')['default']
    AppLogo: typeof import('./src/components/Application/src/AppLogo.vue')['default']
    AppProvider: typeof import('./src/components/Application/src/AppProvider.vue')['default']
    AppSearch: typeof import('./src/components/Application/src/search/AppSearch.vue')['default']
    AppSearchFooter: typeof import('./src/components/Application/src/search/AppSearchFooter.vue')['default']
    AppSearchKeyItem: typeof import('./src/components/Application/src/search/AppSearchKeyItem.vue')['default']
    AppSearchModal: typeof import('./src/components/Application/src/search/AppSearchModal.vue')['default']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AssociateSelect: typeof import('./src/components/AssociateSelect/src/AssociateSelect.vue')['default']
    ATag: typeof import('ant-design-vue/es')['Tag']
    Authority: typeof import('./src/components/Authority/src/Authority.vue')['default']
    AutoCodeRule: typeof import('./src/components/Form/src/components/AutoCodeRule.vue')['default']
    BarCode: typeof import('./src/components/Form/src/components/BarCode.vue')['default']
    Base: typeof import('./src/components/DesktopDesigner/src/components/dashboard/base.vue')['default']
    Basic: typeof import('./src/components/DesktopDesigner/src/Basic.vue')['default']
    BasicArrow: typeof import('./src/components/Basic/src/BasicArrow.vue')['default']
    BasicButton: typeof import('./src/components/Button/src/BasicButton.vue')['default']
    BasicDrawer: typeof import('./src/components/Drawer/src/BasicDrawer.vue')['default']
    BasicForm: typeof import('./src/components/Form/src/BasicForm.vue')['default']
    BasicHelp: typeof import('./src/components/Basic/src/BasicHelp.vue')['default']
    BasicMenu: typeof import('./src/components/Menu/src/BasicMenu.vue')['default']
    BasicMenuItem: typeof import('./src/components/Menu/src/components/BasicMenuItem.vue')['default']
    BasicModal: typeof import('./src/components/Modal/src/BasicModal.vue')['default']
    BasicModalExcel: typeof import('./src/components/Modal/src/BasicModalExcel.vue')['default']
    BasicSubMenuItem: typeof import('./src/components/Menu/src/components/BasicSubMenuItem.vue')['default']
    BasicTable: typeof import('./src/components/FormTable/src/BasicTable.vue')['default']
    BasicTableErp: typeof import('./src/components/Table/src/BasicTableErp.vue')['default']
    BasicTitle: typeof import('./src/components/Basic/src/BasicTitle.vue')['default']
    BasicUpload: typeof import('./src/components/Upload/src/BasicUpload.vue')['default']
    Button: typeof import('./src/components/ButtonForm/src/Button.vue')['default']
    CardList: typeof import('./src/components/CardList/src/CardList.vue')['default']
    CategoryModal: typeof import('./src/components/CategoryModal/src/CategoryModal.vue')['default']
    ChangeCategoryModal: typeof import('./src/components/CategoryModal/src/ChangeCategoryModal.vue')['default']
    ChildTable: typeof import('./src/components/ChildTable/src/ChildTable.vue')['default']
    ClickOutSide: typeof import('./src/components/ClickOutSide/src/ClickOutSide.vue')['default']
    CodeEditor: typeof import('./src/components/CodeEditor/src/CodeEditor.vue')['default']
    CodeMirror: typeof import('./src/components/CodeEditor/src/codemirror/CodeMirror.vue')['default']
    CodeTextArea: typeof import('./src/components/Input/src/CodeTextArea.vue')['default']
    CollapseContainer: typeof import('./src/components/Container/src/collapse/CollapseContainer.vue')['default']
    CollapseHeader: typeof import('./src/components/Container/src/collapse/CollapseHeader.vue')['default']
    CollapseTransition: typeof import('./src/components/Transition/src/CollapseTransition.vue')['default']
    ColorPicker: typeof import('./src/components/ColorPicker/src/ColorPicker.vue')['default']
    ColumnSetting: typeof import('./src/components/FormTable/src/components/settings/ColumnSetting.vue')['default']
    CommonInfo: typeof import('./src/components/Form/src/components/CommonInfo.vue')['default']
    ComponentEvent: typeof import('./src/components/Designer/src/components/componentProperty/ComponentEvent.vue')['default']
    ComponentGroup: typeof import('./src/components/Designer/src/components/ComponentGroup.vue')['default']
    ComponentListConfigs: typeof import('./src/components/Designer/src/components/componentProperty/ComponentListConfigs.vue')['default']
    Computation: typeof import('./src/components/Computation/src/Computation.vue')['default']
    ComputationalSetting: typeof import('./src/components/Designer/src/components/componentProperty/settings/ComputationalSetting.vue')['default']
    Config: typeof import('./src/components/DesktopDesigner/src/components/dashboard/config.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu/src/ContextMenu.vue')['default']
    CopperModal: typeof import('./src/components/Cropper/src/CopperModal.vue')['default']
    CountButton: typeof import('./src/components/CountDown/src/CountButton.vue')['default']
    CountdownInput: typeof import('./src/components/CountDown/src/CountdownInput.vue')['default']
    CountTo: typeof import('./src/components/CountTo/src/CountTo.vue')['default']
    Cropper: typeof import('./src/components/Cropper/src/Cropper.vue')['default']
    CropperAvatar: typeof import('./src/components/Cropper/src/CropperAvatar.vue')['default']
    CustomConfig: typeof import('./src/components/DesktopDesigner/src/components/base/CustomConfig.vue')['default']
    CustomItem: typeof import('./src/components/DesktopDesigner/src/components/base/CustomItem.vue')['default']
    DatabaseLinkDrawer: typeof import('./src/components/DbSelect/components/DatabaseLinkDrawer.vue')['default']
    DataSourceAssoConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/DataSourceAssoConfig.vue')['default']
    DatasourceDrawer: typeof import('./src/components/DataSourceSelect/components/DatasourceDrawer.vue')['default']
    DatasourceSelect: typeof import('./src/components/DataSourceSelect/src/DatasourceSelect.vue')['default']
    DataSourceSetting: typeof import('./src/components/Designer/src/components/componentProperty/settings/DataSourceSetting.vue')['default']
    DatePicker: typeof import('./src/components/DatePicker/src/DatePicker.vue')['default']
    DbSelect: typeof import('./src/components/DbSelect/src/DbSelect.vue')['default']
    DefaultConfig: typeof import('./src/components/DesktopDesigner/src/components/base/DefaultConfig.vue')['default']
    DefaultItem: typeof import('./src/components/DesktopDesigner/src/components/base/DefaultItem.vue')['default']
    Description: typeof import('./src/components/Description/src/Description.vue')['default']
    Designer: typeof import('./src/components/Designer/src/Designer.vue')['default']
    DesignLogo: typeof import('./src/components/ModalPanel/src/DesignLogo.vue')['default']
    DesktopDesignEmpty: typeof import('./src/components/ModalPanel/src/DesktopDesignEmpty.vue')['default']
    DesktopForm: typeof import('./src/components/SystemForm/src/DesktopForm.vue')['default']
    DesktopPreview: typeof import('./src/components/DesktopDesigner/src/components/DesktopPreview.vue')['default']
    DicAssoConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/DicAssoConfig.vue')['default']
    DicDetailDrawer: typeof import('./src/components/DicSelect/components/DicDetailDrawer.vue')['default']
    DicItemDrawer: typeof import('./src/components/DicItemSelect/components/DicItemDrawer.vue')['default']
    DicItemSelect: typeof import('./src/components/DicItemSelect/src/DicItemSelect.vue')['default']
    DicSelect: typeof import('./src/components/DicSelect/src/DicSelect.vue')['default']
    DicTreeSelect: typeof import('./src/components/DicTreeSelect/src/DicTreeSelect.vue')['default']
    DragVerify: typeof import('./src/components/Verify/src/DragVerify.vue')['default']
    DrawerFooter: typeof import('./src/components/Drawer/src/components/DrawerFooter.vue')['default']
    DrawerHeader: typeof import('./src/components/Drawer/src/components/DrawerHeader.vue')['default']
    Dropdown: typeof import('./src/components/Dropdown/src/Dropdown.vue')['default']
    EditableCell: typeof import('./src/components/FormTable/src/components/editable/EditableCell.vue')['default']
    Editor: typeof import('./src/components/Tinymce/src/Editor.vue')['default']
    EditTableHeaderIcon: typeof import('./src/components/FormTable/src/components/EditTableHeaderIcon.vue')['default']
    Empty: typeof import('./src/components/SystemForm/src/Empty.vue')['default']
    EmptyBox: typeof import('./src/components/ModalPanel/src/EmptyBox.vue')['default']
    EventArea: typeof import('./src/components/CreateCodeStep/src/components/EventArea.vue')['default']
    EventEditModal: typeof import('./src/components/Designer/src/components/EventEditModal.vue')['default']
    ExportExcelModal: typeof import('./src/components/Excel/src/ExportExcelModal.vue')['default']
    FewerLeft: typeof import('./src/components/ModalPanel/src/FewerLeft.vue')['default']
    FewerRight: typeof import('./src/components/ModalPanel/src/FewerRight.vue')['default']
    File: typeof import('./src/components/Form/src/components/File.vue')['default']
    FileList: typeof import('./src/components/Upload/src/FileList.vue')['default']
    FlowChart: typeof import('./src/components/FlowChart/src/FlowChart.vue')['default']
    FlowChartToolbar: typeof import('./src/components/FlowChart/src/FlowChartToolbar.vue')['default']
    Form: typeof import('./src/components/SystemForm/src/Form.vue')['default']
    FormAction: typeof import('./src/components/Form/src/components/FormAction.vue')['default']
    FormDesignStep: typeof import('./src/components/CreateCodeStep/src/FormDesignStep.vue')['default']
    FormEventStep: typeof import('./src/components/CreateCodeStep/src/FormEventStep.vue')['default']
    FormFields: typeof import('./src/components/Designer/src/components/componentProperty/settings/FormFields.vue')['default']
    FormItem: typeof import('./src/components/Form/src/components/FormItem.vue')['default']
    FormPreviewDrawer: typeof import('./src/components/CreateCodeStep/src/components/FormPreviewDrawer.vue')['default']
    FormSettingModal: typeof import('./src/components/CreateCodeStep/src/components/FormSettingModal.vue')['default']
    FormView: typeof import('./src/components/Form/src/components/FormView.vue')['default']
    FullScreenSetting: typeof import('./src/components/FormTable/src/components/settings/FullScreenSetting.vue')['default']
    Functional: typeof import('./src/components/Preview/src/Functional.vue')['default']
    HeaderCell: typeof import('./src/components/FormTable/src/components/HeaderCell.vue')['default']
    HiddenComponent: typeof import('./src/components/Designer/src/components/HiddenComponent.vue')['default']
    Icon: typeof import('./src/components/Icon/src/Icon.vue')['default']
    IconPicker: typeof import('./src/components/Icon/src/IconPicker.vue')['default']
    Image: typeof import('./src/components/Form/src/components/Image.vue')['default']
    ImgRotate: typeof import('./src/components/Verify/src/ImgRotate.vue')['default']
    ImgUpload: typeof import('./src/components/Tinymce/src/ImgUpload.vue')['default']
    ImportExcel: typeof import('./src/components/Excel/src/ImportExcel.vue')['default']
    ImportModal: typeof import('./src/components/Import/src/ImportModal.vue')['default']
    Index: typeof import('./src/components/IconFontSymbol/Index.vue')['default']
    Input: typeof import('./src/components/Input/src/Input.vue')['default']
    InputModel: typeof import('./src/components/ApiConfig/src/components/InputModel.vue')['default']
    InputParams: typeof import('./src/components/ApiConfig/src/components/InputParams.vue')['default']
    InputPassword: typeof import('./src/components/InputPassword/src/InputPassword.vue')['default']
    InputUpload: typeof import('./src/components/Form/src/components/InputUpload.vue')['default']
    InterfaceAddressList: typeof import('./src/components/ApiConfig/src/components/InterfaceAddressList.vue')['default']
    InterfaceInfoModal: typeof import('./src/components/Designer/src/components/componentProperty/settings/InterfaceInfoModal.vue')['default']
    JsonPreview: typeof import('./src/components/CodeEditor/src/json-preview/JsonPreview.vue')['default']
    JumpMenu: typeof import('./src/components/MenuSelect/src/JumpMenu.vue')['default']
    LabelComponent: typeof import('./src/components/LabelComponent/src/LabelComponent.vue')['default']
    LabelComponentApiConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/LabelComponentApiConfig.vue')['default']
    LabelComponentInfo: typeof import('./src/components/LabelComponent/src/LabelComponentInfo.vue')['default']
    LazyContainer: typeof import('./src/components/Container/src/LazyContainer.vue')['default']
    ListSettingConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/ListSettingConfig.vue')['default']
    Loading: typeof import('./src/components/Loading/src/Loading.vue')['default']
    LoadingBox: typeof import('./src/components/ModalPanel/src/LoadingBox.vue')['default']
    MapModal: typeof import('./src/components/Map/src/MapModal.vue')['default']
    Markdown: typeof import('./src/components/Markdown/src/Markdown.vue')['default']
    MarkdownViewer: typeof import('./src/components/Markdown/src/MarkdownViewer.vue')['default']
    MenuCollapseTransition: typeof import('./src/components/SimpleMenu/src/components/MenuCollapseTransition.vue')['default']
    MenuConfigStep: typeof import('./src/components/CreateCodeStep/src/MenuConfigStep.vue')['default']
    MenuItem: typeof import('./src/components/SimpleMenu/src/components/MenuItem.vue')['default']
    MenuItemContent: typeof import('./src/components/Menu/src/components/MenuItemContent.vue')['default']
    MenuSelect: typeof import('./src/components/MenuSelect/src/MenuSelect.vue')['default']
    ModalClose: typeof import('./src/components/Modal/src/components/ModalClose.vue')['default']
    ModalFooter: typeof import('./src/components/Modal/src/components/ModalFooter.vue')['default']
    ModalHeader: typeof import('./src/components/Modal/src/components/ModalHeader.vue')['default']
    ModalPanel: typeof import('./src/components/ModalPanel/src/ModalPanel.vue')['default']
    ModalWrapper: typeof import('./src/components/Modal/src/components/ModalWrapper.vue')['default']
    MoneyChineseInput: typeof import('./src/components/Form/src/components/MoneyChineseInput.vue')['default']
    MultiplePopup: typeof import('./src/components/MultiplePopup/src/MultiplePopup.vue')['default']
    MultipleSelect: typeof import('./src/components/MultiplePopup/src/components/MultipleSelect.vue')['default']
    MyMenu: typeof import('./src/components/SimpleMenu/src/components/MyMenu.vue')['default']
    NetworkSelect: typeof import('./src/components/NetworkSelect/src/NetworkSelect.vue')['default']
    NodeEvent: typeof import('./src/components/CreateCodeStep/src/components/NodeEvent.vue')['default']
    NodeHead: typeof import('./src/components/ModalPanel/src/NodeHead.vue')['default']
    OneForOne: typeof import('./src/components/OneForOne/src/OneForOne.vue')['default']
    OneForOneItem: typeof import('./src/components/OneForOne/src/components/OneForOneItem.vue')['default']
    Opinion: typeof import('./src/components/Opinion/src/Opinion.vue')['default']
    OrganizationalTree: typeof import('./src/components/SelectOrganizational/src/OrganizationalTree.vue')['default']
    PageFooter: typeof import('./src/components/Page/src/PageFooter.vue')['default']
    PageLayout: typeof import('./src/components/ModalPanel/src/PageLayout.vue')['default']
    PageWrapper: typeof import('./src/components/Page/src/PageWrapper.vue')['default']
    PopConfirmButton: typeof import('./src/components/Button/src/PopConfirmButton.vue')['default']
    PostCard: typeof import('./src/components/SelectOrganizational/src/card/PostCard.vue')['default']
    Preview: typeof import('./src/components/Preview/src/Preview.vue')['default']
    PreviewDrawer: typeof import('./src/components/Designer/src/components/PreviewDrawer.vue')['default']
    PropertyOption: typeof import('./src/components/Designer/src/components/componentProperty/PropertyOption.vue')['default']
    PushOrder: typeof import('./src/components/CreateCodeStep/src/components/PushOrder.vue')['default']
    Qrcode: typeof import('./src/components/Qrcode/src/Qrcode.vue')['default']
    QrCode: typeof import('./src/components/Form/src/components/QrCode.vue')['default']
    QuickTable: typeof import('./src/components/Table/src/components/advancedQuery/QuickTable.vue')['default']
    RadioButtonGroup: typeof import('./src/components/Form/src/components/RadioButtonGroup.vue')['default']
    RangePicker: typeof import('./src/components/RangePicker/src/RangePicker.vue')['default']
    RedoSetting: typeof import('./src/components/FormTable/src/components/settings/RedoSetting.vue')['default']
    RegularSetting: typeof import('./src/components/Designer/src/components/componentProperty/RegularSetting.vue')['default']
    ResizeFixedLayout: typeof import('./src/components/ModalPanel/src/ResizeFixedLayout.vue')['default']
    ResizePageWrapper: typeof import('./src/components/Page/src/ResizePageWrapper.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor/src/RichTextEditor.vue')['default']
    RoleCard: typeof import('./src/components/SelectOrganizational/src/card/RoleCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScriptConfig: typeof import('./src/components/Designer/src/components/componentProperty/settings/ScriptConfig.vue')['default']
    Scrollbar: typeof import('./src/components/Scrollbar/src/Scrollbar.vue')['default']
    ScrollContainer: typeof import('./src/components/Container/src/ScrollContainer.vue')['default']
    SearchBox: typeof import('./src/components/ModalPanel/src/SearchBox.vue')['default']
    SearchFormItem: typeof import('./src/components/Form/src/components/SearchFormItem.vue')['default']
    Select: typeof import('./src/components/Select/src/Select.vue')['default']
    SelectArea: typeof import('./src/components/Form/src/components/SelectArea.vue')['default']
    SelectAreaBack: typeof import('./src/components/Form/src/components/SelectAreaBack.vue')['default']
    SelectDatabase: typeof import('./src/components/CreateCodeStep/src/components/SelectDatabase.vue')['default']
    SelectDepartment: typeof import('./src/components/SelectOrganizational/src/SelectDepartment.vue')['default']
    SelectDesignList: typeof import('./src/components/Form/src/components/SelectDesignList.vue')['default']
    Selected: typeof import('./src/components/SelectOrganizational/src/Selected.vue')['default']
    SelectForm: typeof import('./src/components/Form/src/components/SelectForm.vue')['default']
    SelectInterfaceAddress: typeof import('./src/components/ApiConfig/src/components/SelectInterfaceAddress.vue')['default']
    SelectMap: typeof import('./src/components/Form/src/components/SelectMap.vue')['default']
    SelectMember: typeof import('./src/components/SelectOrganizational/src/SelectMember.vue')['default']
    SelectMessageTemplate: typeof import('./src/components/PushMessage/src/components/SelectMessageTemplate.vue')['default']
    SelectPost: typeof import('./src/components/SelectOrganizational/src/SelectPost.vue')['default']
    SelectPushMessage: typeof import('./src/components/Designer/src/components/componentProperty/settings/SelectPushMessage.vue')['default']
    SelectRole: typeof import('./src/components/SelectOrganizational/src/SelectRole.vue')['default']
    SelectUser: typeof import('./src/components/SelectOrganizational/src/SelectUser.vue')['default']
    SelectUserModal: typeof import('./src/components/Form/src/components/SelectUserModal.vue')['default']
    SetRuleUserModal: typeof import('./src/components/Designer/src/components/generateComponent/SetRuleUserModal.vue')['default']
    Settings: typeof import('./src/components/FormTable/src/components/settings/index.vue')['default']
    Signature: typeof import('./src/components/Form/src/components/Signature.vue')['default']
    SimpleForm: typeof import('./src/components/SimpleForm/src/SimpleForm.vue')['default']
    SimpleFormGrid: typeof import('./src/components/SimpleForm/src/components/SimpleFormGrid.vue')['default']
    SimpleFormItem: typeof import('./src/components/SimpleForm/src/components/SimpleFormItem.vue')['default']
    SimpleMenu: typeof import('./src/components/SimpleMenu/src/SimpleMenu.vue')['default']
    SimpleMenuTag: typeof import('./src/components/SimpleMenu/src/SimpleMenuTag.vue')['default']
    SimpleSubMenu: typeof import('./src/components/SimpleMenu/src/SimpleSubMenu.vue')['default']
    SizeSetting: typeof import('./src/components/FormTable/src/components/settings/SizeSetting.vue')['default']
    Slider: typeof import('./src/components/Slider/src/Slider.vue')['default']
    StaticData: typeof import('./src/components/Designer/src/components/componentProperty/settings/StaticData.vue')['default']
    StrengthMeter: typeof import('./src/components/StrengthMeter/src/StrengthMeter.vue')['default']
    StructureConfigStep: typeof import('./src/components/CreateCodeStep/src/StructureConfigStep.vue')['default']
    StyleConfiguration: typeof import('./src/components/Designer/src/components/componentProperty/settings/StyleConfiguration.vue')['default']
    SubForm: typeof import('./src/components/Form/src/components/SubForm.vue')['default']
    SubMenuItem: typeof import('./src/components/SimpleMenu/src/components/SubMenuItem.vue')['default']
    SunForm: typeof import('./src/components/Form/src/components/SunForm.vue')['default']
    SvgIcon: typeof import('./src/components/Icon/src/SvgIcon.vue')['default']
    Switch: typeof import('./src/components/Switch/src/Switch.vue')['default']
    TableAction: typeof import('./src/components/FormTable/src/components/TableAction.vue')['default']
    TableConfigStep: typeof import('./src/components/CreateCodeStep/src/TableConfigStep.vue')['default']
    TableFooter: typeof import('./src/components/FormTable/src/components/TableFooter.vue')['default']
    TableHeader: typeof import('./src/components/FormTable/src/components/TableHeader.vue')['default']
    TableHeaderErp: typeof import('./src/components/Table/src/components/TableHeaderErp.vue')['default']
    TableImg: typeof import('./src/components/FormTable/src/components/TableImg.vue')['default']
    TableLayout: typeof import('./src/components/Form/src/components/TableLayout.vue')['default']
    TableLayoutOption: typeof import('./src/components/Designer/src/components/componentProperty/TableLayoutOption.vue')['default']
    TableLayoutPreview: typeof import('./src/components/Form/src/components/TableLayoutPreview.vue')['default']
    TableModal: typeof import('./src/components/ChildTable/src/components/TableModal.vue')['default']
    TableNameModal: typeof import('./src/components/CreateCodeStep/src/components/TableNameModal.vue')['default']
    TableTitle: typeof import('./src/components/FormTable/src/components/TableTitle.vue')['default']
    TemplateList: typeof import('./src/components/PushMessage/src/components/TemplateList.vue')['default']
    Text: typeof import('./src/components/Text/src/Text.vue')['default']
    ThumbUrl: typeof import('./src/components/Upload/src/ThumbUrl.vue')['default']
    Time: typeof import('./src/components/Time/src/Time.vue')['default']
    TimePicker: typeof import('./src/components/TimePicker/src/TimePicker.vue')['default']
    TimeRangePicker: typeof import('./src/components/TimeRangePicker/src/TimeRangePicker.vue')['default']
    Title: typeof import('./src/components/Title/src/Title.vue')['default']
    TooltipButton: typeof import('./src/components/Button/src/TooltipButton.vue')['default']
    Tree: typeof import('./src/components/Tree/src/Tree.vue')['default']
    TreeComponent: typeof import('./src/components/TreeStructure/src/TreeComponent.vue')['default']
    TreeHeader: typeof import('./src/components/Tree/src/TreeHeader.vue')['default']
    TreeSelectComponent: typeof import('./src/components/TreeStructure/src/TreeSelectComponent.vue')['default']
    Upload: typeof import('./src/components/Form/src/components/Upload.vue')['default']
    UploadModal: typeof import('./src/components/Upload/src/UploadModal.vue')['default']
    UploadPreviewModal: typeof import('./src/components/Upload/src/UploadPreviewModal.vue')['default']
    UserCard: typeof import('./src/components/SelectOrganizational/src/card/UserCard.vue')['default']
    UserPostChange: typeof import('./src/components/Application/src/UserPostChange.vue')['default']
    View: typeof import('./src/components/DesktopDesigner/src/components/dashboard/view.vue')['default']
    ViewBtn: typeof import('./src/components/Table/src/components/advancedQuery/ViewBtn.vue')['default']
    ViewDesignStep: typeof import('./src/components/CreateCodeStep/src/ViewDesignStep.vue')['default']
    ViewModal: typeof import('./src/components/Table/src/components/advancedQuery/ViewModal.vue')['default']
    VirtualScroll: typeof import('./src/components/VirtualScroll/src/VirtualScroll.vue')['default']
    WorkflowSearchBox: typeof import('./src/components/ModalPanel/src/WorkflowSearchBox.vue')['default']
    XjrIframe: typeof import('./src/components/Form/src/components/XjrIframe.vue')['default']
    ZoomInOrOut: typeof import('./src/components/ModalPanel/src/ZoomInOrOut.vue')['default']
  }
}
