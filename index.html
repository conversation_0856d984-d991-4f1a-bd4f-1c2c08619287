<!DOCTYPE html>
<html lang="en" id="htmlRoot">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
    />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <title></title>
    <link rel="icon" href="/favicon.ico" />
  </head>
  <body>
    <div id="GLOB_API_URL" style="display: none">%VITE_GLOB_API_URL%</div>
    <!-- 如果不需要非对称加密 可以注释此脚本 -->
    <!-- <script src="/jsencrypt.min.js"></script>  -->
    <script src="/iconfont.js"></script>
    <script src="/desktopIconfont.js"></script>
    <link rel="stylesheet" href="/plugins/css/pluginsCss.css" />
    <link rel="stylesheet" href="/plugins/plugins.css" />
    <link rel="stylesheet" href="/css/luckysheet.css" />
    <link rel="stylesheet" href="/assets/iconfont/iconfont.css" />
    <script src="/plugins/js/plugin.js"></script>
    <script src="/luckysheet.umd.js"></script>

    <script>
      (async () => {
        var htmlRoot = document.getElementById('htmlRoot');
        var theme = window.localStorage.getItem('__APP__DARK__MODE__');
        if (htmlRoot && theme) {
          htmlRoot.setAttribute('data-theme', theme);
          theme = htmlRoot = null;
        }
        window._AMapSecurityConfig = {
          securityJsCode: '1b21905551519807626f2bcd191a6036',
        };
        var url = document.getElementById('GLOB_API_URL');
        try {
          const responseStream = await fetch(url.innerText + '/system/logoConfig/logo-info');
          const { data } = await responseStream.json();
          if (data.shortName) {
            document.title = data.shortName;
            // document.getElementById('app-loading-title').innerText = data.shortName;
          }
          // if (data.refreshLogoUrl) {
          //   document.getElementById('app-loading-logo').setAttribute('src', data.refreshLogoUrl);
          // }
          // document.getElementById('app-loading-logo').setAttribute('style', 'display: block');
        } catch {
          console.error('从服务器获取数据出错，请联系管理员！');
        }
      })();
    </script>
    <div id="app">
      <style>
        html[data-theme='dark'] .app-loading {
          background-color: #2c344a;
        }

        html[data-theme='dark'] .app-loading .app-loading-title {
          color: rgb(255 255 255 / 85%);
        }

        .app-loading {
          display: flex;
          width: 100%;
          height: 100%;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          background-color: #f4f7f9;
        }

        .app-loading .app-loading-wrap {
          position: absolute;
          top: 50%;
          left: 50%;
          display: flex;
          transform: translate3d(-50%, -50%, 0);
          justify-content: center;
          align-items: center;
          flex-direction: column;
        }

        .app-loading .dots {
          display: flex;
          padding: 98px;
          justify-content: center;
          align-items: center;
        }

        .app-loading .app-loading-title {
          display: flex;
          margin-top: 30px;
          font-size: 30px;
          color: rgb(0 0 0 / 85%);
          justify-content: center;
          align-items: center;
        }

        .app-loading .app-loading-logo {
          display: block;
          width: 90px;
          margin: 0 auto;
          margin-bottom: 20px;
        }

        .dot {
          position: relative;
          display: inline-block;
          width: 48px;
          height: 48px;
          margin-top: 30px;
          font-size: 32px;
          transform: rotate(45deg);
          box-sizing: border-box;
          animation: antRotate 1.2s infinite linear;
        }

        .dot i {
          position: absolute;
          display: block;
          width: 20px;
          height: 20px;
          background-color: #0065cc;
          border-radius: 100%;
          opacity: 0.3;
          transform: scale(0.75);
          animation: antSpinMove 1s infinite linear alternate;
          transform-origin: 50% 50%;
        }

        .dot i:nth-child(1) {
          top: 0;
          left: 0;
        }

        .dot i:nth-child(2) {
          top: 0;
          right: 0;
          animation-delay: 0.4s;
        }

        .dot i:nth-child(3) {
          right: 0;
          bottom: 0;
          animation-delay: 0.8s;
        }

        .dot i:nth-child(4) {
          bottom: 0;
          left: 0;
          animation-delay: 1.2s;
        }
        @keyframes antRotate {
          to {
            transform: rotate(405deg);
          }
        }
        @keyframes antRotate {
          to {
            transform: rotate(405deg);
          }
        }
        @keyframes antSpinMove {
          to {
            opacity: 1;
          }
        }
        @keyframes antSpinMove {
          to {
            opacity: 1;
          }
        }
      </style>
      <div class="app-loading">
        <div class="app-loading-wrap">
          <img
            src="/resource/img/logo.png"
            class="app-loading-logo"
            id="app-loading-logo"
            alt="Logo"
          />
          <div class="app-loading-dots">
            <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
          </div>
          <div class="app-loading-title" id="app-loading-title">%VITE_GLOB_APP_TITLE%</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
