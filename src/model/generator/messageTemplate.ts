import { MessageType } from '/@/enums/messageTemplate';
import { BasicFetchResult } from '/@/api/model/baseModel';
export interface TemplateTypeItem {
  value: string;
  label: string;
}
export interface MessageTemplateForm {
  id?: string;
  code: string;
  name: string;
  templateType: string;
  messageType: MessageType;
  enabledMark: number;
  sortCode: number;
  remark: string;
  messageConfig: string;
}
export interface ConfigureItem {
  name: string;
  description: string;
}
export interface MessageContent {
  title: string; // 消息标题
  content: string; //消息内容
  templateCode: string; //模板编号
  appKey: string; //小程序
  configs: Array<MessageContentConfigItem>; //短信配置 公众号配置
}
interface MessageContentConfigItem {
  variable: string;
  parameter: string;
}
export interface SearchParams {
  keyword: string;
  limit: number;
  size: number;
  templateType: string;
}
export interface BasicData {
  id?: string;
  code: string; //编码
  name: string; //名称
  category: string; //类别
  apiConfig: string; //api
  isMenu: number; //菜单
  sortCode: number; //排序
  parentId: string; //上级
  icon: string; //图标
  remark: string; //描述
  enabledMark?: number; //启用1 禁用0
  content: string; // 设计器Json string
}
export type PageResult = BasicFetchResult<BasicData>;
