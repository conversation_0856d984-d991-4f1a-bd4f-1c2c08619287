import { ColEx } from '/@/components/TableForm/src/types';
import { ButtonLocation, FormSchema, Rule } from '/@/components/Form';
import { HiddenComponentInfo } from '/@/components/Designer/src/types/index';

/**
 * 表单设计器 返回类型
 */
export interface FormJson {
  //表单总体配置
  config: FormOptionModel;
  //各组件配置
  list: ComponentOptionModel[];
  hiddenComponent: HiddenComponentInfo[];
}

export interface GeneratorFormSchema extends FormSchema {
  key: string;
  group: string;
  name: string;
  icon: string;
}

/**
 * 组件通用配置 实体模型
 */
export interface ComponentOptionModel {
  //key 唯一
  key: string;
  //field name
  bindField: string;
  //table name
  bindTable: string;
  //名称
  label: string;
  //图标
  rules?: Rule[];
  //类型
  type: string;
  //各组件 特殊配置信息  具体配置信息
  options?: ComponentConfigModel;
  //栅格布局的子节点  栅格布局特有
  layout?: LayoutOptionModel[];
  //子表单子节点 子表单特有
  children?: ComponentOptionModel[];
  //range特有
  bindStartTime?: string;
  bindEndTime?: string;
  //隐藏组件特有
  code?: string; //编码
  value?: string; //值
  //界面优先-结构配置 字段备注展示
  typeName?: string; //类型的中文名称
  //是否是子表组件
  isSubFormChild?: boolean;
  //是否是单表组件
  isSingleFormChild?: boolean;
}

/**
 * 表单整体配置 实体模型
 */
export interface FormOptionModel {
  //是否需要隐藏 必填标记
  hideRequiredMark?: boolean;

  //表单尺寸
  size?: 'default' | 'small' | 'large';

  //对齐方式
  layout?: 'vertical' | 'inline' | 'horizontal';

  //label配置
  labelCol?: Partial<ColEx>;

  //对齐方式
  labelAlign?: 'left' | 'right';

  //按钮位置
  buttonLocation?: ButtonLocation;

  //表单形式
  formType?: 'modal' | 'drawer';

  //表单宽度
  formWidth?: number;
}

/**
 * 组件具体配置 实体模型
 */
export interface ComponentConfigModel {
  //绑定表
  table: string;
  //绑定字段
  field: string;
  //宽度
  width?: string;

  // 占位符
  placeholder?: string;
  // 栅格布局占多少列
  col?: number;
  //是否只读
  readonly?: boolean;
  //是否启用
  disabled?: boolean;
  //是否需要label
  isLabel?: boolean;
  showLabel?: boolean;
  //label
  labelWidth?: number;

  //子表单 表格列宽
  columnWidth?: number;
  //默认值
  defaultValue?: any;
  //是否必填
  required?: boolean;
  //是否能够清空
  allowClear: boolean;
  //自定义验证 正则
  rules?: Rule[];

  //事件配置
  events: any;

  //下拉框模式（下拉框框特有） 'default' | 'multiple' | 'tags' | 'combobox'
  mode?: string;
  //是否为远程组件（下拉，单选，多选等组件）
  remote?: boolean;
  //如果是远程组件，代表远程类型
  datasourceType: 'staticData' | 'dic' | 'datasource' | 'api';
  //静态数据默认选择
  defaultSelect?: string | number;
  //datasourceType 是 dic 数据字典项目code
  itemId: string;
  //datasourceType 是datasrouce 存入数据源Id
  sourceId: string;
  //选项 （下拉，单选，多选等组件）
  staticOptions?: any[];
  //  请求地址 （下拉，单选，多选等组件）
  api?: string;
  //api配置信息（下拉，单选，多选等组件）
  apiConfig: any;
  //数据字典配置信息（下拉，单选，多选等组件）
  dicOptions: Array<any>;
  //返回值数据字段 （下拉，单选，多选等组件）
  resultField?: string;
  //显示字段 （下拉，单选，多选等组件）
  labelField?: string;
  //保存字段 （下拉，单选，多选等组件）
  valueField?: string;
  //子字段（树下拉，级联等）
  childrenField?: string;

  //最大输入
  maxLength?: number;
  //是否可以半选(评分特有)
  allowHalf?: boolean;
  //最大值（数字框特有）
  max?: number;
  //最小值（数字框特有）
  min?: number;
  //数值精度（数字框特有）
  precision: number;
  //小数点（数字框特有）
  decimalSeparator: string;
  //最大行 （多行文本框特有）
  maxRow?: number;
  //是否自适应高度(多行文本特有)
  autoSize: boolean;
  // // 是否显示字数(多行文本特有)
  // showCount: boolean;
  //步长（数字框特有）
  step?: number;
  //前缀（input特有）
  addonBefore?: string;
  //后缀（input特有）
  addonAfter?: string;
  //前缀图标（input特有）
  prefix?: string;
  //后缀图标（input特有）
  suffix?: string;

  //分割线文字位置 （分割线特有）
  orientation?: string;

  //tab页签位置（tab特有）
  tabPosition?: string;
  //tab大小 （tab独有）
  tabSize: string;

  //card布局特有
  title?: string;

  //时间组件特有
  format?: string;
  showTime?: boolean;
  //编码规则（编码组件特有）
  autoCodeRule: string;

  //上间距（分割线特有）
  marginTop: number;
  //下间距（分割线特有）
  marginBottom: number;

  //开始占位（时间范围组件特有）
  startTimePlaceholder: string;

  //结束占位（时间范围组件特有）
  endTimePlaceholder: string;

  //按钮高度（按钮组件特有）
  buttonHeight: string;

  //按钮宽度（按钮组件特有）
  buttonWidth: string;

  //选项卡风格（选项卡组件特有）
  type: string;

  //信息体类型（信息体组件特有）
  infoType: number;

  //预加载数据源类型（表格组件特有）
  preloadType: string;

  //是否选择按钮选数据（表格组件特有）
  useSelectButton: boolean;

  //选数据按钮名称（表格组件特有）
  buttonName: string;
  span?: string | number;
  maxlength?: string | number;
  controls?: boolean;
  subTotal?: boolean;

  //预加载数据绑定字段（表格组件特有）
  prestrainField?: string;

  //单行文本不存表
  isSave?: boolean;
  //是否显示
  isShow?: boolean;
  url?: string;
  //手写签名关联组件
  associateComponents?: string[];
  //子表列表查看
  isListView?: boolean;
  viewList?: any[];
}

/**
 * 栅格布局  实体模型
 */
export interface LayoutOptionModel {
  name?: string;
  span: number;
  list: ComponentOptionModel[];
}

/**
 * 前端代码模型
 */
export interface FrontCodeModel {
  listCode: string; // 列表代码
  formCode: string; //表单代码
  apiCode: string; //请求代码
  modelCode?: string; //模型代码
  configJsonCode: string; //配置json模型代码
  workflowPermissionCode: string; //配置工作流权限数据
  simpleFormCode: string; //simpleForm页面
}

/**
 * 展示代码模型
 */
export interface ShowFrontCodeModel extends FrontCodeModel {
  controllerCode?: string; //控制器代码
  entityCode?: object; //实体类代码
}

//表单尺寸
export enum FormSizeEnum {
  FULL,
  BIG,
  MEDIUM,
  SMALL,
}

export enum AlignTypeEnum {
  LEFT,
  RIGHT,
  CENTER,
}
