import { BasicFetchResult } from '/@/api/model/baseModel';

export interface PrintBasicData {
  id?: string;
  code: string; //编码
  name: string; //名称
  category: string; //类别
  apiConfig: string; //api
  isMenu: number; //菜单
  sortCode: number; //排序
  parentId: string; //上级
  icon: string; //图标
  remark: string; //描述
  enabledMark?: number; //启用1 禁用0
  content: string; // 设计器Json string
}
export type PageResult = BasicFetchResult<PrintBasicData>;
export interface SearchParams {
  keyword: string;
}
export interface PrintConfigData {
  apiConfig: string;
  buttonCode: string;
  buttonId: string;
  menuId: string;
  schemaId: string;
}
export interface PrintButtonRowItem {
  code: string;
  id: string;
  name: string;
  menuId: string;
}
