// v-debounce.ts
import { DirectiveBinding } from 'vue';
import { debounce } from 'lodash-es';

export default {
  beforeMount(el: HTMLElement, binding: DirectiveBinding) {
    if (typeof binding.value !== 'function') {
      throw new Error('Directive value must be a function');
    }
    const delay = 300; // 默认延迟300ms
    el['onEvent'] = debounce(binding.value, delay);
  },
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    el.addEventListener(binding.arg || 'click', el['onEvent'] as EventListener);
  },
  unmounted(el: HTMLElement, binding: DirectiveBinding) {
    el.removeEventListener(binding.arg || 'click', el['onEvent'] as EventListener);
  },
};
