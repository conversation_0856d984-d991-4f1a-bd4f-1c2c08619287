import { App } from 'vue';
import VueGridLayout from 'vue-grid-layout';

export function setupDesktop(app: App) {
  app.use(VueGridLayout);

  const requireComponent = import.meta.glob('../../views/desktop/components/*/*.vue', {
    eager: true,
  });

  Object.keys(requireComponent).forEach((key) => {
    if (!requireComponent[key].default.name) return;

    if (requireComponent[key].default.__file.includes('config')) {
      app.component(
        requireComponent[key].default.name.toLowerCase() + '-config',
        requireComponent[key].default,
      );
    } else {
      app.component(
        requireComponent[key].default.name.toLowerCase() + '-view',
        requireComponent[key].default,
      );
    }
  });

  console.log(app);
}
