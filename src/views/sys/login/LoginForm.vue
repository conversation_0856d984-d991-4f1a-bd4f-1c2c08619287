<template>
  <Form
    class="p-4 enter-x form-box"
    :model="formData"
    :rules="getFormRules"
    ref="formRef"
    v-show="getShow"
    @keypress.enter="handleLogin"
  >
    <div>
      <FormItem name="account" class="enter-x">
        <label class="form-title"> {{ t('账号') }}</label>
        <Input
          size="large"
          v-model:value="formData.account"
          :placeholder="t('账号')"
          class="fix-auto-fill"
          style="height: 58px"
          ><template #prefix>
            <IconFontSymbol icon="yonghu-xianxing" class="user-icon" />
          </template>
        </Input>
      </FormItem>
      <FormItem name="password" class="enter-x">
        <label class="form-title"> {{ t('密码') }}</label>
        <InputPassword
          size="large"
          visibilityToggle
          v-model:value="formData.password"
          :placeholder="t('密码')"
          style="height: 58px"
          ><template #prefix>
            <IconFontSymbol icon="mima" class="user-icon" />
          </template>
        </InputPassword>
      </FormItem>

      <ARow class="enter-x">
        <ACol :span="12">
          <FormItem>
            <!-- No logic, you need to deal with it yourself -->
            <Checkbox v-model:checked="rememberMe" size="small" class="f-16">
              {{ t('记住我') }}
            </Checkbox>
          </FormItem>
        </ACol>
        <!-- <ACol :span="12">
        <FormItem :style="{ 'text-align': 'right' }">
           No logic, you need to deal with it yourself 
          <Button type="link" size="small" @click="setLoginState(LoginStateEnum.RESET_PASSWORD)">
            {{ t('忘记密码？') }}
          </Button>
        </FormItem>
      </ACol> -->
      </ARow>

      <FormItem class="enter-x">
        <Button
          type="primary"
          class="sub-button"
          block
          @click="handleLogin"
          :loading="loading"
          :style="{ 'border-radius': '35px' }"
        >
          {{ t('登录') }}
        </Button>
        <!-- <Button size="large" class="mt-4 enter-x" block @click="handleRegister">
        {{ t('注册') }}
      </Button> -->
      </FormItem>
      <!-- <ARow class="enter-x">
      <ACol :md="8" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.MOBILE)">
          {{ t('手机登录') }}
        </Button>
      </ACol>
      <ACol :md="8" :xs="24" class="!my-2 !md:my-0 xs:mx-0 md:mx-2">
        <Button block @click="setLoginState(LoginStateEnum.QR_CODE)">
          {{ t('二维码登录') }}
        </Button>
      </ACol>
      <ACol :md="6" :xs="24">
        <Button block @click="setLoginState(LoginStateEnum.REGISTER)">
          {{ t('注册') }}
        </Button>
      </ACol>
    </ARow> -->

      <!-- <a-divider class="enter-x">{{ t('其他登录方式') }}</a-divider> -->
    </div>
    <!-- <div style="height: 1000px; width: 800px" v-else>
      <iframe id="iframeId" style="height: 100%; width: 100%" :src="authorizeUrl"></iframe>
    </div> -->

    <!-- <div class="flex justify-evenly enter-x" :class="`${prefixCls}-sign-in-way`">
      <WechatFilled @click="oauthLogin('wechat_enterprise')" />
      <DingtalkCircleFilled @click="oauthLogin('dingtalk')" />
    </div> -->
  </Form>
</template>
<script lang="ts" setup>
  import { reactive, ref, unref, computed, onMounted } from 'vue';

  import { Checkbox, Form, Input, Row, Col, Button } from 'ant-design-vue';
  // import { WechatFilled, DingtalkCircleFilled } from '@ant-design/icons-vue';
  // import LoginFormTitle from './LoginFormTitle.vue';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  import { useUserStore } from '/@/store/modules/user';
  import { LoginStateEnum, useLoginState, useFormRules, useFormValid } from './useLogin';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { Base64 } from 'js-base64';
  //import { onKeyStroke } from '@vueuse/core';
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { getOauthAuthorizeUrl } from '/@/api/system/login';
  import { useRouter } from 'vue-router';
  import { AesEncryption, encryptByBase64 } from '/@/utils/cipher';
  // import { nextTick } from 'vue';

  const ACol = Col;
  const ARow = Row;
  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const { t } = useI18n();
  const { notification, createErrorModal } = useMessage();
  const { prefixCls } = useDesign('login');
  const userStore = useUserStore();

  const { currentRoute } = useRouter();

  const { getLoginState } = useLoginState();
  const { getFormRules } = useFormRules();

  const formRef = ref();
  // const iframeRef = ref();
  const loading = ref(false);
  const rememberMe = ref(false);

  const showFlag = ref(false);
  const authorizeUrl = ref('');

  const formData = reactive({
    account: 'admin',
    password: '123456',
  });

  onMounted(async () => {
    //如果是第三方登录跳转回来 会携带token
    if (currentRoute.value.query.token) {
      try {
        loading.value = true;
        const userInfo = await userStore.oauthLogin({
          token: currentRoute.value.query.token as string,
          mode: 'none', //不要默认的错误提示
        });
        if (userInfo) {
          notification.success({
            message: t('登录成功'),
            description: `${t('欢迎回来')}: ${userInfo.name}`,
            duration: 3,
          });
        }
      } catch (error) {
        createErrorModal({
          title: t('错误提示'),
          content:
            (error as unknown as Error).message || t('网络异常，请检查您的网络连接是否正常!'),
          getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
        });
      } finally {
        loading.value = false;
      }
    }
    //如果第三方登录 登录错误 会携带错误信息
    if (currentRoute.value.query.error) {
      createErrorModal({
        title: t('错误提示'),
        content: t(currentRoute.value.query.error as string),
        getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
      });
    }
    const loginInfo = window.localStorage.getItem('USER__LOGIN__INFO__');
    if (loginInfo) {
      formData.account = Base64.decode(JSON.parse(loginInfo).account);
      formData.password = Base64.decode(JSON.parse(loginInfo).password);
    }
  });

  const { validForm } = useFormValid(formRef);

  //onKeyStroke('Enter', handleLogin);

  const getShow = computed(() => unref(getLoginState) === LoginStateEnum.LOGIN);

  async function handleLogin() {
    const data = await validForm();

    //对称加密
    const encryption = new AesEncryption({
      key: '****************', //MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAILpJXAOe3EwQfZkbIaUsCCa6J8dqTpEbDpHlB8Yu8N9aMhgjoMcbhMp4cVF9nb3inJIWOmYj4y6Tj4XYCVMd3cCAwEAAQ==
    });

    //非对称加密
    //rsa_encrypt(data.password)

    if (!data) return;
    try {
      loading.value = true;
      const userInfo = await userStore.login({
        password: encryptByBase64(encryption.encryptByAES(data.password)),
        userName: data.account,
        deviceType: 0, //pc-0,app-1
        mode: 'none', //不要默认的错误提示
      });
      if (userInfo) {
        notification.success({
          message: t('登录成功'),
          description: `${t('欢迎回来')}: ${userInfo.name}`,
          duration: 3,
        });
        if (rememberMe.value) {
          const info = {
            account: Base64.encode(data.account),
            password: Base64.encode(data.password),
          };
          window.localStorage.setItem('USER__LOGIN__INFO__', JSON.stringify(info));
        } else {
          window.localStorage.removeItem('USER__LOGIN__INFO__');
        }
      }
    } catch (error) {
      createErrorModal({
        title: t('错误提示'),
        content: (error as unknown as Error).message || t('网络异常，请检查您的网络连接是否正常!'),
        getContainer: () => document.body.querySelector(`.${prefixCls}`) || document.body,
      });
    } finally {
      loading.value = false;
    }
  }

  async function oauthLogin(source: string) {
    showFlag.value = true;
    authorizeUrl.value = await getOauthAuthorizeUrl(source);
    window.location.href = authorizeUrl.value;
    // let iframe = document.getElementById('iframeId');
    // console.log(iframe, 'sssssssssss');
    // var MutationObserver = window.MutationObserver;
    // // 创建一个观察器实例
    // var observer = new MutationObserver((item) => {
    //   iframeChange(item);
    // });
    // // 观察器的配置
    // var options = {
    //   childList: true, // 子节点的变动（指新增，删除或者更改） Boolean
    //   attributes: true, // 属性的变动      Boolean
    //   subtree: true, //表示是否将该观察器应用于该节点的所有后代节点      Boolean
    //   attributeOldValue: true, // 表示观察attributes变动时，是否需要记录变动前的属性 Boolean
    //   characterData: true, // 节点内容或节点文本的变动 Boolean
    //   // attributeFilter: ['src'], // 表示需要观察的特定属性 Array，如['class','src', 'style']
    // };

    // // console.log('formRef', iframe.contentWindow.document);

    // // 开始观察目标节点
    // function iframeChange(item) {
    //   console.log(item, '节点发生变化');
    // }
    // observer.observe(document, options);
  }
</script>
<style lang="less" scoped>
  .form-box {
    font-size: 16px;
  }

  .f-16 {
    font-size: 16px;
  }

  :deep(.ant-checkbox-inner) {
    border-color: #ced5f2;
    width: 18px;
    height: 18px;
    border-radius: 0;
  }

  :deep(.ant-form-item input[type='checkbox']) {
    width: 18px;
    height: 18px;
  }

  .form-title {
    line-height: 40px;
  }

  .sub-button {
    height: 60px;
    font-size: 20px;
  }

  .ant-form label {
    font-size: 16px;
  }

  .user-icon {
    font-size: 26px;
    margin-right: 8px;
    color: #707c92;
  }

  .ant-input-affix-wrapper {
    border-color: #ced5f2;
    border-style: none none solid;
  }

  :deep(.ant-input-password-icon) {
    font-size: 20px;
    color: #707c92;
    margin-right: 8px;
  }

  :deep(.ant-input-affix-wrapper-lg) {
    padding-left: 0;
  }
</style>
