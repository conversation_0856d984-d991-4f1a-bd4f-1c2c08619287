<template>
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('功能配置')">
        <a-button type="primary" @click="add">{{ t('添加') }}</a-button>
        <div class="card-box" :key="data.renderKey">
          <div class="card-item" v-for="(item, index) in data.info.config.functions" :key="index">
            <a-form-item :label="t('绑定功能')" :colon="false" labelAlign="left">
              <JumpMenu
                v-model:value="item.id"
                @name="
                  (val) => {
                    item.name = val;
                  }
                "
                @icon="
                  (val) => {
                    item.icon = val;
                  }
                "
                @path="
                  (val) => {
                    item.path = val;
                  }
                "
              />
            </a-form-item>
            <a-form-item :label="t('绑定颜色')" :colon="false" labelAlign="left">
              <SelectColor v-model:value="item.color" />
            </a-form-item>
            <div class="close-icon"
              ><Icon
                icon="ant-design:close-circle-outlined"
                color="#ff8080"
                :size="20"
                @click="deleteFunction(index)"
            /></div>
          </div>
        </div>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import Icon from '/@/components/Icon/index';
  import { JumpMenu } from '/@/components/MenuSelect';
  import { modulesInfo } from '../config/info';
  import SelectColor from './SelectColor.vue';
  import Location from './collapse/Location.vue';
  import { ModulesInfo } from '/@/model/desktop/designer';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: ModulesInfo;
    }>(),
    {
      info: () => {
        return modulesInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: ModulesInfo;
    renderKey: number;
  } = reactive({
    show: false,
    info: modulesInfo,
    renderKey: 0,
  });
  const activeKey = ref(['1', '2']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function add() {
    data.info.config.functions.push({
      id: '',
      name: '',
      icon: '',
      path: '',
      color: '#5e95ff',
    });
  }
  function deleteFunction(index: number) {
    data.info.config.functions.splice(index, 1);
    data.renderKey++;
  }
</script>

<style lang="less" scoped>
  .card-box {
    .card-item {
      position: relative;
      margin: 10px 0;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: -10px;
      right: -6px;
    }
  }
</style>
