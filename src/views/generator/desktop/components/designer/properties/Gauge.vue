<template>
  <!-- 仪表盘 -->
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" @change="changeTitle" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :label="t('数据视图')" :colon="false" labelAlign="left">
          <ApiSelect
            v-model:value="data.info.config.apiConfig"
            :exampleStr="exampleStr"
            @save="saveApiConfig"
          />
        </a-form-item>
        <a-form-item :label="t('指标')" :colon="false" labelAlign="left">
          <BindApiColumns
            v-model:value="data.info.config.valueKey"
            :apiColumns="data.info.config.apiColumns"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('大小定位')">
        <Location v-model:info="data.info" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { chartBarInfo } from '../config/info';
  import { ChartBarInfo } from '/@/model/desktop/designer';
  import ApiSelect from './ApiSelect.vue';
  import BindApiColumns from './collapse/BindApiColumns.vue';
  import Location from './collapse/Location.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: ChartBarInfo;
    }>(),
    {
      info: () => {
        return chartBarInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val: ChartBarInfo) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: any;
  } = reactive({
    show: true,
    info: chartBarInfo,
  });
  const activeKey = ref(['1', '2', '3']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function saveApiConfig() {
    resetApiData();
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.apiData = {};
    data.info.config.apiColumns = [];
    data.info.config.valueKey = '';
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
  function changeTitle() {
    resetDisplay();
  }
  const exampleStr = ` {
    code: 0,
    msg: 'success',
    data: {
      columns: [
        {prop:'name',label:'测试1'},
        {prop:'value',label:'测试2'}
      ],
      list: [
        {name:'demo1',value:10}
      ],
    },
  }`;
</script>

<style lang="less" scoped></style>
