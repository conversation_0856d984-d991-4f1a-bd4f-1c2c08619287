<template>
  <div class="box" v-if="data.show">
    <div class="item-title">
      {{ props.title }}
      <div class="fixed">
        <div
          class="fixed-item"
          v-for="(item, index) in props.config.dataList"
          :key="index"
          :class="index === data.itemIndex ? 'active' : ''"
          @click="showIndex(index)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <div
      class="echarts-tag-box"
      v-if="props.config && props.config.count && props.config.count.show"
    >
      <div class="echarts-tag-content">
        <div class="echarts-tag-num">
          {{ props.config.dataList[data.itemIndex].total }}
          <span class="echarts-tag-unit">{{ props.config.count.unit }}</span>
        </div>
        <div class="echarts-tag-label">{{ props.config.count.title }}</div>
      </div>
    </div>
    <LineBar
      v-if="data.show && props.config.dataList[data.itemIndex]"
      :echarts="props.config.echarts[data.itemIndex]"
      :config="props.config"
      class="box-item"
    />
  </div>
</template>
<script lang="ts" setup>
  import { DesktopComponent } from '/@/enums/desktop';
  import LineBar from './LineBar.vue';
  import { computed, onMounted, reactive, watch } from 'vue';
  import { chartLineProperties } from '../config/properties';
  import { MixLineBarConfig } from '/@/model/desktop/designer';
  import useApiRequest from '/@/hooks/event/useApiRequest';
  const { changeApiOptions } = useApiRequest();
  const props = withDefaults(
    defineProps<{
      type: DesktopComponent;
      w: number;
      h: number;
      title: string;
      config: MixLineBarConfig;
    }>(),
    {
      type: DesktopComponent.DEFAULT,
      w: 0,
      h: 0,
      title: '',
      config: () => {
        return chartLineProperties;
      },
    },
  );
  const emit = defineEmits(['update:config']);
  const data = reactive({ show: false, itemIndex: -1 });
  onMounted(async () => {
    if (Array.isArray(props.config.echarts)) {
      await changeData(true);
      data.show = true;
      data.itemIndex = 0;
      setTimeout(() => {
        resizeChart();
      }, 1);
    }
  });
  watch(
    () => props.w,
    (val) => {
      val && resizeChart();
    },
    {
      deep: true,
    },
  );
  watch(
    () => props.h,
    (val) => {
      val && resizeChart();
    },
    {
      deep: true,
    },
  );
  watch(
    () => props.config.renderKey,
    (val) => {
      val && resizeLayout();
    },
    {
      deep: true,
    },
  );
  let fontcolor = computed(() => {
    return props.config && props.config.condition && props.config.condition.color
      ? props.config.condition.color
      : '#595959';
  });
  let selectedcolor = computed(() => {
    return props.config && props.config.condition && props.config.condition.selected
      ? props.config.condition.selected
      : '#3399ff';
  });
  let boxheight = computed(() => {
    resizeChart();
    return props.config && props.config.count
      ? props.config.count.show
        ? 'calc(100% - 86px)'
        : '100%'
      : '100%';
  });
  async function resizeLayout() {
    await changeData(false);
    setTimeout(() => {
      resizeChart();
    }, 1);
  }
  async function changeData(isFirst) {
    let config = props.config;
    if (config.dataList.length > 0) {
      for await (const element of config.dataList) {
        if (element.apiData.length == 0 || isFirst) {
          if (element.apiConfig.path) {
            let res = await changeApiOptions(element.apiConfig);
            if (res.list && Array.isArray(res.list)) {
              element.apiData = res.list;
            }
            if (res.columns && Array.isArray(res.columns)) {
              element.apiColumns = res.columns;
            }
            if (res.total) {
              element.total = res.total;
            }
          }
        }
      }
      if (isFirst) {
        firstInit(config);
      }
      emit('update:config', config);
    }
  }
  function firstInit(config) {
    config.dataList.forEach((element, index) => {
      element.indicator.forEach((item, itemIndex) => {
        let valueData = element.apiData.map((ele) => {
          return item.value ? ele[item.value] : 0;
        });
        config.echarts[index].series[itemIndex].data = valueData;
        config.echarts[index].series[itemIndex].showAreaStyle = item.showAreaStyle;
        config.echarts[index].series[itemIndex].gradualEndColor = item.gradualEndColor;
        config.echarts[index].series[itemIndex].gradualStartColor = item.gradualStartColor;
        config.echarts[index].series[itemIndex].name = item.name;
        config.echarts[index].series[itemIndex].type = item.type;
      });
    });
  }
  function resizeChart() {
    data.show = false;
    setTimeout(() => {
      data.show = true;
    }, 1);
  }
  function showIndex(index: any) {
    data.itemIndex = index;
  }
</script>
<style lang="less" scoped>
  .box {
    width: 100%;
    height: 100%;
    padding-top: 40px;

    .box-item {
      width: 100%;
      height: v-bind(boxheight);
    }

    .item-title {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      color: #262626;
      font-size: 14px;
      padding-left: 16px;

      .fixed {
        position: absolute;
        top: 5px;
        right: 6px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        display: flex;
        align-items: center;

        .fixed-item {
          padding: 0 20px;
          cursor: pointer;
          margin: 0 4px;
          color: v-bind(fontcolor);
        }

        .fixed-item:hover {
          // color: #262626;
          color: v-bind(fontcolor);
          background-color: #f5f5f5;
        }

        .active {
          color: #fff;
          // background-color: @primary-color;
          background-color: v-bind(selectedcolor);
          border-radius: 4px;
        }

        .active:hover {
          color: #fff;
          // background-color: @primary-color;
          background-color: v-bind(selectedcolor);
          border-radius: 4px;
        }
      }
    }

    .item-title::after {
      content: '';
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #f0f0f0;
    }

    .item {
      height: 100%;
      width: 100%;
      -webkit-tap-highlight-color: transparent;
      user-select: none;
      position: relative;
    }
  }

  .echarts-tag-box {
    // position: absolute;
    height: 86px;
    padding: 20px 24px;
    width: 100%;
    top: 0;
    left: 0;

    .echarts-tag-content {
      position: relative;
      height: 100%;
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px 1px rgb(0 0 0 / 16%);
      display: flex;
      justify-content: space-between;
      padding: 0 16px;
      align-items: center;
    }

    .echarts-tag-num {
      font-size: 30px;
      color: #5e95ff;
    }

    .echarts-tag-label {
      font-size: 18px;
      color: #707070;
    }
  }
</style>
