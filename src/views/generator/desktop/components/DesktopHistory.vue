<template>
  <div>
    <a-table
      :dataSource="data.dataSource"
      rowKey="id"
      @change="pageChange"
      :columns="columns"
      :pagination="data.pagination"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'activityFlag'">
          <a-switch
            v-model:checked="record.activityFlag"
            :disabled="true"
            :checkedValue="1"
            :unCheckedValue="0"
          />
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <span @click="preview(record.id)" class="span-title">{{ t('预览') }}</span>
        </template>
      </template>
    </a-table>
    <DesktopPreview
      v-if="data.previewVisible"
      :list="data.list"
      @close="data.previewVisible = false"
    />
    <LoadingBox v-if="data.spinning" />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import DesktopPreview from './DesktopPreview.vue';
  import { LoadingBox } from '/@/components/ModalPanel/index';
  import { getHistoryInfo, getHistoryPageList } from '/@/api/desktop';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['close']);
  const columns = [
    {
      title: t('序号'),
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
      key: 'createUserName',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      key: 'createDate',
    },
    {
      title: t('当前版本'),
      dataIndex: 'activityFlag',
    },
    {
      title: t('操作'),
      dataIndex: 'operation',
      width: 80,
      align: 'center',
    },
  ];
  let props = withDefaults(
    defineProps<{
      id: string;
    }>(),
    {
      id: '',
    },
  );
  onMounted(() => {
    getList();
  });
  const data = reactive({
    dataSource: [],
    pagination: { current: 1, total: 0, pageSize: 20 },
    previewVisible: false,
    list: [],
    spinning: false,
  });
  async function getList() {
    data.spinning = true;
    try {
      let params = {
        limit: data.pagination.current,
        size: data.pagination.pageSize,
        schemaId: props.id,
      };
      let res = await getHistoryPageList(params);
      data.dataSource = res.list;
      data.pagination.total = res.total;
      data.spinning = false;
    } catch (error) {
      data.spinning = false;
    }
  }
  function pageChange(pagination) {
    data.pagination.current = pagination.current;
    data.dataSource = [];
    getList();
  }
  async function preview(id: string) {
    data.spinning = true;
    try {
      let { jsonContent } = await getHistoryInfo(id);
      if (jsonContent) {
        data.list = JSON.parse(jsonContent);
        data.previewVisible = true;
      }
      data.spinning = false;
    } catch (error) {
      data.spinning = false;
    }
  }
</script>

<style lang="less" scoped>
  .span-title {
    display: block;
    cursor: pointer;
    color: @primary-color;
  }
</style>
