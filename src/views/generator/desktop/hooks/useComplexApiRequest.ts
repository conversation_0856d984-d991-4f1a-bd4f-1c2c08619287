import { httpRequest } from '/@/api/sys/api';
import { getToken } from '/@/utils/auth';
import { useUserStore } from '/@/store/modules/user';
import { MessageSend } from '/@/api/workflow/task';

const userStore = useUserStore();
export default function () {
  async function changeApiOptions(val) {
    // console.log('val: ', val);
    if (val.path) {
      try {
        const headers = { Authorization: `Bearer ${getToken()}` };
        if (val.requestHeaderConfigs.length > 0) {
          val.requestHeaderConfigs.forEach((element) => {
            if (element.name) headers[element.name] = getValue(element);
          });
        }
        // console.log('headers: ', headers);
        let path = val.path;
        if (val.requestParamsConfigs.length > 0) {
          path += '?';
          val.requestParamsConfigs.forEach((element) => {
            if (element.name) path += `${element.name}=${getValue(element)}&`;
          });
        }
        // console.log('path: ', path);
        const apiData = {};
        if (val.requestBodyConfigs.length > 0) {
          val.requestBodyConfigs.forEach((element) => {
            if (element.name) apiData[element.name] = getValue(element);
          });
        }
        // console.log('apiData: ', apiData);
        const res = await httpRequest(
          {
            requestUrl: path,
            requestType: val.method,
          },
          {
            headers,
            data: apiData,
          },
        );

        return res;
      } catch (error) {
        console.log('error: ', error);
      }
    }
  }
  function getValue(element) {
    if (element.assignmentType == 'formData') {
      // 当前人信息
      if (element.value.includes('currentInfo----')) {
        const type = element.value.replace('currentInfo----', '');
        if (type == 'name') {
          return userStore.getUserInfo.name;
        } else if (type == 'id') {
          return userStore.getUserInfo.id;
        } else if (type == 'code') {
          return userStore.getUserInfo.code;
        } else if (type == 'mobile') {
          return userStore.getUserInfo.mobile;
        } else if (type == 'departmentName') {
          return userStore.getUserInfo.departmentName;
        } else if (type == 'departmentId') {
          return userStore.getUserInfo.departmentId;
        } else if (type == 'postId') {
          return userStore.getUserInfo.postId;
        } else if (type == 'roles.id') {
          return userStore.getUserInfo.roles[0].id;
        }
      } else {
        // 获取当前选择数据行数据
        const tableRecord = window.localStorage.getItem('TableRecord');
        if (tableRecord) {
          const list = JSON.parse(tableRecord);
          //  src\views\generator\desktop\components\designer\properties\collapse\complex\InputParams.vue 用___做的分隔符
          const temp = element.value.split('___');
          if (temp.length > 1 && list[temp[0]]) {
            return list[temp[0]][temp[1]];
          }
        }
        return '';
      }
    }
    return element.value;
  }
  async function pushMessageFunc(messageConfig) {
    const bodyParam = { ...messageConfig };
    if (messageConfig.configs) {
      bodyParam['config'] = [];
      messageConfig.configs.forEach((element) => {
        const value = element.value;
        bodyParam['config'].push({
          assignmentType: messageConfig.type,
          config: '',
          description: element.description,
          name: element.name,
          value: value,
        });
      });
      delete bodyParam.configs;
    }
    try {
      const res = await MessageSend(bodyParam);
      return res;
    } catch (error) {
      return false;
    }
  }
  return {
    changeApiOptions,
    pushMessageFunc,
  };
}
