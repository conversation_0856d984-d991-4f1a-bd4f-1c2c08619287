<template>
  <div id="store">
    <div class="comPage_Box">
      <div class="filterForm_box">
        <a-select
          v-model:value="searchForm.headAccountId"
          style="width: 240px"
          placeholder="连锁总部"
          :options="headOptions"
          :field-names="{ label: 'name', value: 'accountId' }"
          :filter-option="headFilterOption"
          show-search
          allowClear
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.id"
          placeholder="门店ID"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.shopName"
          placeholder="门店名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.partName"
          placeholder="连锁分部名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.headName"
          placeholder="连锁总部名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.ownerNameOrNumber"
          placeholder="责任人/工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-select
          v-model:value="searchForm.isDirectJoin"
          style="width: 240px"
          placeholder="直营/加盟"
          :options="directJoinOptions"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.customerTagId"
          style="width: 240px"
          placeholder="门店标签"
          :options="customerTagList"
          :field-names="{ label: 'tagName', value: 'id' }"
          :filter-option="tagFilterOption"
          show-search
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isDtp"
          style="width: 240px"
          placeholder="是否DTP门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isWhole"
          style="width: 240px"
          placeholder="是否统筹门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.isOnline"
          style="width: 240px"
          placeholder="是否电商门店"
          :options="options"
          allowClear
          @change="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-dropdown :disabled="!selectedKeys.length">
          <template #overlay>
            <a-menu @click="handleMoreBtnClick">
              <!-- <a-menu-item key="1">分配责任人</a-menu-item> -->
              <a-menu-item key="2">批量导出</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          :isSelection="true"
          rowKey="id"
          :checkedKeys="selectedKeys"
          @onSelectChange="onSelectChange"
        >
          <template #isDirectJoin="{ record }">
            <span>{{ record.isDirectJoin ? '加盟' : '直营' }}</span>
          </template>
          <template #isOr="{ record, column }">
            <span>{{ record[column.key] ? '是' : '否' }}</span>
          </template>
          <template #action="{ record }">
            <a-button type="link" @click.stop="onAllot([record.id])">分配责任人</a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="550"
    v-model:visible="openModalVisible"
    :title="'分配责任人'"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openModalVisible = false)"
  >
    <div class="modal_box">
      <p class="p_box">
        已选择
        <span :style="{ color: getThemeColor }">{{ allotList.length }}</span> 条数据，即将进行分配给
        <a-select
          style="width: 240px"
          placeholder="员工姓名/工号/手机号"
          v-model:value="allotPersonId"
          :filterOption="false"
          @search="getAllotPersonList"
          showSearch
          allowClear
          @clear="getAllotPersonList()"
        >
          <a-select-option
            v-for="(item, index) in allotPersonList"
            :key="index"
            :value="`${item.id}/${item.name}/${item.code}`"
          >
            {{ `${item.name ?? ''} / ${item.code ?? ''} / ${item.mobile ?? ''}` }}
          </a-select-option>
        </a-select></p
      >
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  const { getThemeColor } = useRootSetting();
  import {
    getAccountPage,
    setAccountRelation,
    getUserList,
    getHeadList,
    getTagList,
    exportData,
  } from '/@/api/customerManage/account';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  import { downloadByData } from '/@/utils/file/download';
  import dayjs from 'dayjs';
  const searchForm = reactive({
    id: '',
    shopName: '',
    partName: '',
    headName: '',
    ownerNameOrNumber: '',
    headAccountId: null,
    customerTagId: null,
    isDirectJoin: null,
    isDtp: null,
    isOnline: null,
    isWhole: null,
  });
  const options = ref([
    {
      value: 1,
      label: '是',
    },
    {
      value: 0,
      label: '否',
    },
  ]);
  const reSet = () => {
    searchForm.id = '';
    searchForm.shopName = '';
    searchForm.partName = '';
    searchForm.headName = '';
    searchForm.ownerNameOrNumber = '';
    searchForm.headAccountId = null;
    searchForm.customerTagId = null;
    searchForm.isDirectJoin = null;
    searchForm.isDtp = null;
    searchForm.isOnline = null;
    searchForm.isWhole = null;
    getList();
  };
  const tableColumns = [
    {
      title: '门店ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      fixed: 'left',
      width: 150,
    },
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      fixed: 'left',
      width: 200,
    },
    {
      title: '连锁分部名称',
      dataIndex: 'parentAccountIdName',
      key: 'parentAccountIdName',
      align: 'center',
      width: 200,
    },
    {
      title: '连锁总部名称',
      dataIndex: 'newOtcAccountName',
      key: 'newOtcAccountName',
      align: 'center',
      width: 200,
    },
    {
      title: '省',
      dataIndex: 'province',
      key: 'province',
      align: 'center',
      width: 80,
    },
    {
      title: '市',
      dataIndex: 'city',
      key: 'city',
      align: 'center',
      width: 80,
    },
    {
      title: '区/县',
      dataIndex: 'district',
      key: 'district',
      align: 'center',
      width: 100,
    },
    {
      title: '所属辖区',
      dataIndex: 'belongRegion',
      key: 'belongRegion',
      align: 'center',
      width: 250,
    },
    {
      title: '责任人',
      dataIndex: 'ownerName',
      key: 'ownerName',
      align: 'center',
      width: 100,
    },
    {
      title: '工号',
      dataIndex: 'ownerNumber',
      key: 'ownerNumber',
      align: 'center',
      width: 150,
    },
    {
      title: '直营/加盟',
      dataIndex: 'isDirectJoin',
      key: 'isDirectJoin',
      align: 'center',
      width: 80,
      isSlot: true,
    },
    {
      title: '门店类型',
      dataIndex: 'customerTypeCodeName',
      key: 'customerTypeCodeName',
      align: 'center',
      width: 100,
    },
    {
      title: '年销售规模(万)',
      dataIndex: 'saleScale',
      key: 'saleScale',
      align: 'center',
      width: 120,
    },
    {
      title: '门店标签',
      dataIndex: 'customerTagName',
      key: 'customerTagName',
      align: 'center',
      width: 90,
    },
    {
      title: '是否DTP药房',
      dataIndex: 'isDtp',
      key: 'isDtp',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
    {
      title: '是否统筹门店',
      dataIndex: 'isWhole',
      key: 'isWhole',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
    {
      title: '是否电商门店',
      dataIndex: 'isOnline',
      key: 'isOnline',
      align: 'center',
      width: 100,
      isSlot: true,
      slotName: 'isOr',
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   isSlot: true,
    //   align: 'center',
    //   fixed: 'right',
    //   width: 120,
    // },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  // 表格多选
  const selectedKeys = ref<any[]>([]);
  const onSelectChange = (keys: any[], rows: any[]) => {
    selectedKeys.value = keys;
    console.log('onSelectChange', keys, rows);
  };
  // 获取表格数据
  const getList = async (flag?: number) => {
    selectedKeys.value = [];
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        terminalType: 2,
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getAccountPage(temp);
      tableData.data = res?.list ?? [];
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  // 分配责任人
  const openModalVisible = ref<boolean>(false);
  const allotList = ref<any[]>([]); // 被分配list
  const allotPersonId = ref(null); // 分配的对象
  // 责任人
  type AllotPerson = {
    id: number;
    code?: string;
    name?: string;
    mobile?: string;
  };
  const allotPersonList = ref<AllotPerson[]>([]); // 分配的对象列表
  const getAllotPersonList = async (val?: string) => {
    try {
      let res = await getUserList({ keyword: val ?? '' });
      allotPersonList.value = res ?? [];
    } catch (error) {
      console.log(error);
    }
  };
  const modalLoading = ref<boolean>(false);
  const handleModalOk = async () => {
    modalLoading.value = true;
    try {
      let str = allotPersonId.value ?? '';
      let temp = {
        userId: str.split('/')[0],
        ownerName: str.split('/')[1],
        ownerNumber: str.split('/')[2],
        terminalIds: allotList.value,
      };
      let res = await setAccountRelation(temp);
      modalLoading.value = false;
      if (res) {
        notification.success({ message: '提示', description: '操作成功' });
        getList(1);
        openModalVisible.value = false;
      }
      selectedKeys.value = [];
      allotList.value = [];
    } catch (error) {
      console.log(error);
      modalLoading.value = false;
    }
  };
  const onAllot = (arr: any[]) => {
    allotList.value = arr;
    allotPersonId.value = null;
    getAllotPersonList();
    openModalVisible.value = true;
  };

  // 连锁总部列表
  const headOptions = ref([]);
  const getSearchHeadList = async () => {
    try {
      let res = await getHeadList();
      headOptions.value = res ?? [];
    } catch (error) {
      console.log(error);
    }
  };
  const headFilterOption = (input: string, option: any) => {
    return option.name.indexOf(input) >= 0;
  };

  // 门店标签列表
  const customerTagList = ref<any[]>([]);
  const getTagOptions = async () => {
    try {
      let res = await getTagList();
      customerTagList.value = res ?? [];
    } catch (error) {
      console.log(error);
    }
  };
  const tagFilterOption = (input: string, option: any) => {
    return option.tagName.indexOf(input) >= 0;
  };

  const directJoinOptions = ref([
    {
      value: 1,
      label: '加盟',
    },
    {
      value: 0,
      label: '直营',
    },
  ]);

  onMounted(() => {
    getSearchHeadList();
    getTagOptions();
    getList();
  });
  // 导出
  const onExport = async () => {
    let temp = {
      ids: selectedKeys.value,
      terminalType: 2,
    };
    let res = await exportData(temp);
    downloadByData(
      res.data,
      `门店管理-${dayjs().format('YYYY-MM-DD-HH-mm')}.xlsx`,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
  };
  const handleMoreBtnClick = (e) => {
    if (e.key === '1') {
      onAllot(selectedKeys.value);
    }
    if (e.key === '2') {
      onExport();
    }
  };
</script>

<style scoped lang="less">
  #store {
    width: 100%;
    height: 100%;
    padding: 8px;
    .comPage_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .detail_title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0;
        padding: 16px 0 0 16px;
      }
    }
    .filterForm_box {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .table_box {
    }
  }
  .modal_box {
    padding: 16px;
    .p_box {
      span {
        margin: 0 4px;
      }
      .ant-select {
        margin-left: 4px;
      }
    }
  }
</style>
