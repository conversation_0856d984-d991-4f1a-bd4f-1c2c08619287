<template>
  <div id="store">
    <div class="comPage_Box">
      <div class="filterForm_box">
        <a-input
            style="width: 240px"
            v-model:value.lazy="searchForm.customerName"
            placeholder="连锁总部"
            allowClear
            @change="getList()"
            @pressEnter="getList()"
        />
        <a-select
            v-model:value="searchForm.isTen"
            style="width: 240px"
            placeholder="是否十大连锁"
            :options="options"
            allowClear
            @change="getList()"
        />
<!--        <a-select-->
<!--            v-model:value="searchForm.provinceName"-->
<!--            style="width: 240px"-->
<!--            placeholder="省份"-->
<!--            :options="areaOptions"-->
<!--            :field-names="{ label: 'name', value: 'name' }"-->
<!--            :filter-option="headFilterOption"-->
<!--            show-search-->
<!--            allowClear-->
<!--            @change="getList()"-->
<!--        />-->
        <a-select
            v-model:value="searchForm.productGroup"
            style="width: 240px"
            placeholder="品种"
            :options="productGroupOptions"
            :field-names="{ label: 'newName', value: 'newName' }"
            :filter-option="productGroupFilterOption"
            show-search
            allowClear
            @change="getList()"
        />
        <a-select
            v-model:value="searchForm.product"
            style="width: 240px"
            placeholder="品规"
            :options="productList"
            :field-names="{ label: 'name', value: 'name' }"
            :filter-option="headFilterOption"
            show-search
            allowClear
            @change="getList()"
        />


        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
      </div>
      <div class="title">库存数量合计（盒）：<span>{{warehouseNumCount}}</span>  <span style="margin-right: 50px"></span>
        库存金额合计（万元）： <span>{{warehouseMoneyCount}}</span> </div>
      <div class="table_box">
        <c-table
            :tableColumns="tableColumns"
            :tableData="tableData.data"
            :loading="loading"
            :currentPage="pagination.currentPage"
            :totalItems="pagination.totalItems"
            :pageSize="pagination.pageSize"
            @update:current-page="(value) => (pagination.currentPage = value)"
            @pagination-change="handlePaginationChange"
            :isSelection="false"
            rowKey="id"
            :checkedKeys="selectedKeys"
            @onSelectChange="onSelectChange"
        >
          <template #isTen="{ record, column }">
            <span>{{ record[column.key] ? '是' : '否' }}</span>
          </template>
        </c-table>
      </div>
    </div>
  </div>

</template>

<script lang="ts" setup>
import cTable from '/@/views/components/Table/index.vue';
import {onMounted, reactive, ref} from 'vue';
import {
  areaList, getPageProduct,
  getHeadInventoryPage, getProductGroup, getHeadStaticCount
} from '/@/api/purchaseSaleStock/store';

const searchForm = reactive({
  customerName: '',
  isTen: undefined,
  customerProvincialName: undefined,
  productGroup: undefined,
  product: undefined,
});
const options = ref([
  {
    value: 1,
    label: '是',
  },
  {
    value: 0,
    label: '否',
  },
]);
const reSet = () => {
  searchForm.customerName = '';
  searchForm.isTen = '';
  searchForm.customerProvincialName = '';
  searchForm.productGroup = '';
  searchForm.product = '';
  getList();
};
const tableColumns = [
  {
    title: '连锁总部',
    dataIndex: 'customerName',
    key: 'customerName',
    align: 'center',
    fixed: 'left',
    width: 200,
  },
  {
    title: '负责人',
    dataIndex: 'personName',
    key: 'personName',
    align: 'center',
    fixed: 'left',
    width: 100,
  }, {
    title: '是否为十大连锁',
    dataIndex: 'isTen',
    key: 'isTen',
    align: 'center',
    fixed: 'left',
    width: 100,
    isSlot: true,
    slotName: 'isTen',
  },

  {
    title: 'EDP品规',
    dataIndex: 'product',
    key: 'product',
    align: 'center',
    width: 150,
  },
  {
    title: '品种类型',
    dataIndex: 'productGroup',
    key: 'productGroup',
    align: 'center',
    width: 150,
  },
  {
    title: '业绩考核单价(元)',
    dataIndex: 'examPrice',
    key: 'examPrice',
    align: 'center',
    width: 100,
  },
  {
    title: '库存数量（盒）',
    dataIndex: 'warehouseNum',
    key: 'warehouseNum',
    align: 'center',
    width: 80,
  },
  {
    title: '库存金额合计（万元）',
    dataIndex: 'warehouseMoney',
    key: 'warehouseMoney',
    align: 'center',
    width: 80,
    isSlot: true,
  },

];
const tableData = reactive({
  data: [],
});
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 500,
  pageSize: 10,
});
// 表格多选
const selectedKeys = ref<any[]>([]);
const onSelectChange = (keys: any[], rows: any[]) => {
  selectedKeys.value = keys;
  console.log('onSelectChange', keys, rows);
};
// 获取表格数据
const getList = async (flag?: number) => {
  selectedKeys.value = [];
  if (!flag) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
  }
  loading.value = true;
  tableData.data = [];
  try {
    let temp = {
      ...searchForm,
      limit: pagination.currentPage,
      size: pagination.pageSize,
    };
    let res = await getHeadInventoryPage(temp);
    tableData.data = res?.list ?? [];
    pagination.totalItems = res.total ?? 0;
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
  getCount()
};
// 处理分页
const handlePaginationChange = (page: any) => {
  pagination.currentPage = page.current;
  pagination.pageSize = page.pageSize;
  getList(1);
};


// 省列表
const areaOptions = ref([]);
const getSearchHeadList = async () => {
  try {
    let res = await areaList();
    areaOptions.value = res ?? [];
  } catch (error) {
    console.log(error);
  }
};
const headFilterOption = (input: string, option: any) => {
  return option.name.indexOf(input) >= 0;
};
const productGroupFilterOption = (input: string, option: any) => {
  return option.newName.indexOf(input) >= 0;
};
// 品规
const productList = ref<any[]>([]);
const getProductOptions = async () => {
  let temp = {
    limit: 1,
    size: 100,
  };
  try {
    let res = await getPageProduct(temp);
    productList.value = res.list ?? [];
  } catch (error) {
    console.log(error);
  }
};

// 品种
const productGroupOptions = ref([]);
const getProductGroupOptions = async () => {
  let temp = {
    limit: 1,
    size: 100,
  };
  try {
    let res = await getProductGroup(temp);
    productGroupOptions.value = res.list ?? [];
  } catch (error) {
    console.log(error);
  }
};
const warehouseNumCount=ref('')
const warehouseMoneyCount=ref('')
const getCount = async () => {
  let temp = {
    ...searchForm
  };
  try {
    let res = await getHeadStaticCount(temp);
    warehouseNumCount.value=res.warehouseNumCount
    warehouseMoneyCount.value=res.warehouseMoneyCount

  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  getSearchHeadList();
  getProductGroupOptions();
  getProductOptions();
  getCount();
  getList();
});

</script>

<style scoped lang="less">
#store {
  width: 100%;
  height: 100%;
  padding: 8px;

  .comPage_Box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    > div {
      width: 100%;
      padding: 16px;

      &:last-child {
        flex: 1;
        height: 0;
      }
    }

    .detail_title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
    }
  }

  .filterForm_box {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .title {
    font-weight: bolder;
    span{
      display: inline-block;
      font-size: 26px;
    }
  }
}


</style>
