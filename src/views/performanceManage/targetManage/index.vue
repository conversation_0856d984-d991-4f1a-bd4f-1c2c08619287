<template>
  <div class="targetManage">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-select
          v-model:value="searchForm.taskType"
          style="width: 150px"
          placeholder="目标类型"
          :options="typeOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-input
          style="width: 150px"
          pleaceholder="KPI名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-select
          style="width: 150px"
          placeholder="请选择周期"
          :options="frequencyOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-select
          v-model:value="searchForm.taskStatus"
          style="width: 150px"
          placeholder="目标状态"
          :options="statusOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-select style="width: 150px" placeholder="请选择任务状态">
          <a-select-option value="1">启用</a-select-option>
          <a-select-option value="2">禁用</a-select-option>
        </a-select>
        <a-range-picker v-model:value="searchForm.time" @change="getList()" />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reset">重置</a-button>
      </div>
      <div class="p_box">
        <a-button type="primary" @click="openModal()">新增目标</a-button>
        <a-button @click="downloadFile">下载</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #taskStatusName="{ record }">
            <div>
              <span
                class="status"
                :class="{
                  status_warning: record.taskStatus === '1',
                  status_success: record.taskStatus === '2',
                  status_gray: record.taskStatus === '3',
                }"
                >{{ record.taskStatusName }}</span
              >
            </div>
          </template>
          <template #targetNum="{ record }">
            {{ record.targetNum == 0 ? '--' : record.targetNum }}
          </template>
          <template #isEnabled="{ record }">
            <a-switch
              v-model:checked="record.isEnabled"
              checkedValue="1"
              unCheckedValue="0"
              :loading="record.switchloading"
              @change="changeSwitch(record)"
            />
          </template>
          <template #action="{ record }">
            <a-button type="link" @click.stop="openModal(record)">{{
              record.taskStatus == 3 ? '查看' : '编辑'
            }}</a-button>
            <a-button type="link" v-if="false" @click.stop="onDel(record)">删除</a-button>
            <a-button type="link" @click.stop="onDetail(record)">达成明细</a-button>
            <!-- <a-button type="primary" @click.stop=""
              >同步数据</a-button
            >
            <a-button  type="link" @click.stop=""
              >导出目标</a-button
            > -->
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="1700"
    v-model:visible="openModalVisible"
    v-if="openModalVisible"
    :title="modalInfo.taskStatus === '3' ? '查看目标' : modalInfo.id ? '编辑目标' : '新增目标'"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openModalVisible = false)"
  >
    <div class="modal_box">
      <a-form
        class="form_box"
        ref="formRef"
        :model="modalInfo"
        autocomplete="off"
        labelAlign="right"
        :colon="true"
      >
        <a-form-item label="目标类型" name="taskType" required>
          <a-select
            v-model:value="modalInfo.taskType"
            style="width: 220px"
            placeholder="请选择"
            :disabled="modalInfo.id"
            :options="typeOptions"
            :field-names="{ label: 'name', value: 'value' }"
            @change="changeTaskType"
          />
        </a-form-item>
        <a-form-item
          v-if="!['6'].includes(modalInfo.taskType as string)"
          label="目标周期"
          name="time"
          required
        >
          <a-range-picker
            style="width: 220px"
            v-model:value="modalInfo.time"
            picker="month"
            valueFormat="YYYY-MM"
            :allowClear="false"
            :disabled-date="disabledDate"
            :disabled="modalInfo.taskStatus === '2' || modalInfo.taskStatus === '3'"
          />
        </a-form-item>
        <a-form-item
          v-if="['6'].includes(modalInfo.taskType as string)"
          label="目标周期"
          name="time"
          required
        >
          <a-date-picker
            style="width: 220px"
            v-model:value="modalInfo.time"
            picker="year"
            valueFormat="YYYY"
            :disabled-date="disabledDate"
          />
        </a-form-item>
      </a-form>
      <div>
        <p>目标规则配置</p>
        <a-table :columns="ruleColumns" :data-source="ruleData" :pagination="false">
          <template #headerCell="{ column }">
            <template
              v-if="
                column.key === 'frequency' ||
                column.key === 'kpiName' ||
                column.key === 'dataRange' ||
                column.key === 'baseline'
              "
            >
              <div class="required">
                {{ column.title }}
              </div>
            </template>
          </template>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'frequency'">
              <a-select
                style="width: 100%"
                v-model:value="record.frequency"
                placeholder="请选择"
                :options="frequencyOptions"
                :field-names="{ label: 'name', value: 'value' }"
              >
              </a-select>
            </template>
            <template v-if="column.key === 'kpiName'">
              <a-input v-model:value="record.kpiName" placeholder="请输入KPI名称" />
            </template>
            <template v-if="column.key === 'dataRange'">
              <div style="display: flex; align-items: center; justify-content: center">
                <div>
                  <a-select
                    style="width: 100px"
                    v-model:value="record.dataRangeLeft"
                    placeholder="请选择"
                    :options="clientOptions"
                    :field-names="{ label: 'name', value: 'value' }"
                    :disabled="record.showRight"
                  />

                  <template v-if="record.dataRangeLeft === '1'">
                    <a-select
                      :options="customerOptions"
                      v-model:value="record.rangeId"
                      style="width: 100px; margin-left: 10px"
                      placeholder="请选择"
                      :field-names="{ label: 'name', value: 'value' }"
                    />
                  </template>
                  <template v-if="record.dataRangeLeft === '2'">
                    <a-select
                      v-model:value="record.rangeType"
                      style="width: 100px; margin-left: 10px"
                      placeholder="请选择"
                      @change="changeProduct"
                      mode="multiple"
                    >
                      <a-select-option :disabled="(record.rangeType?.includes('1') && item.value !== '1') || (record.rangeType?.includes('2') && item.value !== '2') || (record.rangeType?.some(v => v !== '1' && v !== '2') && ['1', '2'].includes(item.value))" :alt="item.name" v-for="item in productOptions" :key="item.value" :value="item.value">
                          {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </template>
<!--                  <a-select-->
<!--                    :options="record.dataRangeLeft === '1' ? customerOptions : productOptions"-->
<!--                    v-model:value="record.customerId"-->
<!--                    style="width: 100px; margin-left: 10px"-->
<!--                    placeholder="请选择"-->
<!--                    :field-names="{ label: 'name', value: 'id' }"-->
<!--                    @change="changeProduct"-->
<!--                    mode="multiple"-->
<!--                  />-->
                </div>
                <Icon
                  v-if="!record.showRight"
                  class="icon"
                  @click="addDataRange(record, index)"
                  icon="ant-design:plus-circle-outlined"
                  color="#ff9201"
                  :size="26"
                  style="cursor: pointer"
                />
                <div v-if="record.showRight" style="margin-left: 10px;display: flex;align-items: center">
                  <span>&</span>
                  <a-select
                    style="width: 100px; margin-left: 10px"
                    :defaultValue="record.dataRangeLeft === '2' ? '1' : '2'"
                    v-model:value="record.dataRangeRight"
                    placeholder="请选择"
                    :options="clientOptions"
                    :disabled="record.showRight"
                    :field-names="{ label: 'name', value: 'value' }"
                  >
                  </a-select>
                  <template v-if="record.dataRangeRight === '1'">
                    <a-select
                      :options="customerOptions"
                      v-model:value="record.rangeId"
                      style="width: 100px; margin-left: 10px"
                      placeholder="请选择"
                      :field-names="{ label: 'name', value: 'value' }"
                    />
                  </template>
                  <template v-if="record.dataRangeRight === '2'">
                    <a-select
                      v-model:value="record.rangeType"
                      style="width: 100px; margin-left: 10px"
                      placeholder="请选择"
                      mode="multiple"
                    >
                      <a-select-option :disabled="(record.rangeType?.includes('1') && item.value !== '1') || (record.rangeType?.includes('2') && item.value !== '2') || (record.rangeType?.some(v => v !== '1' && v !== '2') && ['1', '2'].includes(item.value))" :alt="item.name" v-for="item in productOptions" :key="item.value" :value="item.value">
                        {{ item.name }}
                      </a-select-option>
                    </a-select>
                  </template>
<!--                  <a-select-->
<!--                    :options="record.dataRangeLeft === 1 ? clientOptions : productOptions"-->
<!--                    v-model:value="record.customerId"-->
<!--                    style="width: 100px; margin-left: 15px"-->
<!--                    placeholder="请选择"-->
<!--                    :field-names="{ label: 'name', value: 'value' }"-->
<!--                  />-->
                  <Icon
                    class="icon"
                    @click="deleteDataRange(record, index)"
                    icon="ant-design:delete-outlined"
                    color="#ff9201"
                    :size="26"
                    style="cursor: pointer; margin-top: 5px"
                  />
                </div>
              </div>
            </template>
            <template v-if="column.key === 'baseline'">
              <a-input-number
                :min="1"
                v-model:value="record.baseline"
                style="width: 120px"
                placeholder="请输入基准值"
              />
              <a-select
                style="width: 100px; margin-left: 10px"
                placeholder="请选择"
                v-model:value="record.unit"
                :options="unitOptions"
                :field-names="{ label: 'unitName', value: 'unitType' }"
              >
              </a-select>
            </template>
            <template v-if="column.key === 'lower'">
              <a-input-number
                :min="1"
                v-model:value="record.lower"
                style="width: 120px"
                placeholder="请输入下限值"
              />
              <a-select
                style="width: 100px; margin-left: 10px"
                placeholder="请选择"
                v-model:value="record.unit"
                :options="unitOptions"
                :field-names="{ label: 'unitName', value: 'unitType' }"
              >
              </a-select>
            </template>
            <template v-if="column.key === 'weight'">
              <a-input-number
                :min="1"
                :max="100"
                v-model:value="record.weight"
                style="width: 110px"
                placeholder="请输入权重"
              />
              <span>%</span>
            </template>
            <template v-if="column.key === 'action'">
              <Icon
                v-if="ruleData.length === index + 2 || ruleData.length === 1"
                class="icon"
                @click="addRule()"
                icon="ant-design:plus-circle-outlined"
                color="#ff9201"
                :size="26"
                style="cursor: pointer"
              />
              <Icon
                v-if="ruleData.length === index + 1 && ruleData.length > 1"
                class="icon"
                @click="DeleteRule(record, index)"
                icon="ant-design:delete-outlined"
                color="#ff9201"
                :size="26"
                style="cursor: pointer"
              />
            </template>
          </template>
        </a-table>
      </div>
      <div>
        <p class="required">目标执行人</p>
        <p class="p_red"
          >支持按岗位、组织架构圈定目标执行人，二者取并集 ；新员工入职，依岗位、所属架构自动同步目标
          。</p
        >
        <div style="display: flex">
          <div class="form_box">
            <p>按岗位圈定</p>
            <div class="ant-checkbox-group">
              <a-checkbox-group v-model:value="modalInfo.postIdList">
                <a-checkbox :value="item.id" v-for="item in levelOptions" :key="item.id">{{
                  item.name
                }}</a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
          <div class="form_box">
            <p>按组织架构圈定</p>
            <a-tree
              checkable
              :tree-data="cascaderOptions"
              :fieldNames="{ key: 'id', title: 'name' }"
              v-model:checkedKeys="modalInfo.departIdList"
            />
          </div>
        </div>
      </div>
    </div>
    <!--    <div class="modal_box">-->
    <!--      <p>基础信息</p>-->
    <!--      <a-form-->
    <!--        class="form_box"-->
    <!--        ref="formRef"-->
    <!--        :model="modalInfo"-->
    <!--        :label-col="{ span: 4 }"-->
    <!--        :wrapper-col="{ span: 20 }"-->
    <!--        autocomplete="off"-->
    <!--        labelAlign="right"-->
    <!--        :colon="true"-->
    <!--      >-->
    <!--        <a-form-item label="目标类型" name="taskType" required>-->
    <!--          <a-select-->
    <!--            v-model:value="modalInfo.taskType"-->
    <!--            style="width: 220px"-->
    <!--            placeholder="请选择"-->
    <!--            :disabled="modalInfo.id"-->
    <!--            :options="typeOptions"-->
    <!--            :field-names="{ label: 'name', value: 'value' }"-->
    <!--            @change="changeTaskType"-->
    <!--          />-->
    <!--        </a-form-item>-->
    <!--        <a-form-item-->
    <!--          v-if="!['6'].includes(modalInfo.taskType as string)"-->
    <!--          label="目标周期"-->
    <!--          name="time"-->
    <!--          required-->
    <!--        >-->
    <!--          <a-range-picker-->
    <!--            style="width: 220px"-->
    <!--            v-model:value="modalInfo.time"-->
    <!--            picker="month"-->
    <!--            valueFormat="YYYY-MM"-->
    <!--            :allowClear="false"-->
    <!--            :disabled-date="disabledDate"-->
    <!--            :disabled="modalInfo.taskStatus === '2'||modalInfo.taskStatus === '3'"-->
    <!--          />-->
    <!--        </a-form-item>-->
    <!--        <a-form-item-->
    <!--          v-if="['6'].includes(modalInfo.taskType as string)"-->
    <!--          label="目标周期"-->
    <!--          name="time"-->
    <!--          required-->
    <!--        >-->
    <!--          <a-date-picker-->
    <!--            style="width: 220px"-->
    <!--            v-model:value="modalInfo.time"-->
    <!--            picker="year"-->
    <!--            valueFormat="YYYY"-->
    <!--            :disabled-date="disabledDate"-->
    <!--          />-->
    <!--        </a-form-item>-->
    <!--        <a-form-item label="核算频率" name="frequency" required>-->
    <!--          <a-select-->
    <!--              :disabled="modalInfo.taskStatus === '2'||modalInfo.taskStatus === '3' || modalInfo.taskType === '6'"-->
    <!--            v-model:value="modalInfo.frequency"-->
    <!--            style="width: 220px"-->
    <!--            placeholder="请选择"-->
    <!--            :options="frequencyOptions"-->
    <!--            :field-names="{ label: 'name', value: 'value' }"-->
    <!--            allowClear-->
    <!--          />-->
    <!--          <p class="p_red"-->
    <!--            >核算频率指目标的统计周期，每日：按天统计完成率，同事重置目标值；每月：按自然月统计完成率，同时重置目标值</p-->
    <!--          >-->
    <!--        </a-form-item>-->
    <!--        <a-form-item label="执行人" name="postIdList" required>-->
    <!--          <p>按岗位设置目标，如有新员工入职，设置岗位后，即可自动同步目标。</p>-->
    <!--          <a-checkbox-group   v-model:value="modalInfo.postIdList">-->
    <!--            <a-checkbox  :disabled="modalInfo.taskStatus === '2'||modalInfo.taskStatus === '3'" :value="item.id" v-for="item in levelOptions" :key="item.id">{{-->
    <!--              item.name-->
    <!--            }}</a-checkbox>-->
    <!--          </a-checkbox-group>-->
    <!--        </a-form-item>-->
    <!--        <a-form-item-->
    <!--          label="目标值"-->
    <!--          name="goal"-->
    <!--          :required="!['6'].includes(modalInfo.taskType as string)"-->
    <!--        >-->
    <!--          <div class="goal_box">-->
    <!--            <p v-show="!['6'].includes(modalInfo.taskType as string)">-->
    <!--              <a-inputNumber-->
    <!--                  :disabled="modalInfo.taskStatus === '3'"-->
    <!--                v-model:value="modalInfo.goal"-->
    <!--                placeholder="请输入"-->
    <!--                :min="1"-->
    <!--                style="width: 220px"-->
    <!--              />-->
    <!--              <span>家次/人</span>-->
    <!--            </p>-->
    <!--            <p v-show="modalInfo.taskType === '6'" class="p_red">业绩目标由系统自动从EDP同步。</p>-->
    <!--            &lt;!&ndash; <p v-show="modalInfo.taskType === '7'">-->
    <!--            <a-upload-->
    <!--              accept=".xlsx, .xls"-->
    <!--              :action="`${getAppEnvConfig().VITE_GLOB_API_URL}${-->
    <!--                getAppEnvConfig().VITE_GLOB_UPLOAD_URL-->
    <!--              }`"-->
    <!--              :headers="{ Authorization: `Bearer ${getToken()}` }"-->
    <!--              :max-count="1"-->
    <!--              v-model:file-list="modalInfo.upLoadList"-->
    <!--            >-->
    <!--              <a-button type="primary">-->
    <!--                <template #icon>-->
    <!--                  <Icon icon="ant-design:upload-outlined" />-->
    <!--                </template>-->
    <!--                上传文件-->
    <!--              </a-button>-->
    <!--            </a-upload>-->
    <!--            <span>支持上传.xls .xlsx文件，文件格式请参考示例文件。</span>-->
    <!--            <a-button type="link" @click="toDownExample">下载示例文件</a-button>-->
    <!--            </p> &ndash;&gt;-->
    <!--          </div>-->
    <!--        </a-form-item>-->
    <!--      </a-form>-->
    <!--    </div>-->
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  // import { Icon } from '/@/components/Icon';
  import dayjs, { Dayjs } from 'dayjs';
  import {
    getTypeOptions,
    getStatusOptions,
    getFrequencyOptions,
    getLevelOptions,
    getTaskPageList,
    onTaskAdd,
    onTaskUpdate,
    onTaskEnabled,
    getTaskById,
    onTaskDel,
    getDataRangeOptions,
  } from './targetData';
  import { useMessage } from '/@/hooks/web/useMessage';
  import Icon from '/@/components/Icon';
  import { getDepartmentEnabledTree } from '/@/api/system/department';
  import { getTagPage } from '/@/api/customerManage/tags';
  import { getProductALL } from '/@/api/groundPushManage/varietyDetail';
  import { getOtcTaskTypeUnit } from '/@/api/performanceManage/targetManage';
  const { notification, createMessage } = useMessage();
  // import { downloadByData } from '/@/utils/file/download';
  // import { getToken } from '/@/utils/auth';
  // import { getAppEnvConfig } from '/@/utils/env';
  const typeOptions = ref<any>([]);
  const statusOptions = ref<any>([]);
  const frequencyOptions = ref<any>([]);
  const levelOptions = ref<any>([]);
  const clientOptions = ref<any>([]);
  const productOptions = ref<any>([]);
  const cascaderOptions = ref<any>([]);
  const customerOptions = ref<any>([]);
  const unitOptions = ref<any>([]);
  onMounted(async () => {
    typeOptions.value = await getTypeOptions();
    statusOptions.value = await getStatusOptions();
    frequencyOptions.value = await getFrequencyOptions();
    levelOptions.value = await getLevelOptions();
    clientOptions.value = await getDataRangeOptions();
    customerOptions.value = await getTagList() ?? [];
    customerOptions.value.map((item) => {
      item.value = item.tagCode;
      item.name = item.tagName;
    });
    customerOptions.value.unshift({
      name: '全部',
      value: '1',
    });
    productOptions.value = await getProductALL() ?? [];
    productOptions.value.map((item) => {
      item.value = item.newProductgroupId;
      item.name = item.newName;
    });
    await getDepartment();
  });
  const getDepartment = async () => {
    try {
      let res = await getDepartmentEnabledTree();
      cascaderOptions.value = res ?? [];
    } catch (err) {
      console.log(err);
    }
  };
  const getTagList = async () => {
    const res =  await getTagPage({limit: 1 ,size: 99})
    return res?.list ?? [];
  }
  const searchForm = reactive({
    time: [],
    taskType: undefined,
    taskStatus: undefined,
  });
  const tableColumns = [
    {
      title: '序号',
      isHasIndex: true,
      key: 'No',
      align: 'center',
      width: 50,
    },
    {
      title: '目标类型',
      dataIndex: 'taskType',
      key: 'taskType',
      align: 'center',
      customRender: ({ text, record }) => {
        const obj = {
          children: text,
          props: {} as any
        };
        obj.props.rowSpan = record.taskTypeRowSpan || 1;
        return obj;
      }
    },
    {
      title: '目标周期',
      dataIndex: 'taskCycle',
      key: 'taskCycle',
      align: 'center',
      customRender: ({ text, record }) => {
        const obj = {
          children: text,
          props: {} as any
        };
        obj.props.rowSpan = record.taskCycleRowSpan || 1;
        return obj;
      }
    },
    {
      title: '执行人',
      dataIndex: 'operator',
      key: 'operator',
      align: 'center',
      customRender: ({ text, record }) => {
        const obj = {
          children: text,
          props: {} as any
        };
        obj.props.rowSpan = record.operatorRowSpan || 1;
        return obj;
      }
    },
    {
      title: '周期',
      dataIndex: 'cycle',
      key: 'cycle',
      align: 'center',
    },
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      align: 'center',
    },
    {
      title: '考核数据范围',
      dataIndex: 'dataRange',
      key: 'dataRange',
      align: 'center',
    },
    {
      title: '目标值（基准）',
      dataIndex: 'baseline',
      key: 'baseline',
      align: 'center',
    },
    {
      title: '目标值（下限）',
      dataIndex: 'baseline',
      key: 'baseline',
      align: 'center',
    },
    {
      title: '绩效权重',
      dataIndex: 'baseline',
      key: 'baseline',
      align: 'center',
    },
    {
      title: '考核状态',
      dataIndex: 'taskStatusName',
      key: 'taskStatusName',
      isSlot: true,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
    },
    {
      title: '操作人',
      dataIndex: 'createUserId',
      key: 'createUserId',
      align: 'center',
    },
    {
      title: '任务状态',
      dataIndex: 'isEnabled',
      key: 'isEnabled',
      isSlot: true,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 0,
    pageSize: 10,
  });
  const ruleColumns = [
    {
      title: '周期',
      dataIndex: 'frequency',
      key: 'frequency',
      align: 'center',
      width: 100,
    },
    {
      title: 'KPI名称',
      dataIndex: 'kpiName',
      key: 'kpiName',
      align: 'center',
      width: 200,
    },
    {
      title: '数据范围',
      dataIndex: 'dataRange',
      key: 'dataRange',
      align: 'center',
      width: 550,
    },
    {
      title: '目标值（基准）',
      dataIndex: 'baseline',
      key: 'baseline',
      align: 'center',
    },
    {
      title: '目标值（下限）',
      dataIndex: 'lower',
      key: 'lower',
      align: 'center',
    },
    {
      title: '绩效权重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center',
    },
    {
      title: ' ',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 100,
    },
  ];
  const ruleData = ref<any>([
    {
      ruleName: '',
      ruleDesc: '',
      targetNum: '',
      lower: '',
      weight: '',
      showRight: '',
    },
  ]);
  // 获取表格数据
  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        startTime: searchForm.time?.[0]
          ? dayjs(searchForm.time?.[0]).format('YYYY-MM-01 00:00:00')
          : '',
        endTime: searchForm.time?.[1]
          ? dayjs(searchForm.time?.[1]).endOf('month').format('YYYY-MM-DD 23:59:59')
          : '',
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getTaskPageList(temp);
      pagination.totalItems = res.total ?? 0;
      tableData.data =
        res?.list?.map((item) => {
          return {
            ...item,
            updateTime: item.modifyDate ?? item.createDate, // 有更新时间优先取更新时间，无更新时间取创建时间
          };
        }) ?? [];
      const tempArr = []
      res?.list?.forEach((item) => {
        if (item.dayList && item.dayList?.length > 0) {
          item.dayList.forEach((e) => {
            tempArr.push({
              ...item,
              ...e,
              cycle: '每日'
            })
          })
        }
        if (item.monthList && item.monthList?.length > 0) {
          item.monthList.forEach((e) => {
            tempArr.push({
              ...item,
              ...e,
              cycle: '每月'
            })
          })
        }
        if (item.weekList && item.weekList?.length > 0) {
          item.weekList.forEach((e) => {
            tempArr.push({
              ...item,
              ...e,
              cycle: '每周'
            })
          })
        }
      })
      console.log(tempArr);

      // 对数据进行排序，确保相同的值连续出现
      if (tempArr.length > 0) {
        tempArr.sort((a, b) => {
          // 首先按目标类型排序
          if (a.taskType !== b.taskType) {
            return a.taskType.localeCompare(b.taskType);
          }
          // 然后按目标周期排序
          if (a.taskCycle !== b.taskCycle) {
            return a.taskCycle.localeCompare(b.taskCycle);
          }
          // 最后按执行人排序
          if (a.operator !== b.operator) {
            return a.operator.localeCompare(b.operator);
          }
          return 0;
        });

        // 计算行合并（按层级顺序）
        setRowSpan(tempArr, 'taskType', []); // 目标类型是最顶层，没有父级
        setRowSpan(tempArr, 'taskCycle', ['taskType']); // 目标周期的父级是目标类型
        setRowSpan(tempArr, 'operator', ['taskType', 'taskCycle']); // 执行人的父级是目标类型和目标周期
      }

      tableData.data = tempArr
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  const reset = () => {
    searchForm.taskStatus = undefined;
    searchForm.taskType = undefined;
    searchForm.time = [];
    getList();
  };
  onMounted(() => {
    getList();
  });
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  const router = useRouter();
  const onDetail = (row: any) => {
    // console.log('达成明细', row);
    router.push({
      path: '/targetManage/detail',
      query: {
        id: row.id,
        taskType: row.taskType,
        startDate: row.startYearMonth,
        endDate: row.endYearMonth,
      },
    });
  };
  const onDel = async (row: any) => {
    try {
      let res = await onTaskDel({ ids: [row.id] });
      if (res) {
        notification.success({
          message: '提示',
          description: '删除成功',
        });
        getList();
      }
    } catch (err) {
      console.log(err);
    }
  };

  // 启停用
  const changeSwitch = async (row: any) => {
    row.switchloading = true;
    try {
      let temp = {
        id: row.id,
        isEnabled: row.isEnabled,
      };
      let res = await onTaskEnabled(temp);
      row.switchloading = false;
      if (res) {
        notification.success({
          message: '提示',
          description: '操作成功',
        });
        getList(1);
      }
    } catch (err) {
      console.log(err);
      row.switchloading = false;
    }
  };

  // 下载文件功能
  const downloadFile = () => {
    // 替换为实际下载链接
    const downloadUrl = 'https://example.com/download-link';
    const link = document.createElement('a');
    link.href = downloadUrl;
    // 设置下载文件名
    link.download = 'target_data.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formRef = ref();
  interface ModalInfo {
    taskType?: string;
    id?: string;
    goal?: number;
    frequency?: number | string;
    time?: Dayjs[] | null;
    postIdList?: string[];
    isEnabled?: string;
    taskStatus?: string;
    upLoadList?: any[];
    departIdList?: string[];
  }
  const modalInfo = ref<ModalInfo>({});
  const openModalVisible = ref<boolean>(false);
  const openModal = async (obj?: ModalInfo) => {
    if (obj?.id) {
      let res = await getTaskById({ id: obj.id });
      let t =
        res.taskType === '6'
          ? dayjs(res.startYearMonth).format('YYYY')
          : [
              dayjs(res.startYearMonth).format('YYYY-MM'),
              dayjs(res.endYearMonth).format('YYYY-MM'),
            ];
      modalInfo.value = {
        ...res,
        time: t,
        postIdList: res.postId?.split(','),
        taskStatus: obj.taskStatus,
      };
    } else {
      modalInfo.value = { isEnabled: '1', time: null };
      ruleData.value = [{
        frequency: null,
        kpiName: '',
        dataRange: '',
        baseline: '',
        lower: '',
        weight: '',
        showRight: false,
      }]
    }
    openModalVisible.value = true;
  };
  const changeTaskType = async () => {
    modalInfo.value.time = null;
    if (modalInfo.value.taskType === '6') {
        modalInfo.value.frequency  = '2';
    } else {
      modalInfo.value.frequency  = undefined;
    }
    unitOptions.value = await getOtcTaskTypeUnit(
      {
        taskType: modalInfo.value.taskType,
      }
    ) ?? [];
    console.log(unitOptions.value);
  };
  // 日期禁选规则
  const disabledDate = (current: Dayjs) => {
    return current && current < dayjs(); // 不能选择当前之前的日期
  };
  const modalLoading = ref<boolean>(false);
  // 新增/编辑目标
  const handleModalOk = () => {
    formRef.value
      .validate()
      .then(async () => {
        // let temp = {
        //   ...modalInfo.value,
        //   startYearMonth:
        //     modalInfo.value.taskType === '6'
        //       ? modalInfo.value.time + '-01'
        //       : dayjs(modalInfo.value.time?.[0]).format('YYYY-MM-01'),
        //   endYearMonth:
        //     modalInfo.value.taskType === '6'
        //       ? modalInfo.value.time + '-12'
        //       : dayjs(modalInfo.value.time?.[1]).endOf('month').format('YYYY-MM-DD'),
        //   postId: modalInfo.value.postIdList?.join(','),
        // };
        if (modalInfo.value.postIdList?.length === 0 || !modalInfo.value.postIdList) {
          if (modalInfo.value.departIdList?.length === 0 || !modalInfo.value.departIdList) {
            createMessage.warning('请选择目标执行人');
            return;
          }
        }
        const levelMap = new Map(levelOptions.value.map(item => [item.id, item.name]));
        const postName = modalInfo.value.postIdList ? modalInfo.value.postIdList.map(item => levelMap.get(item) as string) : [];
        ruleData.value.map((item) => {
          item.dataRange = {
            dataRangeList: [
              {
                rangeId: item.dataRangeLeft,
                rangeName: getNameByValue(clientOptions.value, item.dataRangeLeft),
                rangeType: item.dataRangeLeft === '1' ? item.rangeId : (item.rangeType || []).join(','),
                rangeTypeName: item.dataRangeLeft === '1' ? getNameByValue(customerOptions.value, item.rangeId) : getNameByValue(productOptions.value, item.rangeType) || '',
              },
            ]
          }
          if (item.showRight) {
            item.dataRange.dataRangeList.push({
              rangeId: item.dataRangeRight,
              rangeName: getNameByValue(clientOptions.value, item.dataRangeRight),
              rangeType: item.dataRangeRight === '1' ? item.rangeId : (item.rangeType || []).join(','),
              rangeTypeName: item.dataRangeRight === '1' ? getNameByValue : getNameByValue(productOptions.value, item.rangeType) || '',
            });
          }
        });
        let temp = {
          taskType: modalInfo.value.taskType,
          startYearMonth:
            modalInfo.value.taskType === '6'
              ? modalInfo.value.time + '-01'
              : dayjs(modalInfo.value.time?.[0]).format('YYYY-MM-01'),
          endYearMonth:
            modalInfo.value.taskType === '6'
              ? modalInfo.value.time + '-12'
              : dayjs(modalInfo.value.time?.[1]).endOf('month').format('YYYY-MM-DD'),
          postId: modalInfo.value.postIdList?.join(','),
          postNames: postName.join(','),
          deptIds: modalInfo.value.departIdList?.join(','),
          deptNames: getNodeNames(cascaderOptions.value, modalInfo.value.departIdList || []).join(','),
          isEnabled: '1',
          addOtcSalesmanTaskVisitDTOList: ruleData.value.map((item) => {
            return {
              frequency: item.frequency,
              kpiName: item.kpiName,
              dataRange: item.dataRange,
              baseline: item.baseline,
              lower: item.lower,
              weight: item.weight,
              unit: item.unit,
            };
          }),
        };
        // console.log(ruleData.value);
        console.log(temp, 888,modalInfo.value.departIdList,);
        let res = null;
        if (modalInfo.value.id) {
          res = await onTaskUpdate(temp);
        } else {
          res = await onTaskAdd(temp);
        }
        if (res) {
          notification.success({
            message: '提示',
            description: '操作成功',
          });
          getList(modalInfo.value.id ? 1 : undefined);
          openModalVisible.value = false;
        }
      })
      .catch((error) => {
        console.log('error', error);
      });
  };
  const addDataRange = (record, index) => {
    console.log(record, index);
    console.log(ruleData.value[index].dataRangeLeft);
    if (!ruleData.value[index].dataRangeLeft) {
      createMessage.warning('请先选择范围');
      return;
    }
    ruleData.value[index].showRight = true;
    ruleData.value[index].dataRangeRight = ruleData.value[index].dataRangeLeft === '2' ? '1' : '2';
  };
  const addRule = () => {
    ruleData.value.push({
      frequency: '',
      kpiName: '',
      dataRange: '',
      baseline: '',
      lower: '',
      weight: '',
      showRight: false,
    });
  };
  const DeleteRule = (_, index) => {
    ruleData.value.splice(index, 1);
  };
  const deleteDataRange = (_, index) => {
    ruleData.value[index].showRight = false;
  };
  // 递归查找树形节点名称
const getNodeNames = (nodes: any[], ids: string[], names: string[] = []) => {
  nodes.forEach(node => {
    if (ids.includes(node.id)) {
      names.push(node.name);
    }
    if (node.children?.length) {
      getNodeNames(node.children, ids, names);
    }
  });
  return names;
};
const changeProduct = (value:string[]) => {
  // const hasOtherValues = value.some(v => v !== '1' && v !== '2');
  // if (hasOtherValues) {
  //   record.rangeType = value.filter(v => v !== '1' && v !== '2');
  // } else if (value.includes('1')) {
  //   record.rangeType = ['1'];
  // } else if (value.includes('2')) {
  //   record.rangeType = ['2'];
  // }
  console.log(value);
}
// 根据value获取name
  const getNameByValue = (options: any[], value: string | []) => {
    if (Array.isArray(value)) {
      return value.map((v) => getNameByValue(options, v)).join(',');
    }
    const item = options.find((option) => option.value === value);
    return item ? item.name : '';
  };

  // 计算行合并的函数
  const setRowSpan = (data: any[], field: string, parentFields: string[] = []) => {
    let count = 0; // 开始的第一项
    let indexCount = 1; // 对比的另外一项

    // 初始化所有行的 rowSpan
    data.forEach(item => {
      if (!item[`${field}RowSpan`]) {
        item[`${field}RowSpan`] = 1;
      }
    });

    while (indexCount < data.length) {
      const item = data[count];

      // 检查当前字段和所有父级字段是否都相同
      let shouldMerge = item[field] === data[indexCount][field];

      // 如果有父级字段，需要确保父级字段也相同才能合并
      for (const parentField of parentFields) {
        if (item[parentField] !== data[indexCount][parentField]) {
          shouldMerge = false;
          break;
        }
      }

      if (shouldMerge) {
        item[`${field}RowSpan`]++;
        data[indexCount][`${field}RowSpan`] = 0;
      } else {
        // 不相同，重新开始
        count = indexCount;
      }
      // 对比完后进入下一项
      indexCount++;
    }
  };
  // 下载示例文件
  // const toDownExample = () => {
  // console.log('下载示例');
  // let temp = {
  //   ...searchForm,
  // };
  // let res = await exportData(temp);
  // downloadByData(
  //   res.data,
  //   `下载示例.xlsx`,
  //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  // );
  // };

  // 上传文件
  // const toUpload = () => {
  //   console.log('上传文件');
  // };
</script>

<style scoped lang="less">
  .targetManage {
    width: 100%;
    height: 100%;
    padding: 8px;
    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      > div {
        width: 100%;
        padding: 16px;
        &:last-child {
          flex: 1;
          height: 0;
        }
      }
      .p_box {
        padding: 0 16px;
      }
    }
    .filterForm_box {
      * + * {
        margin-left: 16px;
      }
    }
    .table_box {
      .status {
        line-height: 16px;
        margin-left: 20px;
        position: relative;
        &::before {
          content: '';
          width: 10px;
          height: 10px;
          background-color: royalblue;
          border-radius: 50%;
          position: absolute;
          top: 5px;
          left: -16px;
        }
      }
      .status_warning::before {
        background: orange;
      }
      .status_success::before {
        background: green;
      }
      .status_gray::before {
        background: grey;
      }
    }
  }
  .modal_box {
    padding: 16px;
    p {
      line-height: 32px;
      margin-bottom: 0;
    }
    .p_red {
      color: #c10000;
    }
    .form_box {
      margin-top: 10px;
      .ant-checkbox-group {
        width: 250px;
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        .ant-checkbox-wrapper + .ant-checkbox-wrapper {
          margin-left: 0;
          margin-top: 8px;
        }
      }
    }
    .goal_box p:not(:last-child) {
      > * + * {
        margin-left: 8px;
      }
    }
    .required {
      &::before {
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
    :deep(.ant-form-item-explain-error) {
      display: none !important;
    }
  }
</style>
