<template>
  <SelectApiConfig
    v-model="data.config"
    :paramTree="[]"
    :exampleStr="exampleStr"
    @update:modelValue="submit"
  >
    <InputModel
      :value="data.config.name"
      :placeholder="t('配置API')"
      style="width: 100%; min-width: 100px"
    />
  </SelectApiConfig>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { SelectApiConfig, InputModel } from '/@/components/ApiConfig';
  import { cloneDeep } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ApiConfig } from '/@/components/ApiConfig/src/interface';
  const { t } = useI18n();
  const emit = defineEmits(['update:modelValue', 'change']);
  const exampleStr = `{
      code: 0,
      msg: 'success',
      data: {
          "data": [
            {
              "value": "1",
              "label": "a-1",
              "a": "a-1",
              "b": "a-1",
              "c": "a-1",
              "children": [
                {
                  "value": "11",
                  "label": "a-11",
                  "a": "a-11",
                  "b": "a-11",
                  "c": "a-11",
                  "children": []
                }
              ]
            }
          ],
          "columns": [
            {
              "value": "label"
            },
            {
              "value": "value"
            },
            {
              "value": "a"
            },
            {
              "value": "b"
            },
            {
              "value": "c"
            }
          ]
        },//树结构
    }`;
  let props = withDefaults(
    defineProps<{
      modelValue: ApiConfig;
    }>(),
    {
      modelValue: () => {
        return {
          id: '',
          name: '',
          method: '',
          script: '',
          requestParamsConfigs: [], //Query Params
          requestHeaderConfigs: [], //Header
          requestBodyConfigs: [], //Body
        };
      },
    },
  );
  let data: {
    config: ApiConfig;
  } = reactive({
    config: props.modelValue,
  });
  function submit() {
    emit('update:modelValue', cloneDeep(data.config));
    emit('change', cloneDeep(data.config));
  }
</script>

<style lang="less" scoped>
  .title {
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 10px;
  }

  .padding {
    padding: 10px 20px;
  }

  .btn-item {
    position: absolute;
    bottom: 10px;
  }

  .editor-box {
    width: 300px;
    position: absolute;
    height: 350px;
    left: 140px;
    bottom: 13px;
    border: 1px solid #ccc;
    box-shadow: 0 0 6px 3px #ccc;

    .editor-close {
      position: absolute;
      top: -3px;
      right: 13px;
      font-size: 16px;
      cursor: pointer;
    }

    .editor-copy {
      position: absolute;
      bottom: 5px;
      right: 15px;
      cursor: pointer;
      z-index: 999;
      color: #5e95ff;
    }
  }
</style>
