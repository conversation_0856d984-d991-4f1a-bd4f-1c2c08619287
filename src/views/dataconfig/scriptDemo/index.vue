<template>
  <PageWrapper dense fixedHeight contentFullHeight>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" v-auth="'scriptDemo:add'" @click="handleCreate">
          {{ t('新增') }}
        </a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                icon: 'clarity:note-edit-line',
                auth: 'scriptDemo:edit',
                onClick: handleEdit.bind(null, record),
              },
              {
                icon: 'ant-design:delete-outlined',
                auth: 'scriptDemo:delete',
                color: 'error',
                popConfirm: {
                  title: t('是否确认删除？'),
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <scriptModal @register="registerModal" @success="handleSuccess" />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, BasicColumn, FormSchema } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import scriptModal from './components/scriptModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { PageWrapper } from '/@/components/Page';
  import { getScriptDemoPageList, deleteScriptDemo } from '/@/api/system/script';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const columns: BasicColumn[] = [
    {
      title: '示例编码',
      dataIndex: 'code',
      align: 'left',
      resizable: true,
    },
    {
      title: '示例名称',
      dataIndex: 'name',
      align: 'left',
      resizable: true,
    },
    {
      title: t('备注'),
      dataIndex: 'remark',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建人'),
      dataIndex: 'createUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('创建时间'),
      dataIndex: 'createDate',
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改人'),
      dataIndex: 'modifyUserName',
      align: 'left',
      resizable: true,
    },
    {
      title: t('最后修改时间'),
      dataIndex: 'modifyDate',
      align: 'left',
      resizable: true,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: '请输入名称或编号',
      },
    },
  ];

  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerModal, { openModal }] = useModal();

  const [registerTable, { reload }] = useTable({
    title: '示例脚本管理',
    api: getScriptDemoPageList,
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      showResetButton: false,
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('scriptManagement:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      id: record.id,
      isUpdate: true,
    });
  }

  function handleDelete(record: Recordable) {
    deleteScriptDemo([record.id]).then((_) => {
      reload();
      notification.success({
        message: t('提示'),
        description: t('删除成功'),
      });
    });
  }

  function handleSuccess() {
    reload();
  }
</script>
