<template>
  <BasicModal
    v-bind="$attrs"
    destroyOnClose
    @register="registerModal"
    showFooter
    :title="getTitle"
    width="55%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :style="{ 'margin-right': '10px' }">
      <template #code="{ model }">
        <CodeEditor v-model:value="model.scriptContent" language="json" />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { addScriptDemo, updateScriptDemo, getScriptDemoInfo } from '/@/api/system/script';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const formSchema: FormSchema[] = [
    {
      title: '基本信息',
      field: 'code',
      label: '示例编码',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入示例编码',
      },
      colProps: { span: 12 },
    },

    {
      field: 'name',
      label: '示例名称',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入示例名称',
      },
      colProps: { span: 12 },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请输入备注'),
      },
      colProps: { span: 24 },
    },
    {
      title: '脚本编辑',
      field: 'scriptContent',
      label: ' ',
      component: 'Input',
      slot: 'code',
      colProps: { span: 24 },
    },
  ];

  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const { notification } = useMessage();
  const rowId = ref('');
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false, fixedHeight: true, defaultFullscreen: true });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.id;
      const info = await getScriptDemoInfo(rowId.value);
      setTimeout(() => {
        //使用setTimeout解决编辑器格式不对齐问题
        setFieldsValue(info);
      }, 100);
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增示例脚本' : '编辑示例脚本'));

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      if (!unref(isUpdate)) {
        await addScriptDemo(values);
        notification.success({
          message: t('提示'),
          description: t('新增成功'),
        });
      } else {
        values.id = rowId.value;
        await updateScriptDemo(values);
        notification.success({
          message: t('提示'),
          description: t('编辑成功'),
        });
      }
      closeModal();
      emit('success');
    } catch (error) {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
