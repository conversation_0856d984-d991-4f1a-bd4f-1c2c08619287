<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <div
      id="luckysheet"
      style="
        margin: 0;
        padding: 0;
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        overflow: auto;
      "
    ></div>
  </BasicModal>
  <RenameModal @register="registerRenameModal" @success="handleSuccess" />
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { addExcelFile, getExcelInfo, updateExcel } from '/@/api/system/excel';
  import { ExcelAddParamsModel } from '/@/api/system/excel/model/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import luckysheet from 'luckysheet';
  import RenameModal from './RenameModal.vue';

  defineOptions({
    name: 'Luckyexcel',
  });

  const defaultData = [
    {
      name: 'Sheet', //工作表名称
      color: '', //工作表颜色
      index: 0, //工作表索引
      status: 1, //激活状态
      order: 1, //工作表的下标
      hide: 0, //是否隐藏
      row: 20, //行数
      column: 20, //列数
      defaultRowHeight: 22, //自定义行高
      defaultColWidth: 100, //自定义列宽
      celldata: [], //初始化使用的单元格数据
      config: {
        merge: {}, //合并单元格
        rowlen: {}, //表格行高
        columnlen: {}, //表格列宽
        rowhidden: {}, //隐藏行
        colhidden: {}, //隐藏列
        borderInfo: {}, //边框
        authority: {}, //工作表保护
      },
      scrollLeft: 0, //左右滚动条位置
      scrollTop: 315, //上下滚动条位置
      luckysheet_select_save: [], //选中的区域
      calcChain: [], //公式链
      isPivotTable: false, //是否数据透视表
      pivotTable: {}, //数据透视表设置
      filter_select: {}, //筛选范围
      filter: null, //筛选配置
      luckysheet_alternateformat_save: [], //交替颜色
      luckysheet_alternateformat_save_modelCustom: [], //自定义交替颜色
      luckysheet_conditionformat_save: {}, //条件格式
      frozen: {}, //冻结行列配置
      chart: [], //图表配置
      zoomRatio: 1, // 缩放比例
      image: [], //图片
      showGridLines: 1, //是否显示网格线
      dataVerification: {}, //数据验证配置
    },
  ];

  const options = {
    container: 'luckysheet', //luckysheet为容器id
    lang: 'zh', // 设定表格语言
    gridKey: Date.now(),
    data: defaultData,
    title: '',
    showinfobar: false, //是否显示顶部信息栏
    // showtoolbar: false, //是否显示工具栏
    // enableAddRow: false, //允许添加行
    showsheetbar: false, //是否显示底部sheet页按钮

    // showsheetbarConfig: {
    //   add: false, //新增sheet
    //   menu: false, //sheet管理菜单
    //   sheet: false, //sheet页显示
    // },
    // showstatisticBar: false, //是否显示底部计数栏
    // showstatisticBarConfig: {
    //   count: false, // 计数栏
    //   view: false, // 打印视图
    //   zoom: false, // 缩放
    // },
    // sheetFormulaBar: false, //是否显示公式栏
    // allowCopy: false, //是否允许拷贝
    // //自定义底部sheet页右击菜单
    // sheetRightClickConfig: {
    //   delete: false, // 删除
    //   copy: false, // 复制
    //   rename: false, //重命名
    //   color: false, //更改颜色
    //   hide: false, //隐藏，取消隐藏
    //   move: false, //向左移，向右移
    // },
    // //自定义单元格右键菜单
    // cellRightClickConfig: {
    //   copy: false, // 复制
    //   copyAs: false, // 复制为
    //   paste: false, // 粘贴
    //   insertRow: false, // 插入行
    //   insertColumn: false, // 插入列
    //   deleteRow: false, // 删除选中行
    //   deleteColumn: false, // 删除选中列
    //   deleteCell: false, // 删除单元格
    //   hideRow: false, // 隐藏选中行和显示选中行
    //   hideColumn: false, // 隐藏选中列和显示选中列
    //   rowHeight: false, // 行高
    //   columnWidth: false, // 列宽
    //   clear: false, // 清除内容
    //   matrix: false, // 矩阵操作选区
    //   sort: false, // 排序选区
    //   filter: false, // 筛选选区
    //   chart: false, // 图表生成
    //   image: false, // 插入图片
    //   link: false, // 插入链接
    //   data: false, // 数据验证
    //   cellFormat: false, // 设置单元格格式
    // },
    // 自定义顶部菜单
    showtoolbarConfig: {
      //   undoRedo: true, //撤销重做，注意撤消重做是两个按钮，由这一个配置决定显示还是隐藏
      //   paintFormat: true, //格式刷
      //   currencyFormat: false, //货币格式
      //   percentageFormat: false, //百分比格式
      //   numberDecrease: false, // '减少小数位数'
      //   numberIncrease: false, // '增加小数位数
      //   moreFormats: false, // '更多格式'
      //   font: false, // '字体'
      //   fontSize: true, // '字号大小'
      //   bold: false, // '粗体 (Ctrl+B)'
      //   italic: false, // '斜体 (Ctrl+I)'
      //   strikethrough: false, // '删除线 (Alt+Shift+5)'
      //   underline: false, // '下划线 (Alt+Shift+6)'
      //   textColor: true, // '文本颜色'
      //   fillColor: true, // '单元格颜色'
      //   border: true, // '边框'
      //   mergeCell: true, // '合并单元格'
      //   horizontalAlignMode: true, // '水平对齐方式'
      //   verticalAlignMode: true, // '垂直对齐方式'
      //   textWrapMode: true, // '换行方式'
      //   textRotateMode: false, // '文本旋转方式'
      //   image: false, // '插入图片'
      //   link: false, // '插入链接'
      //   chart: false, // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
      //   postil: false, //'批注'
      //   pivotTable: false, //'数据透视表'
      //   function: false, // '公式'
      //   frozenMode: false, // '冻结方式'
      //   sortAndFilter: false, // '排序和筛选'
      //   conditionalFormat: false, // '条件格式'
      //   dataVerification: false, // '数据验证'
      //   splitColumn: false, // '分列'
      //   screenshot: true, // '截图'
      //   findAndReplace: true, // '查找替换'
      //   protection: false, // '工作表保护'
      print: false, // '打印'
    },
    hook: {
      // 表格初始化完成后触发
      cellUpdated: (r, c, oldValue, newValue, isRefresh) => {
        emit('cellUpdated', { r, c, oldValue, newValue, isRefresh });
      },
    },
  };

  const init = () => {
    luckysheet.create(options);
  };

  const { t } = useI18n();

  const emit = defineEmits(['success', 'register', 'cellUpdated']);

  const { notification } = useMessage();
  const isUpdate = ref(true);
  const fileName = ref('');
  const rowId = ref('');
  const [registerRenameModal, { openModal }] = useModal();

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false, canFullscreen: false, defaultFullscreen: true });
    isUpdate.value = !!data?.isUpdate;
    rowId.value = data.id || '';
    if (isUpdate.value) {
      const info = await getExcelInfo(data.id);
      fileName.value = info.fileName;
      options.data = JSON.parse(info.jsonContent);
    } else {
      options.data = defaultData;
    }
    init();
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增excel' : '编辑excel'));

  const handleSubmit = async () => {
    openModal(true, {
      id: rowId.value,
      fileName: fileName.value,
      isUpdate: isUpdate.value,
      title: '文件名称设置',
    });
  };

  const handleClose = () => {
    luckysheet.destroy();
  };

  const handleSuccess = async (info) => {
    const params: ExcelAddParamsModel = {
      jsonContent: JSON.stringify(luckysheet.getAllSheets()),
      fileJson: JSON.stringify(luckysheet.getluckysheetfile()), //列表页下载所需数据
      fileName: info.fileName,
    };
    if (isUpdate.value) {
      params.id = rowId.value;
      await updateExcel(params);
    } else {
      await addExcelFile(params);
    }

    notification.success({
      message: t('提示'),
      description: `${isUpdate.value ? '编辑' : '新增'}excel成功！`,
    });

    emit('success');
    handleClose();
    closeModal();
  };
</script>

<style lang="less">
  .luckysheet-input-box {
    z-index: 99999 !important;
  }
</style>
