<template>
  <div>
    <NodeHead class="mb-3" :nodeName="t('流程属性')" />
    <a-tabs>
      <a-tab-pane key="1" :tab="t('基础配置')">
        <ProcessBasic />
      </a-tab-pane>
      <a-tab-pane key="2" :tab="t('权限设置')">
        <ProcessPermission />
      </a-tab-pane>
      <!-- <a-tab-pane key="3" tab="专项菜单">
        <ProcessMenu />
      </a-tab-pane> -->
      <a-tab-pane key="4" :tab="t('关联功能')">
        <AssociatedFunctions />
      </a-tab-pane>
      <a-tab-pane key="5" :tab="t('超时提醒')">
        <ProcessTimeout />
      </a-tab-pane>
      <a-tab-pane key="6" :tab="t('关联任务')">
        <ProcessRelated />
      </a-tab-pane>
      <a-tab-pane key="7" :tab="t('流程参数')">
        <ProcessParameters />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { NodeHead } from '/@/components/ModalPanel/index';
  import ProcessBasic from '/@bpmn/panel/process/basic/Index.vue';
  import ProcessPermission from '/@bpmn/panel/process/ProcessPermission.vue';
  // import ProcessMenu from '/@bpmn/panel/process/ProcessMenu.vue';
  import AssociatedFunctions from '/@bpmn/panel/process/functionForm/Index.vue';
  import ProcessTimeout from '/@bpmn/panel/process/ProcessTimeout.vue';
  import ProcessRelated from '/@bpmn/panel/process/related/Index.vue';
  import ProcessParameters from '/@bpmn/components/parameters/Process.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
</script>

<style scoped></style>
