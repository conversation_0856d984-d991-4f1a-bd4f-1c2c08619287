<template>
  <BasicPanel>
    <template #basic v-if="showPanel">
      <FormItem :label="t('记录信息：')">
        <a-select
          v-model:value="formInfo.recordInfo"
          style="width: 100%"
          :options="recordTypeOptions"
        />
      </FormItem>
    </template>
    <!-- <template #noticePolicy v-if="showPanel">
      <FormItem :label="t('通知策略：')">
        <NoticePolicyConfig v-model="formInfo.noticePolicyConfigs" />
      </FormItem>
    </template> -->
    <a-tab-pane key="2" :tab="t('脚本参数配置')">
      <FormItem :label="t('是否启用：')">
        <a-switch v-model:checked="formInfo.script.enabled" />
      </FormItem>

      <template v-if="formInfo.script.enabled">
        <FormItem :label="t('脚本格式：')">
          <a-select
            v-model:value="formInfo.script.scriptFormat"
            @change="handleFomatChange"
            style="width: 100%"
          >
            <a-select-option value="JavaScript">JavaScript</a-select-option>
            <a-select-option value="Groovy">Groovy</a-select-option>
          </a-select>
        </FormItem>

        <FormItem :label="t('执行脚本：')">
          <a-textarea
            v-model:value="formInfo.script.scriptContent"
            @change="debounceChange"
            :rows="10"
          />
        </FormItem>
      </template>
    </a-tab-pane>
    <a-tab-pane key="3" tab="API">
      <FormItem :label="t('是否启用：')">
        <a-switch v-model:checked="formInfo.api.enabled" />
      </FormItem>

      <template v-if="formInfo.api.enabled">
        <FormItem :label="t('Api配置：')">
          <ScriptApiSelect
            style="width: 100%"
            v-model="formInfo.api.apiConfig"
            :need-hide-components="true"
          />
        </FormItem>
      </template>
      <template v-if="formInfo.api.enabled">
        <FormItem :label="t('消息模板配置：')">
          <SelectPushMessage
            style="width: 100%"
            v-model="formInfo.api.messageConfig"
            :need-hide-components="true"
          />
        </FormItem>
      </template>
    </a-tab-pane>
    <a-tab-pane key="4" :tab="t('参数操作')">
      <ParamAssignmentConfig :needFromData="false" :processParameter="processParameter"
    /></a-tab-pane>
  </BasicPanel>
</template>

<script setup lang="ts">
  import useStateFormInfo from '/@bpmn/hooks/useStateFormInfo';
  import BasicPanel from '/@bpmn/components/BasicPanel.vue';
  import FormItem from '/@bpmn/layout/FormItem.vue';
  // import NoticePolicyConfig from '/@bpmn/components/NoticePolicyConfig.vue';
  import ScriptApiSelect from '/@bpmn/components/arguments/ScriptApiSelect.vue';
  import ParamAssignmentConfig from '/@bpmn/components/parameters/ParamAssignmentConfig.vue';
  import SelectPushMessage from '/@bpmn/components/arguments/SelectPushMessage.vue';
  import { useBpmnStore } from '/@bpmn/store/bpmn';
  import { RecordType } from '/@/enums/workflowEnum';
  import { computed, inject, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const { showPanel, formInfo } = useStateFormInfo();
  // 记录信息
  const recordTypeOptions = [
    {
      value: RecordType.NO_RECORD,
      label: t('不在流程记录中记录脚本任务操作信息'),
    },
    {
      value: RecordType.RECORD,
      label: t('记录脚本任务操作信息'),
    },
  ];
  const updateScriptTaskExpression = inject('updateScriptTaskExpression') as Function;
  const bpmnStore = useBpmnStore();
  const processParameter = computed(() => {
    return [
      {
        name: t('流程参数'),
        id: 'processParameter',
        disabled: true,
        children: bpmnStore.processInfo.processParamConfigs,
      },
    ];
  });
  onMounted(() => {
    updateScriptTaskExpression(
      formInfo.value.script.scriptFormat,
      formInfo.value.script.scriptContent,
    );
  });
  //进行防抖处理
  const debounceChange = debounce(handleChange, 500);

  function handleFomatChange() {
    if (formInfo.value.script.scriptFormat === 'JavaScript') {
      formInfo.value.script.scriptContent = 'var a = 1;';
    } else if (formInfo.value.script.scriptFormat === 'Groovy') {
      formInfo.value.script.scriptContent = 'def a = 1;';
    }
    updateScriptTaskExpression(
      formInfo.value.script.scriptFormat,
      formInfo.value.script.scriptContent,
    );
  }

  function handleChange() {
    if (
      formInfo.value.script.scriptFormat === 'JavaScript' &&
      !formInfo.value.script.scriptContent
    ) {
      formInfo.value.script.scriptContent = 'var a = 1;';
    } else if (
      formInfo.value.script.scriptFormat === 'Groovy' &&
      !formInfo.value.script.scriptContent
    ) {
      formInfo.value.script.scriptContent = 'def a = 1;';
    }
    updateScriptTaskExpression(
      formInfo.value.script.scriptFormat,
      formInfo.value.script.scriptContent,
    );
  }
</script>

<style scoped></style>
