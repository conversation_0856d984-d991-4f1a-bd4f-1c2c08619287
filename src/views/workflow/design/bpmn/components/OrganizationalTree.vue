<template>
  <div class="overflow-hidden">
    <NodeHead class="header-title" node-name="组织架构" />
    <BasicTree
      title=""
      :treeData="treeData"
      :fieldNames="{ key: 'id', title: 'name' }"
      @select="handleSelect"
    />
  </div>
</template>

<script setup lang="ts">
  import { getDepartmentTree } from '/@/api/system/department';
  import { TreeItem } from '/@/components/Tree';
  import { BasicTree } from '/@/components/Tree';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import { onMounted, ref } from 'vue';
  const emits = defineEmits(['select']);
  const treeData = ref<TreeItem[]>([]);
  async function getList() {
    treeData.value = (await getDepartmentTree()) as unknown as TreeItem[];
  }
  function handleSelect(keys: string) {
    emits('select', keys[0]);
  }
  onMounted(() => {
    getList();
  });
</script>
<style scoped>
  .header-title {
    height: 40px;
    font-size: 16px;
    color: #333333;
    border-bottom: 1px solid #f0f0f0;
  }
</style>
