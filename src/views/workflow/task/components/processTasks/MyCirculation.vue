<template>
  <BasicTable @register="registerTable">
    <template #action="{ record }">
      <TableAction
        :actions="[
          {
            icon: 'ant-design:eye-outlined',
            auth: 'processtasks:view',
            tooltip: '查看',
            onClick: handleView.bind(null, record),
          },
        ]"
      />
    </template>
    <template #currentProgress="{ record }">
      <a-progress
        v-if="typeof record.currentProgress === 'number' && isFinite(record.currentProgress)"
        :percent="record.currentProgress"
        size="small"
      />
    </template>
  </BasicTable>
  <LookProcess
    v-if="visibleLookProcess"
    :visible="visibleLookProcess"
    :taskId="taskIdProcess"
    :processId="processIdProcess"
    @close="handleLookClose"
  />
</template>

<script setup lang="ts">
  import userTaskTable from './../../hooks/userTaskTable';

  import LookProcess from './../LookProcess.vue';

  import { BasicTable, useTable, BasicColumn, TableAction } from '/@/components/Table';
  import { getSchemaTask } from '/@/api/workflow/process';
  import { TaskTypeUrl } from '/@/enums/workflowEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ref } from 'vue';
  const { t } = useI18n();
  const configColumns: BasicColumn[] = [
    {
      title: t('流水号'),
      dataIndex: 'serialNumber',
      width: 80,
      sorter: true,
    },
    {
      title: t('流程名称'),
      dataIndex: 'processName',
      width: '32%',
      align: 'left',
    },
    {
      title: t('任务名称'),
      dataIndex: 'taskName',
      sorter: true,
      width: '17%',
      align: 'left',
    },
    {
      title: t('当前进度'),
      dataIndex: 'currentProgress',
      sorter: true,
      width: '17%',
      slots: { customRender: 'currentProgress' },
    },
    {
      title: t('发起人'),
      dataIndex: 'originator',
      align: 'left',
      width: 80,
    },
    {
      title: t('发起时间'),
      width: 120,
      dataIndex: 'createTime',
      align: 'left',
    },
  ];
  const visibleLookProcess = ref(false);
  const taskIdProcess = ref('');
  const processIdProcess = ref('');

  const { formConfig } = userTaskTable();
  const [registerTable] = useTable({
    title: t('我的传阅列表'),
    api: getSchemaTask,
    rowKey: 'taskId',
    columns: configColumns,
    formConfig: formConfig(),
    beforeFetch: (params) => {
      return { data: params, taskUrl: TaskTypeUrl.CIRCULATED };
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    showIndexColumn: false,
    pagination: {
      pageSize: 18,
    },
    actionColumn: {
      width: 60,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  const handleView = (record) => {
    visibleLookProcess.value = true;
    taskIdProcess.value = record.taskId;
    processIdProcess.value = record.processId;
  };

  const handleLookClose = () => {
    visibleLookProcess.value = false;
  };
</script>

<style scoped></style>
