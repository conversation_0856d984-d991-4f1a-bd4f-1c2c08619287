<template>
  <div class="table-top">
    <span>流程处理信息</span>
    <span @click="handleClick" class="click-btn">
      {{ clickText }} <DoubleRightOutlined :class="isShow ? 'down' : 'up'" />
    </span>
  </div>
  <BasicTable @register="registerTable" v-show="isShow" style="height: calc(30vh - 40px)" />
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { getProcessRecord } from '/@/api/workflow/task';
  import { ref, computed, h } from 'vue';
  import { DoubleRightOutlined } from '@ant-design/icons-vue';
  const props = defineProps({
    processId: String,
  });
  const columns: BasicColumn[] = [
    {
      title: '节点名称',
      dataIndex: 'nodeName',
    },
    {
      title: '执行人',
      dataIndex: 'approveUserName',
    },
    {
      title: '操作',
      dataIndex: 'buttonName',
    },
    {
      title: '审批意见',
      dataIndex: 'approveComment',
    },
    {
      title: '状态',
      dataIndex: 'status',
      customRender: ({ record }) => {
        const color = record.status === '正常' ? '#95f204' : '#ed6f6f';
        return h('span', { style: { color } }, record.status);
      },
    },
    {
      title: '接收时间',
      dataIndex: 'startTime',
    },
    {
      title: '审核时间',
      dataIndex: 'approveTime',
    },
    {
      title: '耗时',
      dataIndex: 'consumingTime',
    },
  ];
  const emits = defineEmits(['btnClick']);

  const isShow = ref(false);
  const clickText = computed(() => {
    return isShow.value ? '收缩' : '展开';
  });

  const [registerTable] = useTable({
    api: getProcessRecord,
    beforeFetch: (params) => {
      return { ...params, processInstanceId: props.processId };
    },
    columns,
    striped: false,
    showIndexColumn: false,
    pagination: false,
  });

  const handleClick = () => {
    isShow.value = !isShow.value;
    emits('btnClick', isShow.value);
  };
</script>
<style lang="less" scoped>
  .table-top {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #e5e7eb;
    line-height: 40px;
  }

  .click-btn {
    color: #5e95ff;
    cursor: pointer;
  }

  .up {
    transform: rotate(270deg);
  }

  .down {
    transform: rotate(90deg);
  }

  :deep(.ant-table-container) {
    height: calc(100% - 40px);
  }
</style>
