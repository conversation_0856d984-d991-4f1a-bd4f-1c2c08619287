<template>
  <div class="wrap">
    <div class="head"
      ><div class="title"><DesignLogo /><slot name="title"></slot></div
      ><div class="operation"> <slot name="close"></slot></div
    ></div>
    <div v-if="hasFullSlot" class="full-box"><slot name="full"></slot></div>
    <div class="box" v-else>
      <div class="left-box" ref="left">
        <slot name="left"></slot>
      </div>
      <div class="right-box" ref="right">
        <div class="fewer-panel-box" @click="changeShowPanel">
          <component :is="fewerPanelComponent" />
        </div>
        <div v-show="showPanel" class="right"><slot name="right"></slot></div
      ></div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { computed, ref, useSlots } from 'vue';
  import { FewerLeft, FewerRight } from '/@/components/ModalPanel';
  import { DesignLogo } from '/@/components/ModalPanel/index';
  let left = ref();
  let right = ref();
  let showPanel = ref(true);
  const hasFullSlot = computed(() => {
    return !!useSlots().full;
  });
  let fewerPanelComponent = computed(() => {
    return showPanel.value ? FewerRight : FewerLeft;
  });
  function showRightBox() {
    left.value.style.width = 'calc(100% - 350px)';
    right.value.style.width = '350px';
  }
  function hideRightBox() {
    left.value.style.width = 'calc(100% - 60px)';
    right.value.style.width = '60px';
  }
  function changeShowPanel() {
    showPanel.value = !showPanel.value;
    if (showPanel.value) {
      showRightBox();
    } else {
      hideRightBox();
    }
  }
</script>
<style lang="less" scoped>
  .wrap {
    position: fixed;
    inset: 0;
    z-index: 999;
    background-color: #fff;

    .head {
      height: 50px;
      box-shadow: 5px 5px 5px rgb(0 0 0 / 10%);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        display: flex;
        align-items: center;
      }

      .operation {
        margin-right: 20px;
      }
    }
  }

  [data-theme='dark'] .wrap {
    background-color: #151515;
  }

  .full-box {
    padding: 0 20px;
  }

  .box {
    display: flex;
    height: calc(100% - 50px);

    .left-box {
      width: calc(100% - 350px);
      padding: 0 20px;
      height: 100vh;
    }

    .right-box {
      box-shadow: -6px 2px 4px rgb(0 0 0 / 10%);
      padding: 0 10px;
      width: 350px;

      .right {
        height: 100%;
      }
    }

    .fewer-panel-box {
      width: 20px;
      position: fixed;
      top: 60px;
      right: 5px;
      z-index: 3;
    }
  }

  :deep(.button-box) {
    display: flex;
    flex-direction: column;
  }

  :deep(.button-box button) {
    margin: 4px 0;
  }

  :deep(.clean-icon) {
    background-color: @clear-color;
    border-color: @clear-color;
  }
</style>
