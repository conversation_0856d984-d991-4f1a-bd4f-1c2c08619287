<template>
  <div id="clockingRecord">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.comment"
          placeholder="案例标题"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.accountName"
          placeholder="成交客户"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-range-picker
          v-model:value="searchForm.time"
          :placeholder="['开始时间', '结束时间']"
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.personName"
          placeholder="提交人"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />

        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          rowKey="id"
        >
          <template #comment="{ record }">
            <div class="multi-line-ellipsis" alt="record['comment']">{{record['comment']}}</div>
          </template>
          <template #action="{ record }">
            <a-button type="link" @click.stop="onAllot(record.id)">查看详情</a-button>
          </template>
          <template #index="{ record ,index}">
            <div>{{index + 1}}</div>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="550"
    v-model:visible="openModalVisible"
    :title="'详情'"
    :confirm-loading="false"
    :maskClosable="false"
    destroyOnClose
    :footer="null"
    centered
    @cancel="() => (openModalVisible = false)"
  >
    <div class="modal_box">
      <div style="display: flex;margin-bottom: 20px">
        <span class="img-title" style="margin-top: 0">
          案例标题:
        </span>
        <div style="font-weight: 500;margin-left: 5px;">
          {{weekDetail?.comment}}
        </div>
      </div>
      <h1>
        <span class="detail-title">
        成交客户:
        </span>
        {{weekDetail?.accountName}}</h1>
      <h1>
        <span class="detail-title">
        提交人:
        </span>
        {{weekDetail?.personName}}</h1>
      <h1>
        <span class="detail-title">
        提交时间:
        </span>
        {{weekDetail?.modifyDate}}</h1>
      <div style="display: flex;margin-bottom: 20px">
        <span class="img-title">
        成单时刻:
        </span>
        <div  class="img-box">
          <a-image class="img-content" v-for="(item, index) in weekDetail?.dealTimeFilePath" :key="index" :src="item" :width="120" />
        </div>
      </div>
      <div style="display: flex">
         <span class="img-title">
        成单小票:
        </span>
        <div class="img-box">
          <a-image  class="img-content" v-for="(item, index) in weekDetail?.dealReceiptFilePath" :key="index" :src="item" :width="120" />
        </div>
      </div>
    </div>
  </a-modal>
</template>


<script lang="ts" setup>
import cTable from '/@/views/components/Table/index.vue';
import { onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import { getCaseCollectDetail, getCaseCollectPageList } from '/@/api/groundPushManage/caseManager';

const searchForm = reactive({
  time: null,
  accountId:'',
  accountName: "",
  comment: "",
  departmentId: "",
  endTime: null,
  personName: "",
  limit: 0,
  order: "",
  size: 0,
  startTime: null,
});
const reSet = () => {
  searchForm.accountId = '';
  searchForm.accountName = '';
  searchForm.comment = '';
  searchForm.departmentId = '';
  searchForm.endTime = null;
  searchForm.personName = '';
  searchForm.time = null;
  searchForm.startTime = null
  getList();
};
const tableColumns = [
  {
    title: '序号',
    isHasIndex: true,
    key: 'No',
    align: 'center',
    width: 50,
  },
  {
    title: '案例标题',
    dataIndex: 'comment',
    key: 'comment',
    align: 'center',
    isSlot: true,
    width: 400
  },
  {
    title: '成交客户',
    dataIndex: 'accountName',
    key: 'accountName',
    align: 'center',
  },
  {
    title: '提交时间',
    dataIndex: 'modifyDate',
    key: 'modifyDate',
    align: 'center',
  },
  {
    title: '提交人',
    dataIndex: 'personName',
    key: 'personName',
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    isSlot: true,
    align: 'center',
    width: 200
  },
]
const tableData = reactive({
  data: [],
});
const loading = ref(false);
const pagination = reactive({
  currentPage: 1,
  totalItems: 500,
  pageSize: 10,
});
const openModalVisible = ref(false);
const weekDetail= ref<any>({})
// 获取表格数据
const getList = async (flag?: number) => {

  if (!flag) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
  }
  loading.value = true;
  tableData.data = [];
  try {
    let temp = {
      ...searchForm,
      startTime: searchForm.time?.[0]
        ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD') + ' 00:00:00'
        : '',
      endTime: searchForm.time?.[1]
        ? dayjs(searchForm.time?.[1]).format('YYYY-MM-DD') + ' 23:59:59'
        : '',
      // departmentId:searchForm.departList[searchForm.departList.length-1],
      limit: pagination.currentPage,
      size: pagination.pageSize,
    };
    let res = await getCaseCollectPageList(temp);
    console.log(res, 888)
    tableData.data = res?.list ?? [];
    pagination.totalItems = res.total ?? 0;
    loading.value = false;
  } catch (error) {
    console.log(error);
    loading.value = false;
  }
};

// 处理分页
const handlePaginationChange = (page: any) => {
  pagination.currentPage = page.current;
  pagination.pageSize = page.pageSize;
  getList(1);
};

const getAllotPersonList = async (val?: string) => {
  try {
    weekDetail.value = await getCaseCollectDetail({ id: val ?? '' })
    weekDetail.value.dealTimeFilePath = weekDetail.value.dealTimeFilePath.split(',')
    weekDetail.value.dealReceiptFilePath = weekDetail.value.dealReceiptFilePath.split(',')
  } catch (error) {
    console.log(error);
  }
};

const onAllot = (id) => {
  getAllotPersonList(id);
  openModalVisible.value = true;
};

onMounted(() => {
  getList();
});

</script>
<style scoped lang="less">
::v-deep{
  .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
    margin: 0px !important;
  }
}
#clockingRecord {
  width: 100%;
  height: 100%;
  padding: 8px;

  .targetMange_Box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    > div {
      width: 100%;
      padding: 16px;

      &:last-child {
        flex: 1;
        height: 0;
      }
    }

    .p_box {
      padding: 0 16px;
    }

    .type_top {
      width: 100%;
      padding: 16px 0 0 16px;

      .btn_group {
        display: inline-flex;

        > div {
          padding: 6px 40px;
          border: 1px solid #d9d9d9;
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          cursor: pointer;

          &:first-child {
            border-right: none;
            border-radius: 3px 0 0 3px;
          }

          &:last-child {
            border-left: none;
            border-radius: 0 3px 3px 0;
          }
        }

        .is_active {
          color: #fff;
        }
      }
    }
  }

  .filterForm_box {
    * + * {
      margin-left: 16px;
    }

    .right_btn {
      float: right;
    }
  }

  .table_box {
    .tipRed {
      color: #c10000;
    }
  }
}

.modal_box {
  padding: 16px;

  h1 {
    margin-bottom: 20px;
  }
  .form_box {
    margin-top: 10px;
  }

  :deep(.ant-form-item-explain-error) {
    display: none !important;
  }

  p {
    margin-bottom: 8px;
  }

  .p1 {
    font-size: 16px;
    line-height: 30px;
    margin-bottom: 16px;
    color: #000;

    span + span {
      margin-left: 120px;
    }
  }

  .p2 {
    font-size: 14px;
    color: #000;
    margin-bottom: 4px;
  }

  .p3 {
    color: rgba(0, 0, 0, 0.7);
    margin-bottom: 16px;
  }

  .p4 {
    color: rgba(0, 0, 0, 0.6);

    > span + span {
      margin-left: 16px;
    }

    .is_normal {
      color: rgba(0, 0, 0, 0.8);
    }

    .is_abnormal {
      color: #c10000;
    }
  }

  .p5 {
    padding-top: 8px;
    display: inline-flex;
    flex-wrap: wrap;
    gap: 12px;

    > img {
      width: 120px;
      height: 120px;
    }
    > video {
      width: 120px;
      height: 120px;
    }
  }

  .line {
    margin-left: 25px;
    width: 2px;
    height: 25px;
    background-color: rgba(0, 0, 0, 0.2);
    margin-bottom: 8px;
  }
  .detail-title {
    width: 80px;
    display: inline-block;
    text-align: right
  }
  .img-title {
    width: 80px;
    display: inline-block;
    text-align: right;
    flex-shrink: 0;
    margin-top: 5px;
    font-weight: 500
  }
  .img-box {
    display: flex;
    flex-wrap: wrap;
    ::v-deep {
      .ant-image {
        margin-left: 5px;
        margin-top: 10px;
      }
    }
  }
}
.multi-line-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 400px; /* 设置合适的宽度 */
}
</style>
