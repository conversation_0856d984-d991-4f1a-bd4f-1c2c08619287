<template>
  <div id="clockingRecord">
    <div class="targetMange_Box">
      <div class="filterForm_box">
        <a-cascader
          style="width: 300px"
          v-model:value="searchForm.departList"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          :options="cascaderOptions"
          change-on-select
          placeholder="省公司-主管区"
          @change="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.newName"
          placeholder="品种"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />

        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button class="right_btn" type="primary" @click="onImport">导入</a-button>
        <a-button class="right_btn" type="primary" @click="onAdd">新增</a-button>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
          rowKey="id"
        >
          <template #action="{ record }">
            <a-button type="link" @click.stop="onDel(record)">删除</a-button>
            <a-button type="link" @click.stop="onDetail(record)">查看详情</a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="550"
    v-model:visible="openModalVisible"
    :title="isEdit ? '查看详情' : '新增'"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openModalVisible = false)"
    :footer="isEdit ? null : undefined"
  >
    <div style="padding: 20px 20px 20px 0">
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :disabled="isEdit"
      >
        <template v-if="!isEdit">
          <a-form-item label="组织架构" name="departList">
            <a-cascader
              style="width: 100%"
              v-model:value="formState.departList"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              :options="cascaderOptions"
              change-on-select
              placeholder="省公司-主管区"
              :show-search="false"
              :disabled="isEdit"
            />
          </a-form-item>
        </template>
        <template v-else>
          <a-form-item label="组织架构" name="departList">
            <a-input v-model:value="formState.departListName" disabled />
          </a-form-item>
        </template>
        <a-form-item label="核心品种" name="newProductGroupId">
          <a-select
            v-model:value="formState.newProductGroupId"
            mode="multiple"
            style="width: 100%"
            placeholder="OTC品种"
            :options="groupOptions"
            :field-names="{ label: 'newName', value: 'newProductgroupId' }"
            allowClear
            :disabled="isEdit"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
  <!-- 导入 -->
  <a-modal
    :width="600"
    v-model:visible="importModalVisible"
    title="导入"
    :confirm-loading="importModalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @cancel="() => (importModalVisible = false)"
    :footer="null"
  >
    <div class="modal_box">
      <a-upload-dragger
        name="file"
        :multiple="false"
        accept=".xlsx, .xls"
        :showUploadList="false"
        action="#"
        :max-count="1"
        :fileList="fileList"
        :customRequest="customUpload"
        :headers="{ Authorization: `Bearer ${getToken()}` }"
      >
        <img src="/src/assets/images/import2.png" />
      </a-upload-dragger>
      <!-- <div style="text-align: center; margin-top: 20px">
        <a-button type="primary" @click="downloadTemplate">下载模板</a-button>
      </div> -->
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import {
    getPageProductGroupProvince,
    getProductGroupProvince,
    saveProductGroupProvince,
    deleteProductGroupProvince,
    pageProductGroupType,
    importProductGroupProvince,
  } from '/@/api/groundPushManage/varietyDetail';
  import { getDepartmentEnabledTree } from '/@/api/system/department';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  import { getToken } from '/@/utils/auth';
  import { getAppEnvConfig } from '/@/utils/env';
  import { useUserStore } from '/@/store/modules/user';

  const openModalVisible = ref<boolean>(false);
  const modalLoading = ref<boolean>(false);
  const importModalVisible = ref(false);
  const importModalLoading = ref(false);
  const fileList = ref<any[]>([]);
  const formState = reactive<any>({
    departList: [],
    newProductGroupId: [],
  });
  const isEdit = ref(false);
  const rules: Record<any, any> = {
    departList: [{ required: true, message: '请选择组织架构', trigger: 'change' }],
    newProductGroupId: [{ required: true, message: '请选择核心品种', trigger: 'change' }],
  };
  const groupOptions = ref([]);
  const getGroupOptions = async () => {
    try {
      let res = await pageProductGroupType();
      groupOptions.value = res.list ?? [];
    } catch (err) {
      console.log(err);
    }
  };
  // const changeCas = () => {
  //   getGroupOptions(formState.departList[formState.departList.length - 1]);
  // };
  const cascaderOptions = ref();
  const getDepartMent = async () => {
    try {
      let res = await getDepartmentEnabledTree();
      cascaderOptions.value = res ?? [];
    } catch (err) {
      console.log(err);
    }
  };
  const searchForm = reactive({
    newName: '',
    departList: [],
    provinceId: ''
  });
  const onAdd = () => {
    console.log('add');

    // 先重置表单数据
    Object.assign(formState, {
      departList: [],
      newProductGroupId: [],
      departListName: '',
    });

    isEdit.value = false;
    openModalVisible.value = true;
  };
  const reSet = () => {
    searchForm.newName = '';
    searchForm.departList = []
    getList();
  };
  const tableColumns = [
    {
      title: '省公司',
      dataIndex: 'parentProvinceName',
      key: 'parentProvinceName',
      width: 400,
      align: 'center',
    },
    {
      title: '主管区',
      dataIndex: 'provinceName',
      key: 'provinceName',
      align: 'center',
    },
    {
      title: '核心品种',
      dataIndex: 'newName',
      key: 'newName',
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'modifyDate',
      key: 'modifyDate',
      align: 'center',
    },
    {
      title: '更新人',
      dataIndex: 'modifyUserName',
      key: 'modifyUserName',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      isSlot: true,
      align: 'center',
      width: 200,
    },
  ];
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  const userStore = useUserStore();
  const userId = userStore.getUserInfo.id;

  const customUpload = (file) => {
    const formData = new FormData();
    formData.append('file', file.file);
    let action =
      getAppEnvConfig().VITE_GLOB_API_URL_PREFIX + '/business/Product/importProductGroupProvince?userId='+userId;
    const xhr = new XMLHttpRequest();
    xhr.open('post', action, true);
    xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
    xhr.onload = () => {
      const res = JSON.parse(xhr.response);
      console.log(res);
      if (res.code == 0) {
        notification.success({
          message: '提示',
          description: '导入成功',
        });
        importModalVisible.value = false;
        getList(); // 刷新列表
      } else {
        notification.error({
          message: '提示',
          description: res.msg,
        });
      }
    };
    xhr.onerror = () => {
      console.log('上传失败');
    };
    xhr.ontimeout = function timeout() {
      console.log('上传超时');
    };
    xhr.send(formData);
    // try {
    //   importModalLoading.value = true;
    //   targetImport(formData).then((res: any) => {
    //     if (res.code == 200) {
    //       notification.success({
    //         message: '提示',
    //         description: '导入成功',
    //       });
    //       importModalVisible.value = false;
    //       getList(1); // 刷新列表
    //     }
    //   });
    // } catch (error) {
    //   console.log(error);
    // } finally {
    //   importModalLoading.value = false;
    // }
  };
  const onImport = () => {
    importModalVisible.value = true;
  };
  // 处理导入确认
  const handleImportOk = () => {
    try {
      importModalLoading.value = true;
      // 处理导入逻辑
      // 这里可以调用 API 进行文件上传和数据处理
      const formData = new FormData();
      console.log(fileList.value);
      formData.append('file', fileList.value[0]);
      importProductGroupProvince(formData).then((res: any) => {
        if (res.code == 200) {
          notification.success({
            message: '提示',
            description: '导入成功',
          });
          importModalVisible.value = false;
          getList(); // 刷新列表
        }
      });
    } catch (error) {
      console.log(error);
    } finally {
      importModalLoading.value = false;
    }
  };

  // 获取表格数据
  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        newName: searchForm.newName,
        provinceId: searchForm.departList?.[searchForm.departList.length - 1] ?? '',
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getPageProductGroupProvince(temp);
      console.log(res, 888);
      tableData.data = res?.list ?? [];
      pagination.totalItems = res.total ?? 0;
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  const formRef = ref();
  const handleModalOk = () => {
    formRef.value
      .validate()
      .then(() => {
        modalLoading.value = true;
        // 获取选中的部门层级信息
        const selectedDepartList = formState.departList;

        // 根据cascader的层级结构获取对应的部门信息
        let provinceId = '';
        let provinceName = '';
        let parentProvinceId = '';
        let parentProvinceName = '';

        if (selectedDepartList && selectedDepartList.length > 0) {
          // 查找对应的部门信息
          const findDepartmentInfo = (options: any[], targetIds: any[], level = 0): boolean => {
            for (const option of options) {
              if (option.id === targetIds[level]) {
                if (level === 1) {
                  // 第一层是省公司
                  parentProvinceId = option.id;
                  parentProvinceName = option.name;
                } else if (level === 2) {
                  // 第二层是主管区
                  provinceId = option.id;
                  provinceName = option.name;
                }

                if (level < targetIds.length - 1 && option.children) {
                  return findDepartmentInfo(option.children, targetIds, level + 1);
                }
                return true;
              }
            }
            return false;
          };

          findDepartmentInfo(cascaderOptions.value || [], selectedDepartList);

          // 如果只选择了一层，则该层既是省公司也是主管区
          if (selectedDepartList.length === 1) {
            provinceId = parentProvinceId;
            provinceName = parentProvinceName;
          }
        }
        const a = groupOptions.value.filter((item: any) => {
          return formState.newProductGroupId.includes(item.newProductgroupId);
        });
        let products: Record<string, string>[] = [];

        a.forEach((item: any) => {
          let obj: Record<string, string> = {};
          obj[item.newProductgroupId] = item.newName;
          products.push(obj);
        });
        let tmp = {};
        debugger
        if (!provinceName) {
          tmp = {
            provinceId: parentProvinceId,
            products,
            provinceName: parentProvinceName,
            parentProvinceId: '1',
            parentProvinceName: 'OTC事业部',
            createUserId: userId,
          };
        } else {
          tmp = {
            provinceId: provinceId,
            products,
            provinceName: provinceName,
            parentProvinceId: parentProvinceId,
            parentProvinceName: parentProvinceName,
            createUserId: userId,
          };
        }

        saveProductGroupProvince(tmp)
          .then(() => {
            notification.success({
              message: '提示',
              description: '新增成功',
            });
            getList();
            openModalVisible.value = false;
            // 重置表单
            Object.assign(formState, {
              departList: [],
              newProductGroupId: [],
            });
          })
          .catch((error) => {
            console.log('保存失败:', error);
            notification.error({
              message: '提示',
              description: '新增失败，请重试',
            });
          })
          .finally(() => {
            modalLoading.value = false;
          });
      })
      .catch((error) => {
        console.log('表单验证失败:', error);
      });
  };
  const onDel = (record: any) => {
    try {
      deleteProductGroupProvince({ id: record.id }).then(() => {
        notification.success({
          message: '提示',
          description: '删除成功',
        });
        getList(1).then(() => {
          // 如果当前页没有数据且不是第一页，则跳转到上一页
          if (tableData.data.length === 0 && pagination.currentPage > 1) {
            pagination.currentPage--;
            getList(1);
          }
        });
      });
    } catch (err) {
      console.log(err);
    }
  };
  const onDetail = (record: any) => {
    getProductGroupProvince({ id: record.id }).then((res: any) => {
      // 填充表单数据用于查看详情
      if (res) {
        // 根据返回的数据构建departList
        let departList: any[] = [];
        if (res.parentProvinceId === '1') {
          departList = ['1', res.provinceId];
        } else {
          departList = ['1', res.parentProvinceId, res.provinceId];
        }
        // 重新赋值表单数据
        Object.assign(formState, {
          departList: departList,
          newProductGroupId: [res.newProductGroupId],
          departListName: res.parentProvinceName+'/' + res.provinceName,
        });
        isEdit.value = true;
        openModalVisible.value = true;
      }
    });
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  onMounted(() => {
    getList();
    getDepartMent();
    getGroupOptions();
  });
</script>
<style scoped lang="less">
  ::v-deep {
    .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
      margin: 0px !important;
    }
  }
  #clockingRecord {
    width: 100%;
    height: 100%;
    padding: 8px;

    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;

      > div {
        width: 100%;
        padding: 16px;

        &:last-child {
          flex: 1;
          height: 0;
        }
      }

      .p_box {
        padding: 0 16px;
      }

      .type_top {
        width: 100%;
        padding: 16px 0 0 16px;

        .btn_group {
          display: inline-flex;

          > div {
            padding: 6px 40px;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            cursor: pointer;

            &:first-child {
              border-right: none;
              border-radius: 3px 0 0 3px;
            }

            &:last-child {
              border-left: none;
              border-radius: 0 3px 3px 0;
            }
          }

          .is_active {
            color: #fff;
          }
        }
      }
    }

    .filterForm_box {
      * + * {
        margin-left: 16px;
      }

      .right_btn {
        float: right;
      }
    }

    .table_box {
      .tipRed {
        color: #c10000;
      }
    }
  }

  .modal_box {
    padding: 16px;

    .form_box {
      margin-top: 10px;
    }

    :deep(.ant-form-item-explain-error) {
      display: none !important;
    }

    p {
      margin-bottom: 8px;
    }

    .p1 {
      font-size: 16px;
      line-height: 30px;
      margin-bottom: 16px;
      color: #000;

      span + span {
        margin-left: 120px;
      }
    }

    .p2 {
      font-size: 14px;
      color: #000;
      margin-bottom: 4px;
    }

    .p3 {
      color: rgba(0, 0, 0, 0.7);
      margin-bottom: 16px;
    }

    .p4 {
      color: rgba(0, 0, 0, 0.6);

      > span + span {
        margin-left: 16px;
      }

      .is_normal {
        color: rgba(0, 0, 0, 0.8);
      }

      .is_abnormal {
        color: #c10000;
      }
    }

    .p5 {
      padding-top: 8px;
      display: inline-flex;
      flex-wrap: wrap;
      gap: 12px;

      > img {
        width: 120px;
        height: 120px;
      }
      > video {
        width: 120px;
        height: 120px;
      }
    }

    .line {
      margin-left: 25px;
      width: 2px;
      height: 25px;
      background-color: rgba(0, 0, 0, 0.2);
      margin-bottom: 8px;
    }
  }
</style>
