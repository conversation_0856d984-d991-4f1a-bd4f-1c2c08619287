<template>
  <div class="header-box box-shadow bg-white dark:bg-dark-900">
    <div class="left">
      <DesignLogo />
      <div class="header-title">{{ title }}</div>
    </div>
    <div class="step-box"><slot name="steps"></slot></div>
    <div class="button-box">
      <slot name="buttons"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { DesignLogo } from '/@/components/ModalPanel/index';
  withDefaults(
    defineProps<{
      title: string;
    }>(),
    {
      title: '',
    },
  );
</script>

<style lang="less" scoped>
  .header-box {
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .left {
    display: flex;
    align-items: center;
  }

  .header-title {
    font-size: 16px;
    color: #333;
    margin-right: 10px;
  }

  [data-theme='dark'] .header-title {
    color: #fff;
  }

  .step-box {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .button-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    margin-right: 20px;
  }
</style>
