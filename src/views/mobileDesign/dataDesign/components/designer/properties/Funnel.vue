<template>
  <!-- 漏斗图 -->
  <Box v-if="data.show">
    <a-collapse v-model:activeKey="activeKey" ghost expandIconPosition="right">
      <a-collapse-panel key="1" :header="t('基础配置')">
        <a-form-item :label="t('标题')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.title" />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="2" :header="t('数据配置')">
        <a-form-item :label="t('数据视图')" :colon="false" labelAlign="left">
          <ApiSelect
            v-model:value="data.info.config.apiConfig"
            :exampleStr="exampleStr"
            @save="saveApiConfig"
          />
        </a-form-item>
        <a-form-item :label="t('维度')" :colon="false" labelAlign="left">
          <BindApiColumns
            v-model:value="data.info.config.labelKey"
            :apiColumns="data.info.config.apiColumns"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('指标')" :colon="false" labelAlign="left">
          <BindApiColumns
            v-model:value="data.info.config.valueKey"
            :apiColumns="data.info.config.apiColumns"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="3" :header="t('漏斗图设置')">
        <!-- <a-form-item :label="t('最小值')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.echarts.series[0].min"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('最大值')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.echarts.series[0].max"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('最小尺寸')" :colon="false" labelAlign="left">
          <a-input
            v-model:value="data.info.config.echarts.series[0].minSize"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('最大尺寸')" :colon="false" labelAlign="left">
          <a-input
            v-model:value="data.info.config.echarts.series[0].maxSize"
            @change="resetDisplay"
          />
        </a-form-item> -->
        <a-form-item :label="t('右边距')" :colon="false" labelAlign="left">
          <a-input
            v-model:value="data.info.config.echarts.series[0].right"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('左边距')" :colon="false" labelAlign="left">
          <a-input v-model:value="data.info.config.echarts.series[0].left" @change="resetDisplay" />
        </a-form-item>
        <a-form-item :label="t('上边距')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.echarts.series[0].top"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('下边距')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.echarts.series[0].bottom"
            @change="resetDisplay"
          />
        </a-form-item>
        <a-form-item :label="t('数据排序')" :colon="false" labelAlign="left">
          <a-select
            v-model:value="data.info.config.echarts.series[0].sort"
            style="width: 100%"
            @change="resetDisplay"
          >
            <a-select-option value="ascending">{{ t('正序') }}</a-select-option>
            <a-select-option value="descending">{{ t('倒序') }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="t('数据间距')" :colon="false" labelAlign="left">
          <a-input-number
            v-model:value="data.info.config.echarts.series[0].gap"
            @change="resetDisplay"
          />
        </a-form-item>
      </a-collapse-panel>
      <a-collapse-panel key="4" :header="t('文本标签')">
        <Label
          v-model:label="data.info.config.echarts.series[0].label"
          :formatterText="formatterText"
          :positionOptions="[
            {
              label: t('左侧'),
              value: 'left',
            },
            {
              label: t('右侧'),
              value: 'right',
            },
          ]"
          @change="resetDisplay"
        />
      </a-collapse-panel>
      <a-collapse-panel key="5" :header="t('图例设置')">
        <Legend
          v-model:legend="data.info.config.echarts.legend"
          :formatterText="legendFormatterText"
          v-model:autoWidth="data.info.config.autoWidth"
          @change="resetDisplay"
        />
      </a-collapse-panel>
      <a-collapse-panel key="6" :header="t('配色设置')">
        <Colors v-model:info="data.info" @change="resetDisplay" />
      </a-collapse-panel>
      <a-collapse-panel key="7" :header="t('大小定位')">
        <Location v-model:info="data.info.h" />
      </a-collapse-panel>
    </a-collapse>
  </Box>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from 'vue';
  import Box from './Box.vue';
  import { chartBarInfo } from '../config/info';
  import { ChartBarInfo } from '/@/model/mobileDesign/designer';
  import Label from './collapse/Label.vue';
  import Legend from './collapse/Legend.vue';
  import BindApiColumns from './collapse/BindApiColumns.vue';
  import ApiSelect from './ApiSelect.vue';
  import Location from './collapse/Location.vue';
  import Colors from './collapse/Colors.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = withDefaults(
    defineProps<{
      info: ChartBarInfo;
    }>(),
    {
      info: () => {
        return chartBarInfo;
      },
    },
  );
  watch(
    () => props.info,
    (val: ChartBarInfo) => {
      if (val) data.info = val;
    },
    {
      deep: true,
    },
  );
  const data: {
    show: boolean;
    info: any;
  } = reactive({
    show: false,
    info: chartBarInfo,
  });
  const activeKey = ref(['1', '2', '3']);
  onMounted(() => {
    data.info = props.info;
    data.show = true;
  });
  function saveApiConfig() {
    resetApiData();
    resetDisplay();
  }
  function resetApiData() {
    data.info.config.apiData = [];
    data.info.config.apiColumns = [];
    data.info.config.labelKey = '';
    data.info.config.valueKey = '';
    data.info.config.echarts.legend.data = [];
    data.info.config.echarts.series[0].data = [];
  }
  function resetDisplay() {
    if (data.info.config.renderKey >= 0) {
      data.info.config.renderKey++;
    }
  }
  const exampleStr = ` {
      code: 0,
      msg: 'success',
      data: {
        columns: [
          {prop:'name',label:'测试1'},
          {prop:'value',label:'测试2'}
        ],
        list: [
          {name:'demo1',value:10},
          {name:'demo2',value:30}
        ],
      },
    }`;

  const formatterText = `参数说明
  
{b}：数据名。

{c}：数据值。

{d}：百分比。
 `;
  const legendFormatterText = `参数说明
{name}：数据名。

{value}：数据值。`;
</script>

<style lang="less" scoped></style>
