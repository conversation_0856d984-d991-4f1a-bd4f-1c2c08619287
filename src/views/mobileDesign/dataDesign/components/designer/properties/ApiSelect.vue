<template>
  <SelectApiConfig v-model="data.config" :exampleStr="props.exampleStr" @update:modelValue="save">
    <InputModel
      :value="data.config.name"
      :placeholder="t('点击绑定API')"
      style="width: 100%; min-width: 100px"
    />
  </SelectApiConfig>
</template>

<script setup lang="ts">
  import { SelectApiConfig, InputModel } from '/@/components/ApiConfig';
  import { ApiConfig } from '/@/components/ApiConfig/src/interface';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { watch, reactive, onMounted } from 'vue';
  const { t } = useI18n();
  let emits = defineEmits(['save', 'update:value']);
  let props = withDefaults(defineProps<{ value: ApiConfig; exampleStr: string }>(), {
    value: () => {
      return {
        id: '',
        name: '',
        method: '',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      };
    },
    exampleStr: `{
    code: 0,
    msg: 'success',
    data: 'value',
  }`,
  });
  let data: {
    config: ApiConfig;
  } = reactive({
    config: {
      id: '',
      name: '',
      method: '',
      requestParamsConfigs: [], //Query Params
      requestHeaderConfigs: [], //Header
      requestBodyConfigs: [], //Body
    },
  });
  watch(
    () => props.value,
    (val) => {
      if (val) data.config = props.value;
    },
    {
      deep: true,
    },
  );
  onMounted(() => {
    data.config = props.value;
  });
  function save(val) {
    emits('save', val);
    emits('update:value', val);
  }
</script>

<style scoped></style>
