<template>
  <PageWrapper dense contentFullHeight fixed-height>
    <BasicTable @register="registerTable" isMenuTable>
      <template #toolbar>
        <div>
          <a-button type="primary" v-auth="'functionDesign:add'" @click="handleCreate">
            {{ t('新增') }}
          </a-button>
        </div>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: record.enabledMark === 1 ? 'jinyong|svg' : 'qiyong|svg',
              auth: 'functionDesign:disabled',
              tooltip: record.enabledMark === 1 ? '禁用' : '启用',
              ifShow: record.enabledMark !== -1,
              onClick: handleEnable.bind(null, record),
            },
            {
              icon: 'clarity:note-edit-line',
              auth: 'functionDesign:edit',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              auth: 'functionDesign:delete',
              onClick: handleDelete.bind(null, record),
            },
          ]"
        />
      </template>
    </BasicTable>
    <DataDesign
      v-if="dialogvisible"
      @register="registerDesignModal"
      @success="reload"
      @close="handleClose"
    />
  </PageWrapper>
</template>
<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, FormSchema, BasicColumn } from '/@/components/Table';

  import { PageWrapper } from '/@/components/Page';
  import { useMessage } from '/@/hooks/web/useMessage';
  import DataDesign from './components/FuncDesign.vue';
  import { useModal } from '/@/components/Modal';
  import { h, ref, createVNode } from 'vue';

  import { Modal, Tag } from 'ant-design-vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  import { deleteMobileFuncData, getFuncPageList, updateFuncEnableMark } from '/@/api/mobileDesign';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { t } = useI18n();
  const dialogvisible = ref(true);
  const searchFormSchema: FormSchema[] = [
    {
      field: 'keyword',
      label: t('关键字'),
      component: 'Input',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请输入功能名称'),
      },
    },
    {
      field: 'formType',
      label: t('功能类型'),
      component: 'Select',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请选择功能类型'),
        options: [
          { label: '系统页面', value: 0 },
          { label: '自定义页面', value: 1 },
        ],
      },
    },
    {
      field: 'enabledMark',
      label: t('页面状态'),
      component: 'Select',
      colProps: { span: 8 },
      componentProps: {
        placeholder: t('请选择页面状态'),
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
          { label: '草稿', value: -1 },
        ],
      },
    },
  ];

  const columns: BasicColumn[] = [
    {
      dataIndex: 'name',
      title: t('功能名称'),
      align: 'left',
      resizable: true,
    },

    {
      dataIndex: 'funcDescribe',
      title: t('功能描述'),
      align: 'left',
      resizable: true,
    },
    {
      dataIndex: 'formType',
      title: t('功能类型'),
      align: 'left',
      resizable: true,
      customRender: ({ record }) => {
        const color = record.formType == 1 ? 'blue' : 'yellow';
        const text = record.formType == 1 ? '自定义表单' : '系统表单';
        return h(Tag, { color: color }, () => text);
      },
    },
    {
      dataIndex: 'enabledMark',
      title: t('状态'),
      align: 'left',
      resizable: true,
      customRender: ({ record }) => {
        const color =
          record.enabledMark == 1 ? 'blue' : record.enabledMark == -1 ? 'yellow' : 'red';
        const text = record.enabledMark == 1 ? '正常' : record.enabledMark == -1 ? '草稿' : '停用';
        return h(Tag, { color: color }, () => text);
      },
    },
    {
      dataIndex: 'menuRemark',
      title: t('备注'),
      width: 120,
      align: 'left',
      resizable: true,
    },
  ];
  const { notification } = useMessage();
  const { hasPermission } = usePermission();

  const [registerDesignModal, { openModal }] = useModal();
  const [registerTable, { reload }] = useTable({
    title: t('功能页列表'),
    api: getFuncPageList,
    rowKey: 'id',
    columns,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: searchFormSchema,
      fieldMapToTime: [],
      showResetButton: false,
    },
    useSearchForm: true,
    showTableSetting: true,
    striped: false,
    actionColumn: {
      width: 120,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
    tableSetting: {
      size: false,
    },
    customRow: (record) => {
      return {
        ondblclick: () => {
          if (hasPermission('functionDesign:edit')) {
            handleEdit(record);
          }
        },
      };
    },
  });

  function handleCreate() {
    openModal(true, {
      title: t('添加功能'),
    });
  }

  function handleEdit(record: Recordable) {
    openModal(true, {
      title: t('编辑功能'),
      isUpdate: true,
      id: record.id,
      appMenuId: record.appMenuId,
      enabledMark: record.enabledMark,
      undoAble: true,
    });
  }

  function handleDelete(record: Recordable = {}) {
    const ids = record.id;

    Modal.confirm({
      title: t('提示'),
      icon: createVNode(ExclamationCircleOutlined),
      content: t('确定要删除所选项吗？'),
      onOk() {
        deleteMobileFuncData(ids).then(() => {
          reload();
          notification.success({
            message: t('提示'),
            description: t('删除成功'),
          });
        });
      },
      onCancel() {},
      okText: t('确认'),
      cancelText: t('取消'),
    });
  }

  async function handleEnable(record) {
    let res = await updateFuncEnableMark({
      id: record.id,
      enabledMark: record.enabledMark == 1 ? 0 : 1,
    });
    if (res) {
      notification.success({
        message: t('提示'),
        description: t('修改状态成功'),
      });
      reload();
    } else {
      notification.error({
        message: t('提示'),
        description: t('修改状态失败'),
      });
    }
  }

  function handleClose() {
    dialogvisible.value = !dialogvisible.value;
    setTimeout(() => {
      dialogvisible.value = !dialogvisible.value;
    }, 100);
  }
</script>
<style lang="less" scoped></style>
