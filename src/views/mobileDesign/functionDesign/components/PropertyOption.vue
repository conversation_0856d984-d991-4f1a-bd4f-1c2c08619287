<template>
  <div class="awc-containter">
    <a-form v-bind="layout" v-if="data" :key="data.key" size="small">
      <template v-if="designType === 'data'">
        <a-form-item
          :label="t('绑定表')"
          v-if="!noHaveTableAndField.includes(data.type) && !data.isSubFormChild"
        >
          <a-select :value="data.bindTable" size="mini" :placeholder="t('请选择数据表')" disabled>
            <a-select-option
              v-for="(table, idx) in tableInfo"
              :disabled="table.disabled"
              :value="table.name"
              :key="idx"
            >
              {{ table.name }}
              <span>
                <a-tag :color="table.isMain ? 'blue' : 'orange'">
                  {{ table.isMain ? t('主表') : t('附表') }}
                </a-tag>
              </span>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="t('绑定字段')" v-if="!noHaveField.includes(data.type)">
          <a-select :value="data.bindField" size="mini" :placeholder="t('请选择表字段')" disabled>
            <a-select-option v-for="(field, idx) in fieldsInfo" :value="field.name" :key="idx">
              {{ field.name }}
              <span>
                <a-tag color="blue">
                  {{ getFieldType(field.type) }}
                </a-tag>
              </span>
            </a-select-option>
          </a-select>
        </a-form-item>
      </template>

      <template v-if="data.type === 'form'">
        <a-form-item :label="t('预加载数据')">
          <a-radio-group
            button-style="solid"
            :value="data.options.preloadType"
            size="small"
            disabled
          >
            <a-radio-button value="api">API</a-radio-button>
            <a-radio-button value="dic">{{ t('数据字典') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item :label="t('接口配置')" v-if="data.options.preloadType === 'api'">
          <a-input
            :value="data.options.apiConfig.path"
            disabled
            :placeholder="t('点击进行接口配置')"
          >
            <template #suffix>
              <Icon icon="ant-design:ellipsis-outlined" />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item :label="t('数据选项')" v-if="data.options.preloadType === 'dic'">
          <DicTreeSelect :value="data.options.itemId" @change="handleDicChange" disabled />
        </a-form-item>

        <a-form-item :label="t('按钮选数据')">
          <a-switch :checked="data.options.useSelectButton" disabled />
        </a-form-item>

        <template v-if="data.options.useSelectButton">
          <a-form-item :label="t('按钮名称')">
            <a-input :value="data.options.buttonName" :placeholder="t('请填写按钮名称')" disabled />
          </a-form-item>

          <a-form-item :label="t('表头配置')">
            <a-input
              :value="configText"
              :placeholder="t('点击进行表头配置')"
              @click="tableTitleClick"
            >
              <template #suffix>
                <Icon icon="ant-design:ellipsis-outlined" />
              </template>
            </a-input>
          </a-form-item>
        </template>

        <template v-if="data.options.preloadType">
          <a-form-item
            :label="item.label"
            v-for="item in data.children.filter((x) => !unPreloadComponents.includes(x.type))"
            :key="item.key"
          >
            <a-input
              v-if="data.options.preloadType === 'api'"
              :value="item.options.prestrainField"
              disabled
              :placeholder="t('请填写接口出参字段')"
            />
            <a-select
              v-else-if="data.options.preloadType === 'dic'"
              :value="item.options.prestrainField"
              disabled
            >
              <a-select-option value="name">name</a-select-option>
              <a-select-option value="value">value</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </template>
      <template
        v-if="
          data.type === 'multiple-popup' ||
          data.type === 'associate-popup' ||
          data.type === 'associate-select' ||
          data.type === 'select' ||
          data.type === 'checkbox' ||
          data.type === 'radio'
        "
      >
        <DataSourceSetting :data="data" disabled />
      </template>

      <a-form-item v-if="hasKey('showTime') && designType === 'data'" :label="t('开始字段')">
        <a-select
          :value="data.bindStartTime"
          size="mini"
          :placeholder="t('请选择开始字段')"
          disabled
        >
          <a-select-option v-for="(field, idx) in fieldsInfo" :value="field.name" :key="idx">
            {{ field.name }}
            <span>
              <a-tag color="blue">
                {{ getFieldType(field.type) }}
              </a-tag>
            </span>
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item v-if="hasKey('showTime') && designType === 'data'" :label="t('结束字段')">
        <a-select :value="data.bindEndTime" size="mini" :placeholder="t('请选择结束字段')" disabled>
          <a-select-option v-for="(field, idx) in fieldsInfo" :value="field.name" :key="idx">
            {{ field.name }}
            <span>
              <a-tag color="blue">
                {{ getFieldType(field.type) }}
              </a-tag>
            </span>
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="t('级联配置')" v-if="data.type === 'cascader'" required>
        <a-input :value="data.options.apiConfig.path" :placeholder="t('点击进行级联配置')" disabled>
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item v-if="hasKey('showFormat')" :label="t('显示格式')">
        <a-select :value="data.options.showFormat" :options="formatOptions" disabled />
      </a-form-item>
      <a-form-item v-if="hasKey('separator')" :label="t('分隔符')">
        <a-select v-model:value="data.options.separator" :options="separatorOptions" />
      </a-form-item>
      <a-form-item v-if="hasKey('selectedConfig')" :label="t('选择设置')">
        <a-select :value="data.options.selectedConfig" :options="selectedOptions" disabled />
      </a-form-item>

      <a-form-item :label="t('标题')" v-if="!noHaveTitle.includes(data.type)">
        <a-input v-model:value="data.label" size="mini" />
      </a-form-item>
      <a-form-item :label="t('标题')" v-if="data.type === 'card'">
        <a-input v-model:value="data.options.title" size="mini" />
      </a-form-item>
      <a-form-item v-if="hasKey('showTime')" :label="t('开始占位')">
        <a-input v-model:value="data.options.startTimePlaceholder" />
      </a-form-item>

      <a-form-item v-if="hasKey('showTime')" :label="t('结束占位')">
        <a-input v-model:value="data.options.endTimePlaceholder" />
      </a-form-item>

      <a-form-item :label="t('占位提示')" v-if="hasKey('placeholder')">
        <a-textarea
          v-if="data.type === 'opinion'"
          v-model:value="data.options.placeholder"
          :placeholder="t('请输入占位提示')"
        />
        <a-input
          v-else
          v-model:value="data.options.placeholder"
          :placeholder="t('请输入占位提示')"
        />
      </a-form-item>

      <a-form-item
        :label="t('默认值')"
        v-if="
          hasKey('defaultValue') &&
          (data.type === 'input' ||
            data.type === 'password' ||
            data.type === 'textarea' ||
            data.type === 'text' ||
            data.type === 'rate' ||
            data.type === 'switch' ||
            data.type === 'slider' ||
            data.type === 'number' ||
            data.type === 'picker-color' ||
            data.type === 'money-chinese' ||
            data.type === 'computational')
        "
      >
        <a-textarea
          v-if="data.type === 'textarea'"
          :value="data.options.defaultValue"
          disabled
          :placeholder="t('请输入默认值')"
        />
        <a-rate
          v-else-if="data.type === 'rate'"
          :value="data.options.defaultValue"
          :count="data.options.count"
          :allowHalf="data.options.allowHalf"
          :allowClear="data.options.allowClear"
          disabled
        />
        <ColorPicker
          v-else-if="data.type === 'picker-color'"
          :value="data.options.defaultValue"
          disabled
        />
        <a-input-number
          v-else-if="
            data.type === 'money-chinese' || data.type === 'computational' || data.type === 'number'
          "
          :value="data.options.defaultValue"
          disabled
          :placeholder="t('请输入默认值')"
        />
        <a-switch
          v-else-if="data.type === 'switch'"
          :checked="data.options.defaultValue"
          :checkedValue="1"
          disabled
          :unCheckedValue="0"
        />
        <template v-else-if="data.type === 'slider'">
          <a-input-number v-if="!data.options.range" :value="data.options.defaultValue" disabled />
          <template v-if="data.options.range">
            <a-input-number
              :value="data.options.defaultValue[0]"
              :max="data.options.max"
              disabled
            />
            <a-input-number
              :value="data.options.defaultValue[1]"
              :max="data.options.max"
              disabled
            />
          </template>
        </template>
        <a-input
          v-else
          :value="data.options.defaultValue"
          disabled
          @blur="data.type === 'number' ? changeNumber : ''"
          :placeholder="t('请输入默认值')"
        />
      </a-form-item>

      <a-form-item
        v-if="hasKey('format') && (data.type === 'time' || data.type === 'time-range')"
        :label="t('时间格式')"
      >
        <a-select :value="data.options.format" disabled>
          <a-select-option value="HH:mm:ss" />
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="hasKey('format') && (data.type === 'date' || data.type === 'date-range')"
        :label="t('日期格式')"
      >
        <a-select :value="data.options.format" disabled @change="formatChange">
          <a-select-option value="YYYY-MM-DD HH:mm:ss" />
          <a-select-option value="YYYY-MM-DD" />
          <a-select-option value="YYYY-MM" />
          <a-select-option value="YYYY" />
        </a-select>
      </a-form-item>

      <template v-if="data.type === 'time'">
        <a-form-item :label="t('默认值')">
          <TimePicker
            style="width: 100%"
            :value="data.options.defaultValue"
            :format="data.options.format"
            disabled
            :placeholder="data.options.placeholder"
          />
        </a-form-item>
      </template>

      <template v-if="data.type === 'date'">
        <a-form-item :label="t('默认值')">
          <XjrDatePicker
            style="width: 100%"
            :value="data.options.defaultValue"
            :format="data.options.format"
            disabled
            :placeholder="data.options.placeholder"
          />
        </a-form-item>
      </template>

      <a-form-item :label="t('编码规则')" v-if="hasKey('autoCodeRule')" required>
        <a-select
          :value="data.options.autoCodeRule"
          size="mini"
          :placeholder="t('请选择编码规则')"
          disabled
        >
          <a-select-option v-for="rule in codeRuleOptions" :value="rule.code" :key="rule.id">
            {{ rule.name }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template v-if="data.type === 'computational' || data.type === 'money-chinese'">
        <a-form-item :label="t('计算式配置')">
          <a-input :value="data.options.computationalConfigValue" readonly />
        </a-form-item>
        <!-- <ComputationalSetting
          v-if="computationalDialog"
          v-model:computationalConfig="data.options.computationalConfig"
          @setComputationalConfigValue="(val) => (data.options.computationalConfigValue = val)"
          v-model:computationalDialog="computationalDialog"
        /> -->
      </template>

      <!-- <a-form-item :label="t('标签宽度')" v-if="hasKey('labelWidth')">
        <a-input-number v-model:value="data.options.labelWidth" :min="0" />
      </a-form-item> -->

      <a-form-item :label="t('组件宽度')" v-if="hasKey('width')">
        <a-input value="100%" disabled />
      </a-form-item>

      <a-form-item :label="t('信息类型')" v-if="hasKey('infoType')">
        <a-select :value="data.options.infoType" size="mini" disabled>
          <a-select-option :value="0">{{ t('当前用户') }}</a-select-option>
          <a-select-option :value="1">{{ t('当前组织') }}</a-select-option>
          <a-select-option :value="2">{{ t('当前时间') }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item :label="t('组件类型')" v-if="hasKey('orgzType')">
        <a-select :value="data.options.orgzType" size="mini" disabled>
          <a-select-option :value="0">{{ t('系统组织架构') }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item :label="t('组件类型')" v-if="hasKey('userType')">
        <a-select :value="data.options.userType" size="mini" disabled>
          <a-select-option :value="0">{{ t('系统用户') }}</a-select-option>
        </a-select>
      </a-form-item>

      <template v-if="data.type === 'signature'">
        <a-form-item :label="t('关联组件')">
          <a-select
            :options="SignSelectOption"
            mode="multiple"
            :placeholder="t('请选择关联组件')"
            :allowClear="true"
            v-model:value="data.options.associateComponents"
            disabled
          />
        </a-form-item>
        <a-form-item :label="t('默认值')">
          <SelectUser
            disabled
            :multiple="true"
            :selectedIds="data.options.defaultValue"
            @change="
              (ids) => {
                data.options.defaultValue = ids;
              }
            "
            @change-names="
              (names) => {
                userNames = names;
              }
            "
          >
            <a-input disabled placeholder="请选择默认值" v-model:value="userNames" />
          </SelectUser>
        </a-form-item>
      </template>

      <a-form-item v-if="hasKey('loadAgain')">
        <template #label>
          <a-tooltip>
            <template #title> 二次加载是决定编辑状态是否重新获取信息体数据 </template>
            <Icon icon="ant-design:question-circle-outlined" color="#909399" />
          </a-tooltip>
          &nbsp;{{ t('二次加载') }}
        </template>
        <a-switch :checked="data.options.loadAgain" disabled />
      </a-form-item>

      <template v-if="data.type === 'button'">
        <a-form-item :label="t('按钮名称')">
          <a-input v-model:value="data.options.name" />
        </a-form-item>
        <template v-if="data.options.isSpecial">
          <a-form-item :label="t('按钮类型')" required>
            <a-select
              v-model:value="data.options.buttonType"
              size="mini"
              disabled
              :placeholder="t('请选择按钮类型')"
            >
              <a-select-option :value="1">{{ t('联想弹层按钮') }}</a-select-option>
              <a-select-option :value="2">{{ t('表格选择数据按钮') }}</a-select-option>
            </a-select>
          </a-form-item>
          <template v-if="data.options.buttonType">
            <DataSourceSetting v-model:data="data" disabled />
          </template>
          <template v-if="data.options.buttonType == 2">
            <a-form-item :label="t('表格选择')" required>
              <a-select
                v-model:value="data.options.selectTable"
                size="mini"
                disabled
                :placeholder="t('请选择表格')"
              >
                <a-select-option
                  v-for="item in buttonTableOptions"
                  :value="item.key"
                  :key="item.key"
                  >{{ item.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
            <a-form-item :label="t('表头配置')">
              <a-input
                :value="configText"
                :placeholder="t('点击进行表头配置')"
                @click="tableTitleClick"
              >
                <template #suffix>
                  <Icon icon="ant-design:ellipsis-outlined" />
                </template>
              </a-input>
            </a-form-item>
            <template v-if="data.options.selectTable">
              <a-form-item
                :label="item.label"
                v-for="item in data.options.tableColumns.filter(
                  (x) => !unPreloadComponents.includes(x.type),
                )"
                :key="item.key"
              >
                <a-input
                  v-if="data.options.datasourceType === 'api'"
                  disabled
                  v-model:value="item.prestrainField"
                  :placeholder="t('请填写接口出参字段')"
                />
                <a-select
                  v-else-if="data.options.datasourceType === 'dic'"
                  disabled
                  v-model:value="item.prestrainField"
                >
                  <a-select-option value="name">name</a-select-option>
                  <a-select-option value="value">value</a-select-option>
                </a-select>
              </a-form-item>
            </template>
          </template>
        </template>
        <a-form-item :label="t('按钮宽度')">
          <a-input
            v-model:value="data.options.buttonWidth"
            :placeholder="t('请填写按钮宽度，不填则默认')"
          />
        </a-form-item>
        <a-form-item :label="t('按钮高度')">
          <a-input
            v-model:value="data.options.buttonHeight"
            :placeholder="t('请填写按钮高度，不填则默认')"
          />
        </a-form-item>
      </template>

      <a-form-item :label="t('最大值')" v-if="hasKey('max')">
        <a-input-number :value="data.options.max" disabled />
      </a-form-item>

      <a-form-item :label="t('最大值')" v-if="hasKey('count')">
        <a-input-number :value="data.options.count" disabled />
      </a-form-item>

      <a-form-item :label="t('最小值')" v-if="hasKey('min')">
        <a-input-number :value="data.options.min" disabled />
      </a-form-item>

      <a-form-item :label="t('步长')" v-if="hasKey('step')">
        <a-input-number :value="data.options.step" :min="0" disabled />
      </a-form-item>

      <a-form-item :label="t('前置标签')" v-if="hasKey('addonBefore')">
        <a-input v-model:value="data.options.addonBefore" :placeholder="t('请输入前置标签')" />
      </a-form-item>

      <a-form-item :label="t('后置标签')" v-if="hasKey('addonAfter')">
        <a-input v-model:value="data.options.addonAfter" :placeholder="t('请输入后置标签')" />
      </a-form-item>

      <a-form-item :label="t('前置图标')" v-if="hasKey('prefix')">
        <IconPicker v-model:value="data.options.prefix" :disabled="false" :readonly="false" />
      </a-form-item>

      <a-form-item :label="t('后置图标')" v-if="hasKey('suffix')">
        <IconPicker v-model:value="data.options.suffix" :disabled="false" :readonly="false" />
      </a-form-item>

      <a-form-item :label="t('最大长度')" v-if="hasKey('maxlength')">
        <a-input
          v-model:value.number="data.options.maxlength"
          :placeholder="t('请输入最大长度，按字符计算')"
        />
      </a-form-item>

      <a-form-item :label="t('开启提示')" v-if="hasKey('checkedChildren')">
        <a-input v-model:value="data.options.checkedChildren" />
      </a-form-item>

      <a-form-item :label="t('关闭提示')" v-if="hasKey('unCheckedChildren')">
        <a-input v-model:value="data.options.unCheckedChildren" />
      </a-form-item>

      <a-form-item :label="t('开启颜色')" v-if="hasKey('checkedColor')">
        <ColorPicker v-model:value="data.options.checkedColor" />
      </a-form-item>

      <a-form-item :label="t('关闭颜色')" v-if="hasKey('unCheckedColor')">
        <ColorPicker v-model:value="data.options.unCheckedColor" />
      </a-form-item>

      <a-form-item :label="t('地址字段')" v-if="hasKey('address')">
        <a-select :value="data.options.address" size="mini" disabled>
          <a-select-option v-for="it in mapComps" :key="it.model" :value="it.bindField">{{
            it.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="t('经纬度字段')" v-if="hasKey('latiAndLong')">
        <a-select :value="data.options.latiAndLong" size="mini" disabled>
          <a-select-option v-for="it in mapComps" :key="it.model" :value="it.bindField">{{
            it.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :label="t('配置类型')" v-if="hasKey('codeType')">
        <a-select
          v-model:value="data.options.codeType"
          size="mini"
          :placeholder="t('请选择配置类型')"
          @change="handleChangeCode"
        >
          <a-select-option value="fixed" key="fixed">
            {{ data.type === 'qrcode' ? t('固定二维码') : t('固定一维码') }}
          </a-select-option>
          <a-select-option value="api" key="api"> {{ t('接口配置') }} </a-select-option>
        </a-select>
      </a-form-item>
      <!-- 树形选择组件 配置 -->
      <a-form-item :label="t('选择树')" v-if="data.type == 'tree-select-component'">
        <a-select
          v-model:value="data.options!.treeConfig.id"
          style="width: 100%"
          :options="treeSelectOption"
          :field-names="{ label: 'name', value: 'id', options: 'children' }"
          disabled
        />
      </a-form-item>
      <a-form-item :label="t('允许多选')" v-if="data.type == 'tree-select-component'">
        <a-switch :checked="data.options.treeConfig.isMultiple" disabled />
      </a-form-item>
      <!-- 树形组件 配置 -->
      <a-form-item :label="t('选择树')" v-if="data.type == 'tree-component'">
        <a-select
          v-model:value="data.options!.treeConfig.id"
          style="width: 100%"
          :options="treeSelectOption"
          :field-names="{ label: 'name', value: 'id', options: 'children' }"
          disabled
        />
      </a-form-item>
      <a-form-item :label="t('允许多选')" v-if="data.type == 'tree-component'">
        <a-switch :checked="data.options.treeConfig.isMultiple" disabled />
      </a-form-item>
      <a-form-item :label="t('组件高度')" v-if="data.type == 'tree-component'">
        <a-input v-model:value="data.options.treeConfig.height" />
      </a-form-item>
      <a-form-item :label="t('一维码格式')" v-if="data.type == 'barcode'">
        <a-select
          v-model:value="data.options.format"
          size="mini"
          :placeholder="t('请选择一维码格式')"
        >
          <a-select-option value="CODE128" />
          <a-select-option value="CODE39" />
          <a-select-option value="EAN13" />
        </a-select>
      </a-form-item>
      <a-form-item
        :label="data.type === 'qrcode' ? t('二维码内容') : t('一维码内容')"
        v-if="(data.type == 'qrcode' || data.type == 'barcode') && data.options.codeType == 'fixed'"
      >
        <a-textarea
          v-model:value="data.options.defaultValue"
          :placeholder="data.type === 'qrcode' ? t('请填写二维码内容') : t('请填写一维码内容')"
          :rows="4"
        />
      </a-form-item>
      <a-form-item
        :label="t('接口配置')"
        v-if="(data.type == 'qrcode' || data.type == 'barcode') && data.options.codeType == 'api'"
      >
        <a-input
          v-model:value="data.options.apiConfig.path"
          :placeholder="t('点击进行接口配置')"
          @click="handleApiConfig()"
        >
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </a-form-item>
      <!-- 子表不需要span -->
      <!-- <a-form-item label="标签宽度" v-if="hasKey('span') && !data.isSubFormChild">
        <a-input-number v-model:value="data.options.span" :min="0" :max="24" addonAfter="/ 24" />
      </a-form-item> -->

      <a-form-item :label="t('双滑块模式')" v-if="hasKey('range')">
        <a-switch :checked="data.options.range" disabled />
      </a-form-item>

      <!-- <a-form-item :label="t('反向坐标轴')" v-if="hasKey('reverse')">
        <a-switch :checked="data.options.reverse" disabled />
      </a-form-item> -->

      <template v-if="data.type === 'image'">
        <a-form-item :label="t('默认值')">
          <a-input v-model:value="imageUrl" :disabled="true" style="margin-bottom: 10px" />
          <a-upload
            action=""
            :beforeUpload="submitUpload"
            accept=""
            :max-count="1"
            :show-upload-list="false"
            :disabled="true"
          >
            <a-button :disabled="true">
              <SvgIcon name="upload" style="margin-right: 10px" />
              {{ t('点击上传') }}
            </a-button>
          </a-upload>
        </a-form-item>
      </template>

      <a-form-item :label="t('切换按钮')" v-if="hasKey('visibilityToggle')">
        <a-switch v-model:checked="data.options.visibilityToggle" />
      </a-form-item>
      <!-- 
      <a-form-item :label="t('行数')" v-if="hasKey('rows') && !data.options.autoSize">
        <a-input-number v-model:value="data.options.rows" :min="0" />
      </a-form-item> -->

      <a-form-item :label="t('显示字数')" v-if="hasKey('showCount')">
        <a-switch v-model:checked="data.options.showCount" />
      </a-form-item>

      <a-form-item :label="t('自适应高度')" v-if="hasKey('autoSize')">
        <a-switch v-model:checked="data.options.autoSize" />
      </a-form-item>

      <a-form-item :label="t('最小行数')" v-if="hasKey('minRows') && !data.options.autoSize">
        <a-input-number v-model:value="data.options.minRows" :min="0" />
      </a-form-item>

      <a-form-item :label="t('最大行数')" v-if="hasKey('maxRows') && !data.options.autoSize">
        <a-input-number v-model:value="data.options.maxRows" :min="0" />
      </a-form-item>

      <a-form-item :label="t('允许半选')" v-if="hasKey('allowHalf')">
        <a-switch v-model:checked="data.options.allowHalf" />
      </a-form-item>
      <template v-if="data.type === 'divider'">
        <a-form-item :label="t('标题')">
          <a-input v-model:value="data.options.defaultValue" />
        </a-form-item>
        <a-form-item :label="t('上间距')">
          <a-input-number v-model:value="data.options.marginTop" :min="0" />
        </a-form-item>
        <a-form-item :label="t('下间距')">
          <a-input-number v-model:value="data.options.marginBottom" :min="0" />
        </a-form-item>
        <a-form-item :label="t('文案对齐')">
          <a-radio-group button-style="solid" v-model:value="data.options.orientation">
            <a-radio-button value="left">{{ t('左') }}</a-radio-button>
            <a-radio-button value="center">{{ t('中') }}</a-radio-button>
            <a-radio-button value="right">{{ t('右') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </template>
      <template v-if="data.type === 'upload'">
        <a-form-item :label="t('上传列表样式')">
          <a-radio-group v-model:value="data.options.listType" button-style="solid">
            <a-radio-button value="text">text</a-radio-button>
            <a-radio-button value="picture">picture </a-radio-button>
            <a-radio-button value="picture-card">picture-card</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item :label="t('文件类型')">
          <a-input
            v-model:value="data.options.accept"
            :placeholder="t('用“,”号隔开,例如:.jpg,.bmp')"
          />
        </a-form-item>

        <a-form-item :label="t('最大文件数')">
          <a-input-number v-model:value.number="data.options.maxNumber" :min="1" />
        </a-form-item>

        <a-form-item :label="t('大小限制')">
          <a-input v-model:value.number="data.options.maxSize" suffix="MB" />
        </a-form-item>

        <a-form-item :label="t('支持多选')">
          <a-switch v-model:checked="data.options.multiple" />
        </a-form-item>
      </template>

      <a-form-item :label="t('选项样式')" v-if="hasKey('optionType')">
        <a-radio-group button-style="solid" v-model:value="data.options.optionType">
          <a-radio-button value="default">{{ t('默认') }}</a-radio-button>
          <a-radio-button value="button">{{ t('按钮') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item :label="t('调用签名')" v-if="hasKey('isCallSign')">
        <a-switch v-model:checked="data.options.isCallSign" disabled />
      </a-form-item>
      <a-form-item :label="t('是否显示文本')" v-if="data.type == 'barcode'">
        <a-switch v-model:checked="data.options.displayValue" />
      </a-form-item>
      <a-form-item :label="t('显示标签')" v-if="hasKey('showLabel')">
        <a-switch v-model:checked="data.options.showLabel" />
      </a-form-item>
      <a-form-item :label="t('是否显示')" v-if="hasKey('isShow')">
        <a-switch v-model:checked="data.options.isShow" disabled />
      </a-form-item>
      <!-- <a-form-item :label="t('允许搜索')" v-if="hasKey('showSearch')">
        <a-switch v-model:checked="data.options.showSearch" />
      </a-form-item> -->
      <a-form-item :label="t('允许多选')" v-if="hasKey('isMultiple')">
        <a-switch :checked="data.options.isMultiple" disabled />
      </a-form-item>
      <a-form-item :label="t('是否必填')" v-if="hasKey('required')">
        <a-switch :checked="data.options.required" disabled />
      </a-form-item>

      <a-form-item :label="t('是否只读')" v-if="hasKey('readonly')">
        <a-switch :checked="data.options.readonly" disabled />
      </a-form-item>

      <a-form-item :label="t('是否禁用')" v-if="hasKey('disabled')">
        <a-switch :checked="data.options.disabled" disabled />
      </a-form-item>
      <!-- <a-form-item :label="t('是否合计')" v-if="hasKey('subTotal') && data.isSubFormChild">
        <a-switch v-model:checked="data.options.subTotal" />
      </a-form-item> -->

      <a-form-item :label="t('开启清除')" v-if="hasKey('allowClear')">
        <a-switch
          v-model:checked="data.options.allowClear"
          :disabled="data.options.readonly || data.options.disabled"
        />
      </a-form-item>

      <!-- <a-form-item :label="t('组件边框')" v-if="hasKey('showComponentBorder')">
        <a-switch v-model:checked="data.options.showComponentBorder" />
      </a-form-item>

      <a-form-item :label="t('表单边框')" v-if="hasKey('showFormBorder')">
        <a-switch v-model:checked="data.options.showFormBorder" />
      </a-form-item>

      <a-form-item :label="t('显示序号')" v-if="hasKey('showIndex')">
        <a-switch v-model:checked="data.options.showIndex" />
      </a-form-item> -->
      <template v-if="data.type === 'title'">
        <a-form-item :label="t('标题')">
          <a-input v-model:value="data.options.defaultValue" />
        </a-form-item>
        <a-form-item :label="t('字体颜色')">
          <ColorPicker v-model:value="data.options.color" />
        </a-form-item>
        <a-form-item :label="t('字体大小')">
          <a-input-number v-model:value="data.options.fontSize" :min="0" />
        </a-form-item>
        <a-form-item :label="t('对齐方式')">
          <a-radio-group v-model:value="data.options.align" button-style="solid">
            <a-radio-button value="left">{{ t('左对齐') }}</a-radio-button>
            <a-radio-button value="center">{{ t('居中对齐') }}</a-radio-button>
            <a-radio-button value="right">{{ t('右对齐') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </template>

      <!-- <template v-if="data.type === 'grid'">
        <a-form-item :label="t('栅格间隔')">
          <a-input-number v-model:value="data.options.gutter" :min="0" />
        </a-form-item>

        <a-form-item :label="t('列配置项')">
          <a-button size="small" type="primary" @click="handleInsertColumn" style="width: 100%">
            {{ t('添加列') }}
          </a-button>
        </a-form-item>

        <a-form-item :label="t('列宽配置')">
          <ul>
            <li v-for="(item, index) in data.layout"  class="column-width">
              <a-input-number
                :placeholder="t('栅格值')"
                size="small"
                v-model:value="item.span"
                :min="0"
                :max="24"
              />
              <DragOutlined class="draggable-icon" />
              <SvgIcon name="delete" @click.stop="handleOptionsRemove(index)" class="delete-icon" />
            </li>
          </ul>
        </a-form-item>

        <a-form-item :label="t('垂直对齐')">
          <a-radio-group v-model:value="data.options.align" button-style="solid">
            <a-radio-button value="top">{{ t('顶部对齐') }}</a-radio-button>
            <a-radio-button value="middle">{{ t('居中对齐') }}</a-radio-button>
            <a-radio-button value="bottom">{{ t('底部对齐') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item :label="t('水平排列')">
          <a-select v-model:value="data.options.justify">
            <a-select-option value="start">{{ t('左对齐') }}</a-select-option>
            <a-select-option value="end">{{ t('右对齐') }}</a-select-option>
            <a-select-option value="center">{{ t('居中') }}</a-select-option>
            <a-select-option value="space-around">{{ t('两侧间隔相等') }}</a-select-option>
            <a-select-option value="space-between">{{ t('两端对齐') }}</a-select-option>
          </a-select>
        </a-form-item>
      </template> -->

      <template v-if="data.type === 'tab'">
        <a-form-item :label="t('选项卡配置')">
          <!-- <a-button size="small" type="primary" @click="handleInsertColumn" style="width: 100%">
            {{ t('添加选项卡') }}
          </a-button> -->
          <ul style="margin-top: 10px">
            <li v-for="(item, index) in data.layout" :key="index" class="column-width">
              <a-input
                :placeholder="t('选项卡名称')"
                size="small"
                :value="item.name"
                :min="0"
                :max="24"
                disabled
              />
              <DragOutlined class="draggable-icon" />
              <!-- <SvgIcon name="delete" @click.stop="handleOptionsRemove(index)" class="delete-icon" /> -->
            </li>
          </ul>
        </a-form-item>
        <a-form-item :label="t('选项卡风格')">
          <a-radio-group button-style="solid" :value="data.options.type" disabled>
            <a-radio-button value="line">line</a-radio-button>
            <a-radio-button value="card">card</a-radio-button>
          </a-radio-group>
        </a-form-item>

        <a-form-item :label="t('选项卡位置')">
          <a-radio-group button-style="solid" :value="data.options.tabPosition" disabled>
            <a-radio-button value="top">{{ t('顶部') }}</a-radio-button>
            <a-radio-button value="right">{{ t('右侧') }}</a-radio-button>
            <a-radio-button value="bottom">{{ t('底部') }}</a-radio-button>
            <a-radio-button value="left">{{ t('左侧') }}</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </template>
      <!-- <template v-if="data.type === 'form'">
        <div class="flex justify-between items-center ml-3 mb-3">
          <div
            class="pl-2 border-l-[6px] border-[#5e95ff] border-solid"
            style="font-size: 14px !important"
            >{{ t('表头合并') }}</div
          >
          <a-button
            style="font-size: 14px !important"
            type="primary"
            size="small"
            @click="addMutipleHeads"
            >{{ t('添加') }}</a-button
          >
        </div>

        <div v-if="data.options.multipleHeads?.length > 0">
          <div
            v-for="(sight, index) in data.options.multipleHeads"
            :key="sight.key"
            class="mt-6 relative bg-[#f8f8f8] py-2 px-4 ml-3"
          >
            <a-form-item
              label="名称"
              :name="['sights', index, 'title']"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-input v-model:value="sight.title" />
            </a-form-item>
            <a-form-item
              :name="['sights', index, 'children']"
              label="子列"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-select
                mode="multiple"
                v-model:value="sight.children"
                :options="sight.headschilds"
                :fieldNames="{ label: 'title', value: 'key' }"
                style="width: 100%"
                @click="sightChild(sight)"
              />
            </a-form-item>
            <a-form-item
              :name="['sights', index, 'align']"
              label="对齐"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-select v-model:value="sight.align" :options="sightsAlign" style="width: 100%" />
            </a-form-item>
            <div class="icon-delete">
              <SvgIcon
                name="delete"
                @click.stop="deleteSight(index, sight.key)"
                class="svg-delete"
              />
            </div>
          </div>
        </div>
      </template> -->
    </a-form>
    <ApiConfig
      v-if="apiConfigDialog"
      v-model:apiConfigDialog="apiConfigDialog"
      v-model:apiConfig="data.options.apiConfig"
      :isCascader="isCascader"
      :isQrcode="isQrcode"
      :formItem="data"
      :title="isCascader ? t('级联配置-API') : t('API配置')"
    />
    <ApiAssoConfig
      v-if="apiAssoDia"
      disabled
      v-model:apiAssoDia="apiAssoDia"
      v-model:apiConfig="data.options.apiConfig"
      type="preload-title"
    />
    <DicAssoConfig
      v-if="dicAssoDia"
      v-model:dicAssoDia="dicAssoDia"
      v-model:dicOptions="data.options.dicOptions"
      type="preload-title"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent, inject, Ref, ref, watch, onMounted, nextTick, computed } from 'vue';
  import { DragOutlined } from '@ant-design/icons-vue';

  import { SvgIcon, IconPicker, Icon } from '/@/components/Icon';
  import {
    FieldInfo,
    noHaveTableAndField,
    noHaveField,
    noHaveTitle,
    TableInfo,
    unPreloadComponents,
    MutipleHeadInfo,
  } from '/@/components/Designer/src/types';
  import { getDicDetailList } from '/@/api/system/dic';
  import { getCodeRule } from '/@/api/system/code';
  import type { SelectProps } from 'ant-design-vue';

  import { message } from 'ant-design-vue';
  import DataSourceSetting from '/@/components/Designer/src/components/componentProperty/settings/DataSourceSetting.vue';
  import ApiAssoConfig from '/@/components/Designer/src/components/componentProperty/settings/ApiAssoConfig.vue';
  import DicAssoConfig from '/@/components/Designer/src/components/componentProperty/settings/DicAssoConfig.vue';
  //import ComputationalSetting from '/@/components/Designer/src/components/componentProperty/settings/ComputationalSetting.vue';

  import { ApiConfig } from '/@/components/ApiConfig';
  import { geList } from '/@/api/system/generator/treeStructure';
  import { ColorPicker } from '/@/components/ColorPicker';
  import { TimePicker } from '/@/components/TimePicker';
  import { XjrDatePicker } from '/@/components/DatePicker';
  import { DicTreeSelect } from '/@/components/DicTreeSelect';
  import Sortable from 'sortablejs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { getFileList } from '/@/api/system/file';
  import { uploadMultiApi } from '/@/api/sys/upload';
  import { SelectUser } from '/@/components/SelectOrganizational/index';

  const { t } = useI18n();
  export default defineComponent({
    name: 'PropertyOption',
    components: {
      SvgIcon,

      IconPicker,
      Icon,
      DataSourceSetting,
      // ComputationalSetting,

      ApiConfig,

      DragOutlined,
      ColorPicker,
      TimePicker,
      XjrDatePicker,
      DicTreeSelect,
      ApiAssoConfig,
      DicAssoConfig,
      SelectUser,
    },
    props: {
      //所选组件配置
      select: {
        type: Object,
      },
      widgetForm: {
        type: Object,
      },
    },
    emits: ['update:select'],
    setup(props, context) {
      const tableInfo = inject<Ref<TableInfo[]>>('tableInfo') as Ref<TableInfo[]>;
      const designType = inject<string>('designType');
      const mapComps = ref<any[]>([]); //地图可以映射的组件
      const checkFlag = (o) => {
        //地图组件不能映射的组件
        const filterType = [
          'number',
          'cascader',
          'area',
          'switch',
          'slider',
          'time',
          'time-range',
          'date',
          'date-range',
          'rate',
          'picker-color',
          'upload',
          'image',
          'button',
          'form',
          'title',
          'grid',
          'tab',
          'card',
          'divider',
          'computational',
          'money-chinese',
          'map',
          'one-for-one',
        ];
        return !filterType?.includes(o.type);
      };
      watch(
        () => props.widgetForm,
        (val) => {
          let curComp: any = props.select;
          if (curComp?.type && val) {
            getMapRelationComps(curComp, val?.list);
          }
        },
        { deep: true, immediate: true },
      );
      const imageUrl = ref('');
      const data = ref<any>();
      const fieldsInfo = ref<FieldInfo[]>([]); //所选表字段
      const remoteOptions = ref<Recordable>([]);
      const remoteDefaultOptions = ref<Recordable[]>([]);
      const codeRuleOptions = ref<any>([]);
      const SignSelectOption = ref<SelectProps['options']>([]);
      const userNames = ref<string>();

      const computationalDialog = ref<boolean>(false);
      const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
      };
      const apiConfigDialog = ref<boolean>(false);
      const apiAssoDia = ref<boolean>(false);
      const dicAssoDia = ref<boolean>(false);
      const isCascader = ref<boolean>(false);
      const isQrcode = ref<boolean>(false);
      const buttonTableOptions = ref<any>([]);
      const formatOptions = [
        {
          label: t('显示全部结果'),
          value: 'all',
        },
        {
          label: t('显示最子级结果'),
          value: 'showMostChildLevel',
        },
      ];
      const separatorOptions = [
        {
          label: '/',
          value: '/',
        },
        {
          label: '--',
          value: '--',
        },
        {
          label: '|',
          value: '|',
        },
      ];
      const selectedOptions = [
        {
          label: t('必须选择到最子级'),
          value: 'selectMostChildLevel',
        },
        {
          label: t('可以选到任意一级'),
          value: 'any',
        },
      ];
      const sightsAlign = [
        {
          label: '左对齐',
          value: 'left',
        },
        {
          label: '居中',
          value: 'center',
        },
        {
          label: '右对齐',
          value: 'right',
        },
      ];
      const configText = computed(() => {
        let preloadType = data.value.options.preloadType;
        if (data.value.type == 'button' && data.value.options.buttonType == 2) {
          preloadType = data.value.options.datasourceType;
        }
        const dicOptions = data.value.options.dicOptions;
        const outputParams = data.value.options.apiConfig?.outputParams;
        if (
          (preloadType === 'dic' && dicOptions?.length) ||
          (preloadType === 'api' && outputParams?.length)
        ) {
          return t('已配置');
        } else {
          return '';
        }
      });

      const handleTableChange = (value: string) => {
        const currentTable = tableInfo.value.find((table) => table.name === value)!;
        fieldsInfo.value = currentTable?.fields;
      };
      let treeSelectOption = ref<any>([]);
      watch(
        () => props.select,
        (val) => {
          //如果选择组件变化  首先判断它是否为子表单组件
          if (val?.type === 'form' || val?.type === 'one-for-one') {
            tableInfo?.value.map((x) => {
              x.disabled = !!x.isMain;
            });
          } else {
            tableInfo?.value.map((x) => {
              x.disabled = !x.isMain;
            });
          }
          data.value = val;
          if (designType === 'data') {
            val?.bindTable ? handleTableChange(val?.bindTable) : (fieldsInfo.value = []); //没有选择数据表时 不展示数据字段
          }
          getMapRelationComps(val, props.widgetForm?.list);
          if (val?.type == 'button' && val?.options.buttonType == 2) {
            buttonTableOptions.value = [];
            getButtonRelatonTables(props.widgetForm?.list);
          }
          if (val?.type === 'image') {
            getImage();
          }
          if (val?.type === 'signature') {
            getSignAssoComponents();
          }
        },
        {
          deep: true,
          immediate: true,
        },
      );
      watch(
        data,
        (val) => {
          context.emit('update:select', val);
        },
        {
          immediate: true,
          deep: true,
        },
      );
      watch(
        () => data.value.options.latiAndLong,
        (val) => {
          if (data.value.type === 'map') {
            let arr = mapComps.value.filter((o) => {
              return o.bindField == val;
            });
            if (arr.length <= 0) {
              data.value.options.latiAndLong = null;
            }
          }
        },
      );
      watch(
        () => data.value.options.address,
        (val) => {
          if (data.value.type === 'map') {
            let arr = mapComps.value.filter((o) => {
              return o.bindField == val;
            });
            if (arr.length <= 0) {
              data.value.options.address = null;
            }
          }
        },
      );

      onMounted(() => {
        getCodeRuleList();
        if (data.value.type === 'grid' || data.value.type === 'tab') {
          sortableColumn();
        }
        initTreeData();
      });
      async function initTreeData() {
        let res = await geList();
        treeSelectOption.value = res;
      }
      function getMapRelationComps(curComp, list) {
        //获取地图可以映射的组件
        if (curComp.type == 'map') {
          mapComps.value = [];
          list?.forEach((o: any) => {
            if (o.bindTable == curComp.bindTable && checkFlag(o)) {
              mapComps.value.push(o);
            } else if (o.type == 'tab' && o.layout?.length > 0) {
              getTabMapRelaton(o, curComp.bindTable);
            } else if (
              o.bindTable == curComp.bindTable &&
              (o.type == 'form' || o.type == 'one-for-one') &&
              o.children?.length > 0
            ) {
              o.children.forEach((k) => {
                if (k.bindTable == curComp.bindTable && checkFlag(k)) {
                  mapComps.value.push(k);
                }
              });
            }
          });
        }
      }

      function getTabMapRelaton(o, bindTable) {
        o.layout.forEach((k) => {
          if (k.list?.length > 0) {
            k.list.forEach((j) => {
              if (j.bindTable == bindTable && checkFlag(j)) {
                mapComps.value.push(j);
              } else if (j.type == 'tab' && j.layout?.length > 0) {
                getTabMapRelaton(j, bindTable);
              } else if (j.bindTable == bindTable && j.type == 'form' && j.children?.length > 0) {
                j.children.forEach((f) => {
                  if (f.bindTable == bindTable && checkFlag(f)) {
                    mapComps.value.push(f);
                  }
                });
              }
            });
          }
        });
      }
      function getButtonRelatonTables(list) {
        list.forEach((x) => {
          if (['tab', 'grid', 'card'].includes(x.type)) {
            for (const child of x.layout!) {
              getButtonRelatonTables(child.list);
            }
          } else {
            if (x.type == 'form') {
              buttonTableOptions.value.push({ key: x.key, bindTable: x.bindTable, label: x.label });
            }
          }
        });
      }

      function getSignAssoComponents() {
        SignSelectOption.value = props.widgetForm?.list
          .filter((item) => item.type === 'user')
          .map((item) => {
            return {
              label: item.label,
              value: item.key,
            };
          });
      }
      const sortableColumn = () => {
        nextTick(() => {
          const tbody: any = document.querySelector('.awc-containter ul');
          Sortable.create(tbody, {
            handle: '.draggable-icon',
            onEnd: ({}) => {
              //const noMoveColumn = data.value.layout.slice(oldIndex, data.value.layout.length - 1);
            },
          });
        });
      };

      const hasKey = (key: string) => Object.keys(data.value.options).includes(key);

      const handleInsertColumn = () => {
        data.value.layout.push({
          span: 0,
          list: [],
        });
        sortableColumn();
      };

      const showApiConfig = (cascader = false) => {
        apiConfigDialog.value = true;
        isCascader.value = cascader;
      };
      const handleApiConfig = () => {
        apiConfigDialog.value = true;
        isQrcode.value = true;
      };

      const formatChange = () => {
        data.value.options.defaultValue = '';
      };

      const handleOptionsRemove = (index: number) => {
        if (data.value.type === 'grid' || data.value.type === 'tab') {
          data.value.layout.splice(index, 1);
        } else {
          data.value.options.staticOptions.splice(index, 1);
        }
      };

      const handleSelectModeChange = (event: any) => {
        const { value } = event.target;
        if (value === null) {
          data.value.options.defaultValue.length
            ? (data.value.options.defaultValue = data.value.options.defaultValue[0])
            : (data.value.options.defaultValue = null);
        } else {
          if (data.value.options.defaultValue) {
            if (!(data.value.options.defaultValue instanceof Array)) {
              data.value.options.defaultValue = [data.value.options.defaultValue];
            }
          } else {
            data.value.options.defaultValue = [];
          }
        }
      };

      const handleSliderModeChange = (checked: boolean) => {
        checked
          ? (data.value.options.defaultValue = [0, 0])
          : (data.value.options.defaultValue = 0);
      };

      const tableTitleClick = () => {
        if (
          data.value.options.preloadType === 'dic' ||
          (data.value.type == 'button' &&
            data.value.options.buttonType == 2 &&
            data.value.options.datasourceType == 'dic')
        ) {
          dicAssoDia.value = true;
        } else if (
          data.value.options.preloadType === 'api' ||
          (data.value.type == 'button' &&
            data.value.options.buttonType == 2 &&
            data.value.options.datasourceType == 'api')
        ) {
          apiAssoDia.value = true;
        }
      };

      const handleDicChange = async (value: string) => {
        const result = await getDicDetailList({ itemId: value });

        remoteDefaultOptions.value = result.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      };

      const preloadChange = () => {
        data.value.children.map((item) => {
          if (!unPreloadComponents.includes(item.type)) {
            return (item.options.prestrainField = '');
          }
        });
      };

      const getCodeRuleList = async () => {
        const result = await getCodeRule();
        codeRuleOptions.value = result;
      };

      const handleApiInputFocus = () => {
        const { labelField, valueField, api } = data.value.options.props;
        // 如果三个条件  某一个为空 都不执行
        if (!labelField || !valueField || !api) return;

        fetch(api, {})
          .then((resp) => resp.json())
          .then((json) => {
            if (Array.isArray(json)) {
              data.value.options.staticOptions = json.map((res: any) => ({
                label: res[labelField],
                value: res[valueField],
              }));
            } else {
              // 最多显示不超过100
              if (json.data.length > 100) {
                json.data.length = 100;
              }
              data.value.options.staticOptions = json.data.map((res: any) => ({
                label: res[labelField],
                value: res[valueField],
              }));
            }
          })
          .catch((_) => {
            data.value.options.staticOptions = [
              {
                label: 'datasource_1',
                value: 'datasource_1',
              },
              {
                label: 'datasource_2',
                value: 'datasource_2',
              },
            ];
          });
      };

      const getFieldType = (type: number): string => {
        switch (type) {
          case 1:
            return 'number';
          case 2:
            return 'bool';
          case 3:
            return 'date';
          case 4:
            return 'time';
          default:
            return 'string';
        }
      };
      const changeReadonly = (checked: Boolean) => {
        if (checked) {
          hasKey('allowClear') ? (data.value.options.allowClear = false) : '';
          hasKey('required') ? (data.value.options.required = false) : '';
        }
      };
      const changeDisabled = (checked: Boolean) => {
        if (checked) {
          hasKey('readonly') ? (data.value.options.readonly = true) : '';
          hasKey('allowClear') ? (data.value.options.allowClear = false) : '';
          hasKey('required') ? (data.value.options.required = false) : '';
        }
      };
      const changeNumber = () => {
        if (data.value.type !== 'number') return;
        const minNum = data.value.options.min;
        const maxNum = data.value.options.max;
        const value = data.value.options.defaultValue;
        if (minNum > maxNum) {
          message.error(t('计数器最小值只能小于等于最大值'));
          data.value.options.min = data.value.options.max - 1;
        } else if (value < minNum) {
          message.error(t('计数器默认值不能小于最小值'));
          data.value.options.defaultValue = '';
        } else if (value > maxNum) {
          message.error(t('计数器默认值不能大于最大值'));
          data.value.options.defaultValue = '';
        }
      };

      const submitUpload = (file) => {
        let folderId = data.value.options.defaultValue;
        uploadMultiApi(
          {
            name: 'file',
            file: [file],
          },
          folderId,
        ).then((res) => {
          data.value.options.defaultValue = res[0].folderId;
          getImage();
        });
      };

      function handleChangeCode() {
        data.value.options.defaultValue = '';
      }
      const deleteSight = (index: number, key: string) => {
        data.value.options.multipleHeads.splice(index, 1);
        data.value.options.multipleHeads.forEach((o) => {
          let idx = o.children.findIndex((k) => {
            return k == key;
          });
          if (idx >= 0) {
            o.children.splice(idx, 1);
          }
        });
      };

      function addHeadsObj(o, obj?) {
        const com: MutipleHeadInfo = {
          key: o.key,
          title: o.title,
          dataIndex: o.dataIndex,
          children: [],
          align: 'center',
        };
        if (obj) {
          obj.push(com);
        } else {
          data.value.options.multipleHeads.push(com);
        }
      }

      const addMutipleHeads = () => {
        let len = data.value.options.multipleHeads.length;
        let keys: number[] = [];
        data.value.options.multipleHeads.map((o) => {
          keys.push(Number(o.key.split('di')[1].split('lie')[0]));
        });

        let num = -1;
        for (let i = 0; i < len; i++) {
          if (!keys.includes(i)) num = i;
        }
        if (num === -1) {
          num = len;
        }

        addHeadsObj({
          key: 'di' + num + 'lie',
          title: '第' + num + '列',
          dataIndex: 'di' + num + 'lie',
        });
      };

      function findParent(arr, o, cur, filters) {
        for (let i = 0; i < arr.length; i++) {
          let k = arr[i];
          if (k.key != cur) {
            if (k == o.key) {
              filters.push(k);
              break;
            } else if (k.dataIndex !== undefined && k.children.length > 0) {
              findParent(k.children, o, cur, filters);
            }
          }
        }
      }
      function findSelf(obj, cur, parent) {
        for (let i = 0; i < obj.children.length; i++) {
          let k = obj.children[i];
          if (k == cur) {
            filterHeads.value.push(parent);
            break;
          } else {
            let temp = data.value.options.multipleHeads.find((o) => {
              return o.key == k;
            });
            if (temp) {
              findSelf(temp, cur, parent);
            }
          }
        }
      }
      function findBrother(arr, cur, key) {
        arr.forEach((o) => {
          if (o.children.length > 0 && cur != o.key && o.key !== key) {
            findBrotherParent(o.children, cur);
          }
        });
      }
      function findBrotherParent(arr, cur) {
        for (let i = 0; i < arr.length; i++) {
          let k = arr[i];
          if (k == cur) {
            filterHeads.value.push(k);
            break;
          } else if (k.dataIndex !== undefined && k.children.length > 0) {
            findBrotherParent(k.children, cur);
          }
        }
      }
      const filtercomps = ref<string[]>([]);
      const filterHeads = ref<string[]>([]);

      const sightChild = (sight) => {
        let temp: MutipleHeadInfo[] = [];

        data.value.children.map((a) => {
          a.title = a.label;
          a.dataIndex = a.bindField;
        });

        checkDatas(data.value.children, temp, sight);

        checkHeads(data.value.options.multipleHeads, temp, sight);

        sight.headschilds = temp;
      };
      function checkDatas(origin, temp, sight) {
        filtercomps.value = [];

        origin.forEach((k) => {
          findParent(data.value.options.multipleHeads, k, sight.key, filtercomps.value);
        });
        let datas = origin.filter((o) => {
          return !filtercomps.value.includes(o.key);
        });
        datas.forEach((o) => {
          if (o.key !== sight.key) {
            addHeadsObj(o, temp);
          }
        });
      }
      function checkHeads(origin, temp, sight) {
        filterHeads.value = [sight.key]; //不能选自己
        origin.forEach((o) => {
          if (o.children.length > 0 && sight.key != o.key) {
            //谁选了自己就排除谁
            findSelf(o, sight.key, o.key);
          }
          if (sight.key != o.key) {
            //兄弟列有被别人选中过的排除(被自己选中的除外)
            findBrother(origin, o.key, sight.key);
          }
          if (o.children.length <= 0) {
            //不能选子列是空的
            filterHeads.value.push(o.key);
          }
        });

        let datas = origin.filter((o) => {
          return !filterHeads.value.includes(o.key);
        });
        datas.forEach((o) => {
          if (o.key !== sight.key) {
            addHeadsObj(o, temp);
          }
        });
      }
      async function getImage() {
        if (data.value.options.defaultValue) {
          let fileList = await getFileList({ folderId: data.value.options.defaultValue });
          if (fileList.length) {
            imageUrl.value = fileList[0].fileUrl;
            data.value.options.defaultValue = fileList[0].folderId;
          }
        } else {
          imageUrl.value = '';
        }
      }
      return {
        data,
        hasKey,
        handleInsertColumn,
        handleOptionsRemove,
        handleSelectModeChange,
        handleSliderModeChange,
        handleTableChange,
        handleApiInputFocus,
        handleDicChange,
        showApiConfig,
        getFieldType,
        getCodeRule,
        changeReadonly,
        changeDisabled,
        changeNumber,
        submitUpload,
        tableTitleClick,
        apiConfigDialog,
        apiAssoDia,
        dicAssoDia,
        isCascader,
        configText,
        noHaveTableAndField,
        unPreloadComponents,
        preloadChange,
        formatChange,
        noHaveField,
        noHaveTitle,
        tableInfo,
        fieldsInfo,
        designType,
        remoteOptions,
        remoteDefaultOptions,
        codeRuleOptions,
        computationalDialog,
        layout,
        formatOptions,
        separatorOptions,
        selectedOptions,
        mapComps,
        handleApiConfig,
        isQrcode,
        handleChangeCode,
        sightsAlign,
        deleteSight,
        addMutipleHeads,
        sightChild,
        buttonTableOptions,
        imageUrl,
        t,
        treeSelectOption,
        SignSelectOption,
        userNames,
      };
    },
  });
</script>
<style scoped lang="less">
  *,
  :deep(.ant-input) {
    font-size: 12px !important;
  }

  .fc-style .widget-config-container .config-content .ant-form-item,
  .fc-style .widget-config-container .config-content .el-form-item,
  .fc-style .widget-config-container .config-content h4 {
    padding-bottom: 12px;
    border-bottom: 1px dashed #e1e1e1;
    margin-bottom: 16px;
  }

  .select-list {
    margin-bottom: 10px;
  }

  .select-list label {
    padding: 0 11px;
  }

  :deep(.ant-row) {
    align-items: center;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-table-wrapper) {
    margin: 5px 0 10px;
  }

  .column-width {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .draggable-icon {
      margin: 0 10px;
      cursor: move;
      color: #ccc;
    }

    .delete-icon {
      color: #4ecece;
      cursor: pointer;
    }
  }

  .icon-delete {
    position: absolute;
    top: -12px;
    right: -5px;
    width: 24px;
    text-align: center;
    border: 1px solid #f64c4c;
    color: #f64c4c;
    border-radius: 50%;
    cursor: pointer;

    .svg-delete {
      width: 13px !important;
      height: 13px !important;
    }
  }
  // @import '/@/assets/style/designer/index.css';
</style>
