<template>
  <div
    v-if="
      (generatorConfig.outputConfig.funcType === 0 && current === 4) ||
      (generatorConfig.outputConfig.funcType === 1 &&
        generatorConfig.outputConfig.createCode === true)
    "
  >
    <a-tabs v-model:activeKey="activeKey" size="large">
      <a-tab-pane key="1" :tab="t('表单页面')">
        <CodeEditor :mode="MODE.VUE" :value="codes?.formCode" />
      </a-tab-pane>
      <a-tab-pane key="2" :tab="t('列表页面')">
        <CodeEditor :mode="MODE.VUE" :value="codes?.listCode" />
      </a-tab-pane>
      <a-tab-pane key="3" :tab="t('Api代码')">
        <CodeEditor :mode="MODE.JS" :value="codes?.apiCode" />
      </a-tab-pane>
      <a-tab-pane key="5" :tab="t('Config代码')">
        <CodeEditor :mode="MODE.JS" :value="codes?.configJsonCode" />
      </a-tab-pane>
    </a-tabs>
  </div>
  <div v-else class="flex items-center justify-center h-full text-[30px] text-[#ccc]">
    自定义表单不生成代码
  </div>
</template>
<script lang="ts" setup>
  import { inject, Ref, ref, watch } from 'vue';
  import { CodeEditor, MODE } from '/@/components/CodeEditor';

  import { useMessage } from '/@/hooks/web/useMessage';

  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { buildAppFormProps } from '/@/utils/helper/designHelper';
  import { buildAppCode } from '/@/utils/helper/generatorHelper';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { AppFormProps } from '/@/model/generator/appFormConfig';
  import { GeneratorAppModel } from '/@/api/system/generator/model';
  const { t } = useI18n();

  const generatorConfig = inject<GeneratorConfig>('generatorConfig') as GeneratorConfig;

  const current = inject<Ref<number>>('current') as Ref<number>;
  const designType = inject<Ref<string>>('designType') as Ref<string>;
  const { notification } = useMessage();

  const activeKey = ref('1');

  const codes = ref<GeneratorAppModel>();
  watch(
    () => current.value,
    (val) => {
      //如果已经到此步骤 则生成代码显示
      if (
        (generatorConfig.outputConfig.funcType === 0 && val === 4) ||
        (generatorConfig.outputConfig.funcType === 1 &&
          generatorConfig.outputConfig.createCode === true &&
          val === 4)
      ) {
        codes.value = buildAppCode(
          generatorConfig,
          buildAppFormProps(generatorConfig.formJson) as AppFormProps,
          designType.value,
        );
      }
    },
  );

  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    if (
      (generatorConfig.outputConfig.funcType === 0 && current.value === 4) ||
      (generatorConfig.outputConfig.funcType === 1 &&
        generatorConfig.outputConfig.createCode === true &&
        current.value === 3)
    ) {
      if (!codes.value?.apiCode || codes.value?.apiCode.length === 0) {
        notification.error({
          message: t('提示'),
          description: t(`Api代码生成错误，请联系管理员！`),
        }); //提示消息
        return false;
      }

      if (!codes.value?.formCode || codes.value?.formCode.length === 0) {
        notification.error({
          message: t('提示'),
          description: t(`表单页面代码生成错误，请联系管理员！`),
        }); //提示消息
        return false;
      }
      if (!codes.value?.listCode || codes.value?.listCode.length === 0) {
        notification.error({
          message: t('提示'),
          description: t(`列表页面代码生成错误，请联系管理员！`),
        }); //提示消息
        return false;
      }
      if (!codes.value?.configJsonCode || codes.value?.configJsonCode.length === 0) {
        notification.error({
          message: t('提示'),
          description: t(`Config配置代码生成错误，请联系管理员！`),
        }); //提示消息
        return false;
      }
    }
    return true;
  };

  defineExpose({ validateStep });
</script>
