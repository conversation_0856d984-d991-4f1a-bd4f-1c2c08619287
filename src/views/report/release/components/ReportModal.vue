<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" :style="{ 'margin-right': '10px' }" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { FormSchema } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import {
    addReportRelease,
    updateReportRelease,
    getReportReleaseInfo,
    getReportList,
  } from '/@/api/system/report';
  import { ReportCategory } from '/@/enums/reportEnum';
  const Separator = '---';
  onMounted(async () => {
    let res = await getReportList();
    let list = res.map((ele) => {
      ele.id = ele.id + Separator + ele.dataType; // value = id---type
      return ele;
    });
    reportListOptions.value = list;
  });

  const emits = defineEmits(['success', 'register']);
  const { notification } = useMessage();
  const isUpdate = ref(true);
  const rowId = ref('');
  const systemId = ref('');
  const reportListOptions = ref<any[]>([]);
  const { t } = useI18n();

  const accountFormSchema: FormSchema[] = [
    {
      field: 'code',
      label: t('菜单编号'),
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: t('请填写菜单编号'),
      },
    },
    {
      field: 'title',
      label: t('菜单名称'),
      component: 'Input',
      required: true,
      colProps: { span: 12 },
      componentProps: {
        placeholder: t('请填写菜单名称'),
      },
    },
    {
      field: 'parentId',
      label: t('上级菜单'),
      component: 'MenuSelect',
      required: import.meta.env.VITE_GLOB_PRODUCTION === 'true',
      componentProps: {
        placeholder: t('请选择上级菜单'),
        onChange: (_, options) => {
          systemId.value = options.systemId;
        },
        getPopupContainer: () => document.body,
      },
      colProps: { span: 12 },
    },
    {
      field: 'reportId',
      label: t('报表文件'),
      component: 'Select',
      required: true,
      componentProps: {
        placeholder: t('请选择报表文件'),
        fieldNames: { label: 'name', value: 'id' },
        options: reportListOptions,
        getPopupContainer: () => document.body,
      },
      colProps: { span: 12 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      component: 'InputNumber',
      componentProps: {
        placeholder: t('请填写排序'),
        min: 0,
      },
      colProps: { span: 12 },
    },
    {
      field: 'icon',
      label: t('图标'),
      component: 'IconPicker',
      componentProps: {
        placeholder: t('请选择图标'),
      },
      colProps: { span: 12 },
    },
    {
      field: 'remark',
      label: t('功能描述'),
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请填写功能描述'),
      },
      colProps: { span: 24 },
    },
  ];
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 90,
    rowProps: {
      gutter: 20,
    },
    schemas: accountFormSchema,
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false, width: 700 });

    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      rowId.value = data.id;
      const record = await getReportReleaseInfo(data.id);
      if (record.dataType != undefined) {
        record.reportId = record.reportId + Separator + record.dataType;
      } else {
        record.reportId = record.reportId + Separator + ReportCategory.COMMON;
      }
      systemId.value = record.systemId;
      setFieldsValue({
        ...record,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? t('新增') : t('编辑')));

  async function handleSubmit() {
    try {
      const values = await validate();
      values.systemId = systemId.value;
      setModalProps({ confirmLoading: true });
      let dataType: ReportCategory = ReportCategory.COMMON;
      if (values.reportId && values.reportId.includes(Separator)) {
        let reportIdArr = values.reportId.split(Separator);
        values.reportId = reportIdArr[0];
        dataType = reportIdArr[1];
      }
      values.dataType = dataType;
      if (!unref(isUpdate)) {
        //false 新增
        await addReportRelease(values);
        notification.success({
          message: t('提示'),
          description: t('新增成功！'),
        }); //提示消息
      } else {
        values.id = rowId.value;
        await updateReportRelease(values);
        notification.success({
          message: t('提示'),
          description: t('修改成功！'),
        }); //提示消息
      }

      closeModal();
      emits('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
