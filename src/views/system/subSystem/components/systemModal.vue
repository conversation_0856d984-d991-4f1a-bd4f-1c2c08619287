<template>
  <BasicModal
    v-bind="$attrs"
    destroyOnClose
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="40%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :style="{ 'margin-right': '10px' }" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermissionStore } from '/@/store/modules/permission';
  import { addSubSystem, updateSubSystem } from '/@/api/system/subSystem';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useAppInject } from '/@/hooks/web/useAppInject';
  const { t } = useI18n();
  export const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: t('名称'),
      component: 'Input',
      required: true,
      colProps: { lg: 24, md: 24 },
    },

    {
      field: 'code',
      label: t('编码'),
      component: 'Input',
      required: true,
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'icon',
      label: t('图标'),
      component: 'IconPicker',
      required: true,
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'description',
      label: t('描述'),
      component: 'InputTextArea',
      colProps: { lg: 24, md: 24 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      component: 'InputNumber',
      required: true,
      colProps: { lg: 24, md: 24 },
    },
  ];

  export default defineComponent({
    name: 'MenuDrawer',
    components: {
      BasicModal,
      BasicForm,
    },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const { notification } = useMessage();
      const rowId = ref('');
      const permissionStore = usePermissionStore();
      const { t } = useI18n();
      const { getShowTopMenu } = useMenuSetting();
      const { getIsMobile } = useAppInject();
      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 100,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: { lg: 12, md: 24 },
      });

      const [registerDrawer, { setModalProps, closeModal }] = useModalInner(async (data) => {
        resetFields();
        setModalProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;

        if (unref(isUpdate)) {
          rowId.value = data.record.id;

          setFieldsValue({
            ...data.record,
          });
        }
      });

      const getTitle = computed(() => (!unref(isUpdate) ? t('新增子系统') : t('编辑子系统')));

      async function handleSubmit() {
        try {
          const values = await validate();

          setModalProps({ confirmLoading: true });
          // TODO custom api
          if (!unref(isUpdate)) {
            //false 新增
            await addSubSystem(values);
            notification.success({
              message: t('操作'),
              description: t('新增成功'),
            }); //提示消息
          } else {
            values.id = rowId.value;
            await updateSubSystem(values);
            notification.success({
              message: t('操作'),
              description: t('编辑成功'),
            }); //提示消息
          }
          await permissionStore.changeSubsystem(getShowTopMenu.value, getIsMobile.value);
          closeModal();
          emit('success');
        } catch (error) {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        registerDrawer,
        registerForm,
        getTitle,
        handleSubmit,
        t,
      };
    },
  });
</script>
<style scoped></style>
