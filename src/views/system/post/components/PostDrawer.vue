<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    :title="getTitle"
    width="30%"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" :style="{ 'margin-right': '10px' }" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, computed, unref } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { BasicModal, useModalInner } from '/@/components/Modal';

  import { addPost, updatePost } from '/@/api/system/post';

  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export const formSchema: FormSchema[] = [
    {
      field: 'name',
      label: t('岗位名称'),
      required: true,
      component: 'Input',
      colProps: { span: 24 },
    },
    {
      field: 'code',
      label: t('岗位编码'),
      required: true,
      component: 'Input',
      colProps: { span: 24 },
    },
    {
      field: 'enabledMark',
      label: t('状态'),
      component: 'RadioButtonGroup',
      defaultValue: 1,
      componentProps: {
        options: [
          { label: t('启用'), value: 1 },
          { label: t('停用'), value: 0 },
        ],
      },
      colProps: { span: 24 },
    },
    {
      field: 'parentId',
      label: t('上级岗位'),
      component: 'TreeSelect',
      componentProps: {
        fieldNames: {
          label: 'name',
          key: 'id',
          value: 'id',
        },
        getPopupContainer: () => document.body,
      },
      colProps: { lg: 24, md: 24 },
    },
    {
      label: t('备注'),
      field: 'remark',
      component: 'InputTextArea',
      colProps: { span: 24 },
    },
  ];

  export default defineComponent({
    name: 'PostDrawer',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(true);
      const { notification } = useMessage();
      const rowId = ref('');
      const deptId = ref('');

      const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
        labelWidth: 90,
        schemas: formSchema,
        showActionButtonGroup: false,
      });

      const [registerDrawer, { setModalProps, closeModal }] = useModalInner((data) => {
        resetFields();
        setModalProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;
        deptId.value = data.deptId;

        updateSchema({
          field: 'parentId',
          componentProps: { treeData: data.treeData },
        });
        if (unref(isUpdate)) {
          rowId.value = data.record.id;
          setFieldsValue({
            ...data.record,
          });
        }
      });

      const getTitle = computed(() => (!unref(isUpdate) ? t('新增岗位') : t('编辑岗位')));

      async function handleSubmit() {
        try {
          const values = await validate();
          values.deptId = deptId.value;
          setModalProps({ confirmLoading: true });
          // TODO custom api
          if (!unref(isUpdate)) {
            //false 新增
            await addPost(values);
            notification.success({
              message: t('新增岗位'),
              description: t('成功'),
            }); //提示消息
          } else {
            values.id = unref(rowId);
            await updatePost(values);
            notification.success({
              message: t('编辑岗位'),
              description: t('成功'),
            }); //提示消息
          }
          closeModal();
          emit('success');
        } catch (error) {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        registerDrawer,
        registerForm,
        getTitle,
        handleSubmit,
        t,
      };
    },
  });
</script>
<style scoped>
  :deep(.ant-col:last-child .ant-form-item:not(.ant-form-item-with-help)) {
    margin-bottom: 0;
  }
</style>
