import { TreeItem } from '/@/components/Tree';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export const treeData: TreeItem[] = [
  {
    title: t('登录日志'),
    icon: 'ant-design:audit-outlined',
    key: '1',
  },
  {
    title: t('访问日志'),
    icon: 'ant-design:exception-outlined',
    key: '2',
  },
  {
    title: t('操作日志'),
    icon: 'ant-design:file-protect-outlined',
    key: '3',
  },
  {
    title: t('异常日志'),
    icon: 'ant-design:file-search-outlined',
    key: '4',
  },
  // {
  //   title: '接口日志',
  //   icon: 'ant-design:file-sync-outlined',
  //   key: '5',
  // },
  {
    title: '表单日志',
    icon: 'ant-design:file-text-outlined',
    key: '6',
  },
  {
    title: t('消息推送日志'),
    icon: 'ant-design:file-search-outlined',
    key: '7',
  },
];
