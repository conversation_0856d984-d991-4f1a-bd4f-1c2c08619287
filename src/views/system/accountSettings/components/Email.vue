<template>
  <div class="flex flex-col items-center justify-center py-8 bg-white">
    <div class="form-box">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        ref="FormRef"
      >
        <a-form-item
          label="发件人昵称"
          name="sender"
          :rules="[{ required: true, message: '请填写发件人昵称!' }]"
        >
          <a-input v-model:value="formState.sender" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="SMTP服务器"
          name="host"
          :rules="[{ required: true, message: '请填写SMTP服务器!' }]"
        >
          <a-input v-model:value="formState.host" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="SMTP端口"
          name="port"
          :rules="[{ required: true, message: '请填写SMTP端口!' }]"
        >
          <a-input v-model:value="formState.port" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="SMTP安全连接"
          name="isSsl"
          :rules="[{ required: true, message: '请填写SMTP安全连接!' }]"
        >
          <a-switch v-model:checked="formState.isSsl" />
        </a-form-item>
        <a-form-item
          label="SMTP用户名"
          name="userName"
          :rules="[{ required: true, message: '请填写SMTP用户名!' }]"
        >
          <a-input v-model:value="formState.userName" placeholder="请填写" />
        </a-form-item>
        <a-form-item
          label="SMTP密码"
          name="password"
          :rules="[{ required: true, message: '请填写SMTP密码!' }]"
        >
          <a-input v-model:value="formState.password" placeholder="请填写" />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 20 }">
          <a-button class="!ml-4" type="primary" @click="handleSubmit">
            {{ t('确认') }}
          </a-button>
        </a-form-item>
      </a-form>
    </div>
    <div class="flex justify-center"></div>
  </div>
</template>
<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { set, getInfo } from '/@/api/system/generator/accountSettings';
  import { MessageType } from '/@/enums/messageTemplate';
  import { useMessage } from '/@/hooks/web/useMessage';
  const { notification } = useMessage();
  const { t } = useI18n();
  const FormRef = ref();
  interface FormState {
    sender: string;
    host: string;
    port: number;
    isSsl: boolean;
    userName: string;
    password: string;
  }

  const formState = reactive<FormState>({
    sender: '',
    host: '',
    port: 8080,
    isSsl: false,
    userName: '',
    password: '',
  });
  onMounted(async () => {
    let res = await getInfo(MessageType.EMAIL);
    if (res && res.configJson) {
      let config = JSON.parse(res.configJson);
      if (config.sender) formState.sender = config.sender;
      if (config.host) formState.host = config.host;
      if (config.port) formState.port = config.port;
      if (config.isSsl) formState.isSsl = config.isSsl;
      if (config.userName) formState.userName = config.userName;
      if (config.password) formState.password = config.password;
    }
  });
  async function handleSubmit() {
    try {
      await FormRef.value.validate();
      await set(MessageType.EMAIL, formState);
      notification.success({
        message: t('提示'),
        description: t('配置成功'),
      }); //提示消息
    } catch (error) {}
  }
</script>
<style lang="less" scoped>
  .form-box {
    width: 100%;
    padding: 0 20px;
  }
</style>
