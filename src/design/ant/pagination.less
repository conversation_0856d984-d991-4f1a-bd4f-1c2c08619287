html[data-theme='dark'] {
  .ant-pagination {
    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item {
      background-color: rgb(255 255 255 / 4%) !important;

      a {
        color: #8b949e !important;
      }
    }

    .ant-select-arrow {
      color: @text-color-secondary !important;
    }

    .ant-pagination-item-active {
      background-color: @primary-color !important;
      border: none;
      border-radius: none !important;

      a {
        color: @white !important;
      }
    }
  }
}

.ant-pagination {
  align-items: center;

  .ant-pagination-prev,
  .ant-pagination-next {
    font-size: 12px;
    color: @text-color-base;
    border: 1px solid;
  }

  .ant-pagination-prev:hover,
  .ant-pagination-next:hover,
  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    a {
      color: @primary-color;
    }
  }

  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-item {
    margin: 0 4px !important;
    border: 1px solid #d9d9d9 !important;
    min-width: 28px !important;
    height: 28px !important;
    line-height: 26px !important;

    a {
      color: #606266;
    }

    &:last-child {
      margin-right: 0 !important;
    }
  }

  .ant-pagination-item-active {
    background-color: @primary-color !important;
    border: 1px solid @primary-color !important;
    border-radius: none !important;

    a {
      color: @white !important;
    }
  }

  .ant-pagination-options {
    margin-left: 12px;
  }

  .ant-select-selector {
    border: 1px solid #d9d9d9 !important;
    height: 28px !important;
  }

  .ant-pagination-options-quick-jumper input {
    height: 28px !important;
    margin: 0 6px;
    line-height: 28px;
    text-align: center;
  }

  .ant-select-arrow {
    color: @border-color-shallow-dark;
  }

  &-disabled {
    display: none !important;
  }
}
