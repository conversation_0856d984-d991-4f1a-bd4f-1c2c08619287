import type { ValidationRule } from 'ant-design-vue/lib/form/Form';
import type { ComponentType } from './types/index';
import { useI18n } from '/@/hooks/web/useI18n';
import { dateUtil } from '/@/utils/dateUtil';
import { isNumber, isObject } from '/@/utils/is';

const { t } = useI18n();

/**
 * @description: 生成placeholder
 */
export function createPlaceholderMessage(component: ComponentType) {
  if (component.includes('Input') || component.includes('Complete')) {
    return t('请输入');
  }
  if (component.includes('Picker')) {
    return t('请选择');
  }
  if (
    component.includes('Select') ||
    component.includes('Cascader') ||
    component.includes('Checkbox') ||
    component.includes('Radio') ||
    component.includes('Switch')
  ) {
    // return `请选择${label}`;
    return t('请选择');
  }
  return '';
}

const DATE_TYPE = ['DatePicker', 'MonthPicker', 'WeekPicker', 'TimePicker'];

function genType() {
  return [...DATE_TYPE, 'RangePicker'];
}

export function setComponentRuleType(
  rule: ValidationRule,
  component: ComponentType,
  valueFormat: string,
) {
  if (['DatePicker', 'MonthPicker', 'WeekPicker', 'TimePicker'].includes(component)) {
    rule.type = valueFormat ? 'string' : 'object';
  } else if (['RangePicker', 'Upload', 'CheckboxGroup', 'TimePicker'].includes(component)) {
    rule.type = 'array';
  } else if (['InputNumber'].includes(component)) {
    rule.type = 'number';
  }
}

export function processDateValue(attr: Recordable, component: string) {
  const { valueFormat, value } = attr;
  if (valueFormat) {
    attr.value = isObject(value) ? dateUtil(value).format(valueFormat) : value;
  } else if (DATE_TYPE.includes(component) && value) {
    attr.value = dateUtil(attr.value);
  }
}

export function handleInputNumberValue(component?: ComponentType, val?: any) {
  if (!component) return val;
  if (['Input', 'InputPassword', 'InputSearch', 'InputTextArea'].includes(component)) {
    return val && isNumber(val) ? `${val}` : val;
  }
  return val;
}

/**
 * 时间字段
 */
export const dateItemType = genType();

//默认值组件为字符串
export const defaultValueComponents = ['Input', 'InputPassword', 'InputSearch', 'InputTextArea'];

/**
 * 不需要绑定数据库字段的 组件
 */
export const noFieldComponent = ['Divider', 'Grid', 'Text', 'TextLink', 'Tab', 'RangePicker'];

/**
 * 重置表单时需要使用空数组做默认值的组件
 */
export const arrayValueComponents = ['SubForm', 'OneForOne'];

/**
 * 含有静态数据的组件 重置时如果数据源不为静态组件则传空
 */
export const staticDataComponents = ['XjrSelect', 'ApiRadioGroup', 'ApiCheckboxGroup'];

/**
 * 含有数据字典的组件
 */
export const DicDataComponents = [
  'XjrSelect',
  'ApiRadioGroup',
  'ApiCheckboxGroup',
  'AssociateSelect',
  'MultiplePopup',
];

/**
 * 表单组件双向绑定时 需要用v-model:checked 而不是v-model:value的组件
 */
export const checkedValueComponents = ['Switch'];

/**
 * 不需要将defaultValue赋值给组件
 */
export const noDefaultValueComponents = [
  'Info',
  'TimeRangePicker',
  'RangePicker',
  'AutoCodeRule',
  'Upload',
  'Dept',
];

/**
 * 不在工作流展示的组件
 */
export const noShowWorkFlowComponents = ['hiddenComponent'];

/**
 * 不在代码生成器、自定义表单展示的组件
 */
export const noShowGenerateComponents = ['hiddenComponent', 'opinion'];
