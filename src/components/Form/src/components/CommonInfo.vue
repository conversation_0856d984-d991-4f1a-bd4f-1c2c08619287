<template>
  <div>
    <a-input disabled :value="name" :size="size" :bordered="bordered" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, watchEffect } from 'vue';
  import { getDepartment, getDepartmentTree } from '/@/api/system/department';
  import { getUser } from '/@/api/system/user';
  import { getPostInfo } from '/@/api/system/post';
  import { getRole } from '/@/api/system/role';
  import { useUserStore } from '/@/store/modules/user';
  import { dateUtil } from '/@/utils/dateUtil';
  import { CommonInfoType } from '/@/components/Designer/src/types';

  const userStore = useUserStore();

  const props = defineProps({
    value: String,
    size: String,
    infoType: Number,
    bordered: {
      type: Boolean,
      default: true,
    },
    loadAgain: {
      type: Boolean,
      default: false,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    isShowAllName: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value']);

  const name = ref<string>();

  watchEffect(async () => {
    if (props && props.value) {
      //当前用户
      if (props.infoType === CommonInfoType.USER_NAME) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.id || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.name;
          if (props.loadAgain) emit('update:value', userStore.getUserInfo.id);
        } else {
          //如果不是当前登陆人  需要用户id  查询当前用户信息
          const userInfo = await getUser(props.value);
          name.value = userInfo.name;
        }
      }

      //当前部门
      if (props.infoType === CommonInfoType.DEPT_NAME) {
        //判断传入的值 是不是当前登陆人的部门 或需要二次加载  就不需要发请求获取用户信息了
        if (
          (props.value === userStore.getUserInfo.departmentId || props.loadAgain) &&
          !props.isView
        ) {
          if (props.isShowAllName) {
            const treeData = await getDepartmentTree();
            const info = getAllParentNodes(treeData, userStore.getUserInfo.departmentId);
            name.value = info.join('/');
          } else {
            name.value = userStore.getUserInfo.departmentName;
          }
          if (props.loadAgain) emit('update:value', userStore.getUserInfo.departmentId);
        } else {
          //如果不是当前登陆人  需要部门id  查询部门信息
          if (props.isShowAllName) {
            const treeData = await getDepartmentTree();
            const info = getAllParentNodes(treeData, props.value);
            name.value = info.join('/');
          } else {
            const dept = await getDepartment(props.value);
            name.value = dept.name;
          }
        }
      }

      //当前时间
      if (props.infoType === CommonInfoType.TIME) {
        //如果是时间 需要二次加载展示当前时间  不需要则默认显示参数时间
        name.value =
          props.loadAgain && !props.isView
            ? dateUtil(new Date()).format('YYYY-MM-DD HH:mm:ss')
            : props.value;
        if (props.loadAgain && !props.isView) emit('update:value', name.value);
      }

      //当前登录人编码
      if (props.infoType === CommonInfoType.USER_CODE) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.code || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.code;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }

      //当前登录人电话
      if (props.infoType === CommonInfoType.USER_MOBILE) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.mobile || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.mobile;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }

      //当前登录人邮箱
      if (props.infoType === CommonInfoType.USER_EMAIL) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.email || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.email;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }

      //当前登录人岗位
      if (props.infoType === CommonInfoType.POST_NAME) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.postId || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.postName;
          if (props.loadAgain) emit('update:value', userStore.getUserInfo.postId);
        } else {
          const post = await getPostInfo(props.value);
          name.value = post.name;
        }
      }

      //当前登录人角色
      if (props.infoType === CommonInfoType.ROLE_NAME) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.roleId || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.roleName;
          if (props.loadAgain) emit('update:value', userStore.getUserInfo.roleId);
        } else {
          const role = await getRole(props.value);
          name.value = role.name;
        }
      }

      //当前登录人联系地址
      if (props.infoType === CommonInfoType.USER_ADDRESS) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if ((props.value === userStore.getUserInfo.address || props.loadAgain) && !props.isView) {
          name.value = userStore.getUserInfo.address;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }

      //当前登录人组织ID
      if (props.infoType === CommonInfoType.DEPT_ID) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if (
          (props.value === userStore.getUserInfo.departmentId || props.loadAgain) &&
          !props.isView
        ) {
          name.value = userStore.getUserInfo.departmentId;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }

      //当前登录人组织编码
      if (props.infoType === CommonInfoType.DEPT_CODE) {
        //判断传入的值 是不是当前登录人 或需要二次加载  就不需要发请求获取用户信息了
        if (
          (props.value === userStore.getUserInfo.departments[0].code || props.loadAgain) &&
          !props.isView
        ) {
          name.value = userStore.getUserInfo.departments[0].code;
          if (props.loadAgain) emit('update:value', name.value);
        } else {
          name.value = props.value;
        }
      }
    } else {
      //当前用户
      if (props.infoType === CommonInfoType.USER_NAME) {
        name.value = userStore.getUserInfo.name;
        emit('update:value', userStore.getUserInfo.id);
      }

      //当前部门
      if (props.infoType === CommonInfoType.DEPT_NAME) {
        if (props.isShowAllName) {
          const treeData = await getDepartmentTree();
          const info = getAllParentNodes(treeData, userStore.getUserInfo.departmentId);
          name.value = info.join('/');
        } else {
          name.value = userStore.getUserInfo.departmentName;
        }
        emit('update:value', userStore.getUserInfo.departmentId);
      }

      //当前时间
      if (props.infoType === CommonInfoType.TIME) {
        name.value = dateUtil(new Date()).format('YYYY-MM-DD HH:mm:ss');
        emit('update:value', name.value);
      }

      //当前登录人编码
      if (props.infoType === CommonInfoType.USER_CODE) {
        name.value = userStore.getUserInfo.code;
        emit('update:value', name.value);
      }

      //当前登录人电话
      if (props.infoType === CommonInfoType.USER_MOBILE) {
        name.value = userStore.getUserInfo.mobile;
        emit('update:value', name.value);
      }

      //当前登录人邮箱
      if (props.infoType === CommonInfoType.USER_EMAIL) {
        name.value = userStore.getUserInfo.email;
        emit('update:value', name.value);
      }

      //当前登录人岗位
      if (props.infoType === CommonInfoType.POST_NAME) {
        name.value = userStore.getUserInfo.postName;
        emit('update:value', userStore.getUserInfo.postId);
      }

      //当前登录人角色
      if (props.infoType === CommonInfoType.ROLE_NAME) {
        name.value = userStore.getUserInfo.roleName;
        emit('update:value', userStore.getUserInfo.roleId);
      }

      //当前登录人联系地址
      if (props.infoType === CommonInfoType.USER_ADDRESS) {
        name.value = userStore.getUserInfo.address;
        emit('update:value', name.value);
      }

      //当前登录人组织ID
      if (props.infoType === CommonInfoType.DEPT_ID) {
        name.value = userStore.getUserInfo.departmentId;
        emit('update:value', name.value);
      }

      //当前登录人组织编码
      if (props.infoType === CommonInfoType.DEPT_CODE) {
        name.value = userStore.getUserInfo.departments[0].code;
        emit('update:value', name.value);
      }
    }
  });

  function getAllParentNodes(list, id) {
    for (let i in list) {
      if (list[i].id === id) {
        return [list[i].name];
      }
      if (list[i].children?.length) {
        let node = getAllParentNodes(list[i].children, id);
        if (node) {
          node.unshift(list[i].name);
          return node;
        }
      }
    }
  }
</script>
