<template>
  <BasicTable @register="registerTable" />
</template>
<script lang="ts" setup>
  import { inject, watchEffect } from 'vue';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { getDicDetailList } from '/@/api/system/dic';
  import { apiConfigFunc, camelCaseString, isValidJSON } from '/@/utils/event/design';

  const props = defineProps({
    preloadType: String,
    apiConfig: Object,
    itemId: String,
    isPagination: Boolean,
    showIndex: Boolean,
    showFormBorder: Boolean,
  });

  const formModel = inject<any>('formModel', null);
  const isCamelCase = inject<boolean>('isCamelCase', false);

  watchEffect(async () => {
    if (props.preloadType === 'api' && props.apiConfig?.apiParams) {
      props.apiConfig.apiParams.forEach((params) => {
        params.tableInfo?.forEach((o) => {
          if (o.bindType == 'data') {
            let val = isValidJSON(o.value);
            let field = '';
            if (val && val.bindField) {
              field = !isCamelCase ? val.bindField : camelCaseString(val.bindField);
              formModel && formModel[field];
            }
          }
        });
      });
    }
    let dataSource: any[] = [];
    let columns: BasicColumn[] = [];
    let rowKey = 'key';
    if (props.preloadType === 'dic') {
      if (props.itemId) dataSource = await getDicDetailList({ itemId: props.itemId! });
    } else if (props.preloadType === 'api') {
      dataSource = await apiConfigFunc(props.apiConfig, isCamelCase, formModel);
    }
    if (dataSource?.length) {
      const dataKeys = Object.keys(dataSource[0]);
      if (dataKeys.includes('id')) rowKey = 'id';
      columns = dataKeys.map((item) => {
        return {
          title: item,
          dataIndex: item,
        };
      });
    }
    setProps({
      dataSource,
      columns,
      rowKey,
      pagination: props.isPagination,
      showIndexColumn: props.showIndex,
      bordered: props.showFormBorder,
    });
  });

  const [registerTable, { setProps }] = useTable({
    striped: false,
  });
</script>
