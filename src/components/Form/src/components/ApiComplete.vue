<template>
  <a-auto-complete
    v-bind="$attrs"
    :options="options"
    :allow-clear="true"
    @select="onSelect"
    @search="onSearch"
  />
</template>
<script lang="ts" setup>
  import { ref, unref, watch } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { isFunction } from '/@/utils/is';
  import { get } from 'lodash-es';
  import { getDicDetailList } from '/@/api/system/dic';
  import { getDatasourceData } from '/@/api/system/datasource';

  const options = ref<{ value: string; label: string }[]>([]);

  const loading = ref(false);
  const isFirstLoad = ref(true);

  const emit = defineEmits(['update:value']);

  const props = defineProps({
    value: String,
    api: {
      type: Function as PropType<(arg?: Recordable) => Promise<{ value: string; text: string }[]>>,
      default: null,
    },
    // api params
    params: {
      type: [Array, Object, String, Number],
    },
    staticOptions: {
      type: Array,
    },
    resultField: propTypes.string.def(''),
    labelField: propTypes.string.def('label'),
    valueField: propTypes.string.def('value'),
    immediate: propTypes.bool.def(true),
    alwaysLoad: propTypes.bool.def(false),
    //数据来源 默认为空  如果不为空 则参数 api
    datasourceType: String,
  });

  watch(
    () => props.value,
    (val) => {
      unref(isFirstLoad) && fetch(val);
    },
    { deep: true },
  );

  async function fetch(_value) {
    let api;
    if (props.datasourceType) {
      if (props.datasourceType === 'dic') {
        api = getDicDetailList;
      }
      if (props.datasourceType === 'datasource') {
        api = getDatasourceData;
      }
    } else {
      api = props.api;
    }

    options.value = props.staticOptions as { value: string; label: string }[];

    if (!api || !isFunction(api)) return;
    try {
      loading.value = true;
      const res = await api(props.params);
      isFirstLoad.value = false;

      if (Array.isArray(res)) {
        options.value = res;
      }
      if (props.resultField) {
        options.value = get(res, props.resultField) || [];
      }
    } catch (error) {
      console.warn(error);
    } finally {
      loading.value = false;
    }
  }

  async function onSearch(value) {
    fetch(value);
  }

  async function onSelect(value) {
    emit('update:value', value);
  }
</script>
