<template>
  <div>
    <div class="select-btn flex">
      <a-button type="primary" @click="add" v-if="!disabled" class="mr-2">
        <PlusOutlined />
        {{ t('新增') }}
      </a-button>
      <a-button
        type="primary"
        @click="multipleDialog = true"
        v-if="useSelectButton"
        class="select-btn mr-2"
        :disabled="disabled"
      >
        {{ buttonName }}
      </a-button>
      <ImpExcel @success="importTable" v-if="isImport">
        <a-button type="primary" :disabled="disabled"> 导入 </a-button>
      </ImpExcel>
      <a-button
        type="primary"
        @click="exportTable"
        v-if="isExport"
        :disabled="disabled"
        class="ml-2"
      >
        导出
      </a-button>
    </div>
    <a-table
      :columns="headColums.length > 0 ? headColums : columns"
      :bordered="showFormBorder"
      :pagination="false"
      :data-source="data"
      :scroll="{ x: 'max-content' }"
      :expandedRowKeys="expandedRowKeys"
      @expand="onExpand"
    >
      <template #summary>
        <a-table-summary-row v-if="columns.some((x) => x.componentProps?.subTotal)">
          <a-table-summary-cell v-for="(column, idx) in columns" :key="idx">
            <a-typography-text keyboard v-if="column.componentProps?.subTotal">
              {{ t('合计：') }} {{ sum(data.map((x) => x[column.dataIndex as string])) }}
            </a-typography-text>
          </a-table-summary-cell>
        </a-table-summary-row>
      </template>
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key !== 'action'">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <FormItem
            :name="[mainKey, index, column.dataIndex]"
            :rules="rules(column, record, index)"
            :validateTrigger="['blur', 'change']"
            v-else
          >
            <!---如果是checked一类的组件-->
            <template v-if="checkedValueComponents.includes(column.componentType)">
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                v-model:checked="record[column.dataIndex]"
              />
            </template>
            <!---如果是RangePicker组件-->
            <template
              v-else-if="
                column.componentType === 'RangePicker' || column.componentType === 'TimeRangePicker'
              "
            >
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                v-model:startField="column.dataIndex.split(',')[0]"
                v-model:endField="column.dataIndex.split(',')[1]"
                v-model:value="record[column.dataIndex]"
                v-model:record="data[index]"
                :mainKey="mainKey"
              />
            </template>

            <!---如果是渲染函数组件-->
            <template v-else-if="column.componentType === 'Render'">
              <component
                :bordered="showComponentBorder"
                :is="
                  column.render({
                    model: record,
                    field: column.dataIndex,
                    rules: column.rules,

                    componentProps: getComponentsProps(column, record, index),
                  })
                "
              />
              <!-- {{ column.render({ model: record, field: column.dataIndex }) }} -->
            </template>

            <template v-else-if="column.componentType === 'Slot'">
              <!-- {{ props.slots![column.slot] }} -->
              <component
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column.componentProps, column.dataIndex, record, index)"
                :is="props.slots![column.slot]"
                :formModel="formModel"
                :field="column.dataIndex"
                :index="index"
                :mainKey="mainKey"
              />
            </template>

            <template v-else-if="column.key !== 'index'">
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                :index="index"
                :mainKey="mainKey"
                v-model:value="record[column.dataIndex]"
              />
            </template>
          </FormItem>
        </template>
        <template v-if="column.key === 'action' && !disabled">
          <MinusCircleOutlined @click="remove(index)" style="padding-bottom: 20px" />
        </template>
      </template>
      <template #expandedRowRender="{ record, index }" v-if="sunFormInfo">
        <SunForm
          :disabled="disabled"
          :form-api="formApi"
          :subFormIndex="index"
          :subFormList="mainKey"
          v-bind="sunFormInfo.componentProps"
          v-model:value="record[sunFormInfo.mainKey]"
        />
      </template>
    </a-table>

    <FormItemRest>
      <MultipleSelect
        ref="MultipleSelectRef"
        v-model:multipleDialog="multipleDialog"
        :params="{ itemId }"
        :datasourceType="preloadType"
        :dicOptions="dicOptions"
        :apiConfig="apiConfig"
        :isSubFormUse="true"
        @submit="renderSubFormList"
        popupType="preload"
      />
    </FormItemRest>
  </div>
</template>
<script lang="ts" setup>
  import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
  import { Form } from 'ant-design-vue';
  import { inject, onMounted, ref, unref, watch, nextTick, computed } from 'vue';
  import { SubFormColumn } from '../types';
  import { componentMap } from '../componentMap';
  import { DicDataComponents, checkedValueComponents, staticDataComponents } from '../helper';
  import { MultipleSelect } from '/@/components/MultiplePopup';
  import { isFunction, cloneDeep, isBoolean, sum, upperFirst, isNil } from 'lodash-es';
  import { deepMerge } from '/@/utils';
  import { apiConfigFunc, moneyChineseData } from '/@/utils/event/design';
  import { getDicDetailList } from '/@/api/system/dic';
  import { getDepartment, getDepartmentTree } from '/@/api/system/department';
  import { getUserMulti, getUser, getAllUserList } from '/@/api/system/user';
  import { getAreaList } from '/@/api/system/area';
  import { getInfo } from '/@/api/system/generator/treeStructure';
  import { TreeStructureType } from '/@/enums/treeStructure';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { MutipleHeadInfo } from '/@/components/Designer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { FormActionType } from '/@/components/Form/src/types/form';
  import XLSX from 'xlsx-js-style';
  import { ImpExcel, ExcelData } from '/@/components/Excel';
  import SunForm from './SunForm.vue';

  const { t } = useI18n();
  // 用于包裹弹窗的form组件 因为一个FormItem 只能收集一个表单组件  所以弹窗的form 必须排除
  // const FormItemRest = Form.ItemRest;
  const FormItem = Form.Item;
  const FormItemRest = Form.ItemRest;

  //const prereloadData = ref<Recordable[] | undefined>(undefined);
  const multipleDialog = ref<boolean>(false);
  const emit = defineEmits(['change', 'update:value']);

  const props = defineProps({
    /**
     * 如果是编辑状态 默认现实的值
     */
    value: { type: Array as PropType<Recordable[]>, default: () => [] },
    /**
     * 是否预加载
     */
    preload: {
      type: Boolean,
      default: false,
    },
    /**
     * 预加载 类型 开启 preload  为true 才有用
     * datasource || dataitem
     */
    preloadType: { type: String, default: null },

    /**
     * 需要绑定主表的字段  用于验证  必填
     */
    mainKey: { type: String, required: true },
    /**
     * 子表单配置
     */
    columns: {
      type: Array as PropType<SubFormColumn[]>,
      required: true,
    },
    /**
     * 是否禁用所有组件
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    /**
     * 预加载数据为api时使用
     */
    apiConfig: Object,
    /**
     * 预加载数据为数据字典时使用
     */
    itemId: String,
    dicOptions: Array,
    /**
     * 是否使用按钮选数据
     */
    useSelectButton: Boolean,
    /**
     * 选数据按钮名称
     */
    buttonName: String,
    //是否显示表格边框
    showFormBorder: Boolean,
    //是否显示组件边框
    showComponentBorder: Boolean,
    //是否展示序号
    showIndex: Boolean,
    //add before hooks
    addBefore: Function,
    //add after hooks
    addAfter: Function,
    //表头合并数据
    multipleHeads: { type: Array as PropType<MutipleHeadInfo[]> },
    formApi: {
      type: Object as PropType<FormActionType>,
    },
    isExport: {
      type: Boolean,
      default: false,
    },
    isImport: {
      type: Boolean,
      default: false,
    },
    tableName: {
      type: String,
      default: '',
    },
    //插槽节点
    slots: [Object, Array],
  });
  const data = ref<Recordable[]>([]);
  //缓存字段是否disable
  const cacheMap = new Map<String, number[]>();
  const expandedRowKeys = ref<number[]>([]);
  const headColums = ref<MutipleHeadInfo[]>([]); // 多表头
  const originHeads = ref<MutipleHeadInfo[]>([]); // 多表头源数据
  const columns = ref<SubFormColumn[]>(props.columns);
  const isCamelCase = inject<boolean>('isCamelCase', false);
  const apiComponent = ['XjrSelect', 'AssociateSelect', 'ApiRadioGroup'];
  // 注入表单数据
  const formModel = inject<any>('formModel', null);

  const { notification } = useMessage();

  const sunFormInfo = computed(() => {
    return props.columns.find((x) => x.componentType === 'SunForm');
  });

  onMounted(() => {
    data.value = cloneDeep(props.value);
    if (props.showIndex && columns.value && columns.value[0].key !== 'index') {
      columns.value.unshift({
        title: '序号',
        key: 'index',
        align: 'center',
        width: 60,
      });
    }
    columns.value = filterColum(columns.value);
    nextTick(() => {
      //处理多表头
      if (props.multipleHeads && props.multipleHeads.length > 0) {
        originHeads.value = cloneDeep(props.multipleHeads);

        getColumns(columns.value);
        //过滤权限为不显示的组件
        filterHeads(headColums.value);
      }
    });
  });

  watch(
    () => props.columns,
    (val) => {
      // console.log('watch props.columns');
      columns.value = filterColum(val);
    },
  );

  watch(
    () => data.value,
    (val, oldVal) => {
      if (!oldVal.length && val.length) {
        val.forEach((item, index) => {
          item.key = index;
          if (sunFormInfo.value && item[sunFormInfo.value!.mainKey!]?.length) {
            expandedRowKeys.value.push(item.key);
          }
        });
      }
    },
    {
      deep: true,
    },
  );

  watch(
    () => props.value,
    (v) => {
      // console.log('watch props.value', props.value);
      data.value = v;
      // const rangeComponents = props.columns.filter((column) =>
      //   column.componentType?.includes('Range'),
      // );
      // if (rangeComponents.length && formModel) {
      //   rangeComponents.forEach((item) => {
      //     data.value.forEach((x) => {
      //       if (x[(item.dataIndex as string)?.split(',')[0]]) {
      //         x[item.dataIndex as string] = [
      //           x[(item.dataIndex as string)?.split(',')[0]],
      //           x[(item.dataIndex as string)?.split(',')[1]],
      //         ];
      //       }
      //     });
      //   });
      // }

      //要保证在预加载之后在emit  不然预加载数据不会绑定到表单数据中
      emit('change', unref(data));
      emit('update:value', unref(data));
    },
    { deep: true },
  );
  function getColumns(children) {
    let headsColumn: MutipleHeadInfo[] = [];
    let leafNode = 0;
    children.forEach((o) => {
      o.isleaf = false;
      let flag = false;
      for (let i = 0; i < originHeads.value.length; i++) {
        let k = originHeads.value[i];

        let idx = k.children.findIndex((j) => {
          return j == o.key || j.key == o.key;
        });
        if (idx >= 0) {
          o.isleaf = true;
          flag = true;
          leafNode += 1;
          k.children.splice(idx, 1, o);
          let obj = headsColumn.find((j) => {
            return j.key == k.key;
          });
          if (!obj) headsColumn.push(k);
          break;
        } else {
          flag = false;
        }
      }
      if (!flag) {
        let obj = headsColumn.find((j) => {
          return j.key == o.key;
        });
        if (!obj) headsColumn.push(o);
      }
    });
    if (leafNode > 0) {
      //继续循环
      getColumns(headsColumn);
    } else {
      headColums.value = headsColumn;
    }
  }
  function filterHeads(children) {
    children.forEach((k, i) => {
      if (typeof k == 'string') {
        children.splice(i, 1);
      } else if (k.children) {
        filterHeads(k.children);
      }
    });
  }

  const add = () => {
    //给各个组件赋默认值
    const pushObj: Recordable = {};
    if (sunFormInfo.value) {
      pushObj[sunFormInfo.value.mainKey!] = [];
    }
    //新增数据钩子
    if (isFunction(props.addBefore)) {
      props.addBefore(formModel, pushObj);
    }

    props.columns.forEach((column, index) => {
      //判断是否为操作菜单  并且设置了默认值
      if ((column.key === 'index' && index === 0) || column.componentType === 'SunForm') return;
      if (column.key !== 'action') {
        if (column.componentType === 'RangePicker' || column.componentType === 'TimeRangePicker') {
          handleSetRangeTimeValue(pushObj, column.dataIndex as string);
        } else if (
          (column.componentType &&
            staticDataComponents.includes(column.componentType) &&
            (column.componentProps as any)?.datasourceType === 'staticData') ||
          (column.componentType &&
            DicDataComponents.includes(column.componentType) &&
            (column.componentProps as any)?.datasourceType === 'dic')
        ) {
          let { defaultSelect } = column.componentProps as any;
          pushObj[column!.dataIndex as string] = defaultSelect;
        } else {
          pushObj[column!.dataIndex as string] = column.defaultValue;
        }
      }
    });
    pushObj.key = data.value.length;
    data.value.push(pushObj);
    emit('change', unref(data));
    emit('update:value', unref(data));
    //新增数据钩子
    if (isFunction(props.addAfter)) {
      props.addAfter(formModel, pushObj);
    }
  };

  const remove = (index) => {
    data.value.splice(index, 1);
    emit('change', unref(data));
    emit('update:value', unref(data));
  };

  const renderSubFormList = async (list) => {
    list?.forEach((x) => {
      const dataObj = {};
      props.columns?.map((item) => {
        if (!item?.dataIndex) return;
        dataObj[item.dataIndex as string] = item.componentProps?.prestrainField
          ? x[item.componentProps.prestrainField]
          : null;
      });

      data.value.push(dataObj);
    });
    emit('change', unref(data));
    emit('update:value', unref(data));
  };

  watch(
    () => props.preloadType,
    async (val) => {
      if (!!val && !props.useSelectButton) {
        let res;
        if (props.preloadType === 'api') {
          res = await apiConfigFunc(props.apiConfig, false, formModel);
        } else if (props.preloadType === 'dic') {
          res = await getDicDetailList({ itemId: props.itemId });
        }
        renderSubFormList(res);
      }
    },
    {
      immediate: true,
    },
  );

  /**
   * @author: tzx
   * @description: 设置RangeTime的值
   *
   */
  function handleSetRangeTimeValue(values: Recordable, field: string) {
    const startTimeKey = field.split(',')[0];
    const endTimeKey = field.split(',')[1];
    values[startTimeKey] = '';
    values[endTimeKey] = '';

    return values;
  }
  const rules = (column, record, index) => {
    if (column.key === 'index') return;
    const requiredRule = {
      required: getComponentsProps(column, record, index)?.required || false,
      message: `${column.title}是必填项`,
    };
    const rulesList = cloneDeep(getComponentsProps(column, record, index)?.rules);
    if (!rulesList) return [requiredRule];
    rulesList?.map((item) => (item.pattern = eval(item.pattern)));
    return [...rulesList, requiredRule];
  };

  const getComponentsProps = (column, record, index) => {
    let componentProps = column?.componentProps;
    const dataIndex = column?.dataIndex;
    if (!componentProps) return;
    if (isFunction(componentProps)) {
      componentProps = componentProps({ updateSchema, formModel, record, index, disableRow }) ?? {};
    } else {
      componentProps = cloneDeep(column?.componentProps);
      if (componentProps['events']) {
        for (const eventKey in componentProps['events']) {
          try {
            const event = new Function(
              'schema',
              'formModel',
              'formActionType',
              `${componentProps['events'][eventKey]}`,
            );

            componentProps['on' + upperFirst(eventKey)] = function () {
              event(column, formModel, props.formApi);
            };
          } catch (error) {
            console.log('error', error);
            notification.error({
              message: 'Tip',
              description: '触发事件填写有误！',
            });
          }
        }
      }
    }
    if (isBoolean(props.disabled)) {
      componentProps['disabled'] = componentProps['disabled'] || props.disabled;
    } else if (isBoolean(componentProps['disabled'])) {
      componentProps['disabled'] =
        cacheMap.has(dataIndex) && cacheMap.get(dataIndex)?.includes(index) ? true : false;
    }

    return componentProps as Recordable;
  };

  // const getDisable = (column, index) => {
  //   console.log('getDisable', cacheMap.get(column.dataIndex), column.componentProps.disabled);
  //   return cacheMap.has(column.dataIndex) && cacheMap.get(column.dataIndex)?.includes(index)
  //     ? true
  //     : false;
  // };

  const onExpand = (expanded, record) => {
    if (expanded) {
      expandedRowKeys.value.push(record.key);
    } else {
      if (expandedRowKeys.value.length) {
        expandedRowKeys.value = expandedRowKeys.value.filter((x) => x !== record.key);
      }
    }
  };

  const disableRow = (column: SubFormColumn, index?: number) => {
    let col: any = columns.value.find((x) => x.dataIndex == column.dataIndex);
    if (col && index !== undefined) {
      //如果当前字段已经缓存
      if (cacheMap.has(col.dataIndex)) {
        let indexArray = cacheMap.get(col.dataIndex);

        if (column.componentProps.disabled) {
          if (!indexArray) {
            indexArray = [index];
          } else {
            if (!indexArray.includes(index)) {
              indexArray.push(index);
            }
          }
        } else {
          if (indexArray) {
            if (indexArray.findIndex((x) => x === index) > -1) {
              indexArray.splice(
                indexArray.findIndex((x) => x === index),
                1,
              );
            }
          }
        }
      } else {
        if (column.componentProps.disabled) {
          cacheMap.set(col.dataIndex, [index]);
        }
      }
    }
  };

  const updateSchema = (column: SubFormColumn, index?: number) => {
    let col: any = columns.value.find((x) => x.dataIndex == column.dataIndex);
    if (col && index !== undefined) {
      //如果当前字段已经缓存
      if (cacheMap.has(col.dataIndex)) {
        let indexArray = cacheMap.get(col.dataIndex);

        if (column.componentProps.disabled) {
          if (!indexArray) {
            indexArray = [index];
          } else {
            indexArray.push(index);
          }
        } else {
          if (indexArray) {
            if (indexArray.findIndex((x) => x === index) > -1) {
              indexArray.splice(
                indexArray.findIndex((x) => x === index),
                1,
              );
            }
          }
        }
      } else {
        if (column.componentProps.disabled) {
          cacheMap.set(col.dataIndex, [index]);
        }
      }
    }
    return col || col?.length ? deepMerge(col, column) : col;
  };
  function filterColum(column) {
    return column.filter((o) => {
      return (
        o.componentType !== 'SunForm' &&
        (o.key == 'action' ||
          o.key == 'index' ||
          (((isBoolean(o.show) && o.show) || !isBoolean(o.show)) &&
            isBoolean(o.componentProps?.isShow) &&
            o.componentProps?.isShow) ||
          !isBoolean(o.componentProps?.isShow))
      );
    });
  }

  const exportTable = async () => {
    const fieldList = {};
    data.value.map((item) => {
      for (const k in item) {
        if (!fieldList[k]) {
          fieldList[k] = [];
        }
        fieldList[k].push(item[k]);
      }
    });
    const columnsInfo = columns.value.filter((item) => item.key !== 'action');
    const exportValue: Array<string>[] = [[]];
    columnsInfo.map((item) => {
      const requiredSign = '(*)';
      const title = item.componentProps.required ? item.title + requiredSign : item.title;
      if (item.componentType?.includes('RangePicker')) {
        const column = [`${title}开始时间`, `${title}结束时间`];
        exportValue[0].push(...column);
      } else {
        exportValue[0].push(title!);
      }
    });
    if (Object.keys(fieldList).length) {
      for (const col of columnsInfo) {
        const componentProps = col.componentProps;
        const componentType = col.componentType;
        for (const [index, val] of fieldList[col.dataIndex!]?.entries()) {
          let value = isNil(val) ? '' : val;
          if (
            apiComponent.includes(componentType!) ||
            (componentType === 'MultiplePopup' && col.componentProps.popupType === 'associate')
          ) {
            //下拉、联想下拉、单选、联想弹层
            if (componentProps.datasourceType === 'staticData') {
              const info = componentProps.staticOptions.find((option) => option.value === val);
              value = info?.label || val;
            } else if (componentProps.datasourceType === 'api') {
              const options = await apiConfigFunc(
                componentProps.apiConfig,
                isCamelCase,
                formModel,
                index,
              );
              const info = options?.find((option) => option.value === val);
              value = info?.label || val;
            } else if (componentProps.datasourceType === 'dic') {
              const options = await getDicDetailList(componentProps.params);
              const info = options.find((option) => option.value === val);
              value = info?.name || val;
            }
          } else if (
            componentType === 'ApiCheckboxGroup' ||
            (componentType === 'MultiplePopup' && componentProps.popupType === 'multiple')
          ) {
            //多选、多选弹层
            if (val) {
              const valArr = val.split(',');
              if (componentProps.datasourceType === 'staticData') {
                const info = componentProps.staticOptions.filter((option) =>
                  valArr.includes(option.value),
                );
                if (info.length) {
                  const labelArr = info.map((item) => item.label);
                  value = labelArr.toString();
                }
              } else if (componentProps.datasourceType === 'api') {
                const options = await apiConfigFunc(
                  componentProps.apiConfig,
                  isCamelCase,
                  formModel,
                  index,
                );
                const info = options?.filter((option) => valArr.includes(option.value));
                if (info.length) {
                  const labelArr = info.map((item) => item.label);
                  value = labelArr.toString();
                }
              } else if (componentProps.datasourceType === 'dic') {
                const options = await getDicDetailList(componentProps.params);
                const info = options.filter((option) => valArr.includes(option.value));
                if (info.length) {
                  const labelArr = info.map((item) => item.name);
                  value = labelArr.toString();
                }
              }
            }
          } else if (componentType === 'Info') {
            //信息体
            if (componentProps.infoType === 0) {
              //用户
              const userInfo = await getUser(val);
              value = userInfo.name;
            }
            if (componentProps.infoType === 1) {
              //部门
              const dept = await getDepartment(val);
              value = dept.name;
            }
          } else if (componentType === 'User') {
            //人员
            const users = await getUserMulti(val);
            value = users.map((item) => item.name).toString();
          } else if (componentType === 'Dept') {
            //组织架构
            if (val) {
              const dept = await getDepartment(val);
              value = dept.name;
            }
          } else if (componentType === 'MoneyChineseInput') {
            //货币大写
            value = moneyChineseData(val);
          } else if (componentType === 'ApiCascader') {
            //级联
            const options = await apiConfigFunc(
              componentProps.apiConfig,
              isCamelCase,
              formModel,
              index,
            );
            const valArr = val?.split(',');
            if (valArr?.length) {
              const labelArr = getCascaderLabel(options, valArr);
              value =
                componentProps.showFormat === 'all'
                  ? labelArr.join(componentProps.separator)
                  : labelArr[labelArr.length - 1];
            }
          } else if (componentType === 'Area') {
            //行政区域
            const options = await getAreaList({ id: '0' });
            const valArr = val?.split(',');
            if (valArr?.length) {
              const labelArr = await getAreaLabel(options, valArr);
              value = labelArr.join('/');
            }
          } else if (componentType === 'TreeSelectComponent') {
            //树选择
            if (componentProps.treeConfig.id) {
              let formState = await getInfo(componentProps.treeConfig?.id);
              if (formState && formState.config) {
                let config = JSON.parse(formState.config);
                let options = config.staticData;
                if (formState.type == TreeStructureType.API) {
                  const res = await apiConfigFunc(
                    config.apiData.apiConfig,
                    isCamelCase,
                    formModel,
                    index,
                  );
                  options = res?.data || [];
                }
                const valArr = val?.split(',');
                const labelArr = [];
                for (const item of valArr) {
                  const valInfo = item?.split('---');
                  getTreeSelectLabel(options, valInfo, labelArr);
                }
                value = labelArr.toString();
              }
            }
          }
          if (!exportValue[index + 1]) {
            exportValue[index + 1] = [];
          }
          if (componentType?.includes('RangePicker')) {
            //时间范围、日期范围
            exportValue[index + 1].push(...value);
          } else {
            exportValue[index + 1].push(value);
          }
        }
      }
    }
    const dataInfo = XLSX.utils.aoa_to_sheet(exportValue);
    /**设置标题头背景色 */
    for (const key in dataInfo) {
      // 第一行，表头
      if (key.replace(/[^0-9]/gi, '') === '1') {
        dataInfo[key].s = {
          fill: {
            //背景色
            fgColor: {
              rgb: 'C0C0C0',
            },
          },
          font: {
            //字体
            name: '宋体',
            sz: 12,
            bold: true,
          },
          border: {
            //边框
            bottom: {
              style: 'thin',
              color: '#9a9a9a',
            },
            right: {
              style: 'thin',
              color: '#9a9a9a',
            },
          },
          alignment: {
            horizontal: 'center', //水平居中
            vertical: 'center',
          },
        };
      }
    }
    dataInfo['!rows'] = [{ hpx: 25 }];

    // 创建工作簿
    const wb = XLSX.utils.book_new();
    // 将工作表放入工作簿中
    XLSX.utils.book_append_sheet(wb, dataInfo);
    // 生成文件并下载
    XLSX.writeFile(wb, `${props.tableName}.xlsx`);
  };

  const deptId = ref('');
  const importTable = async (excelDataList: ExcelData[]) => {
    const columnsInfo = columns.value.filter((item) => item.key !== 'action');
    const tableInfo: any = [];
    for (const [index, item] of excelDataList[0].results?.entries()) {
      const returnItem = {};
      for (const col of columnsInfo) {
        const componentProps = col.componentProps;
        const componentType = col.componentType;
        const val = item[col.title!];
        let value = val;
        if (!item[col.title!]) {
        } else if (
          apiComponent.includes(componentType!) ||
          (componentType === 'MultiplePopup' && col.componentProps.popupType === 'associate')
        ) {
          //下拉、联想下拉、单选、联想弹层
          if (componentProps.datasourceType === 'staticData') {
            const info = componentProps.staticOptions.find((option) => option.label === val);
            value = info?.value || val;
          } else if (componentProps.datasourceType === 'api') {
            const options = await apiConfigFunc(
              componentProps.apiConfig,
              isCamelCase,
              formModel,
              index,
            );
            const info = options?.find((option) => option.label === val);
            value = info?.value || val;
          } else if (componentProps.datasourceType === 'dic') {
            const options = await getDicDetailList(componentProps.params);
            const info = options.find((option) => option.name === val);
            value = info?.value || val;
          }
        } else if (
          componentType === 'ApiCheckboxGroup' ||
          (componentType === 'MultiplePopup' && componentProps.popupType === 'multiple')
        ) {
          //多选、多选弹层
          const valArr = val.split(',');
          if (componentProps.datasourceType === 'staticData') {
            const info = componentProps.staticOptions.filter((option) =>
              valArr.includes(option.label),
            );
            if (info.length) {
              const labelArr = info.map((item) => item.value);
              value = labelArr.toString();
            }
          } else if (componentProps.datasourceType === 'api') {
            const options = await apiConfigFunc(
              componentProps.apiConfig,
              isCamelCase,
              formModel,
              index,
            );
            const info = options?.filter((option) => valArr.includes(option.label));
            if (info.length) {
              const labelArr = info.map((item) => item.value);
              value = labelArr.toString();
            }
          } else if (componentProps.datasourceType === 'dic') {
            const options = await getDicDetailList(componentProps.params);
            const info = options.filter((option) => valArr.includes(option.name));
            if (info.length) {
              const labelArr = info.map((item) => item.value);
              value = labelArr.toString();
            }
          }
        } else if (componentType === 'Info') {
          //信息体
          if (componentProps.infoType === 0) {
            //用户
            const userInfo = await getAllUserList();
            const user = userInfo.find((item) => item.name === val);
            value = user?.id || val;
          }
          if (componentProps.infoType === 1) {
            //部门
            const dept = await getDepartmentTree();
            getDepartmentVal(dept, val);
            value = deptId.value;
          }
        } else if (componentType === 'User') {
          //人员
          const userInfo = await getAllUserList();
          const userNameArr = val.split(',');
          const users = userInfo
            .filter((item) => userNameArr.includes(item.name))
            .map((item) => item.id);
          value = users.toString();
        } else if (componentType === 'Dept') {
          //组织架构
          const dept = await getDepartmentTree();
          getDepartmentVal(dept, val);
          value = deptId.value;
        } else if (componentType === 'MoneyChineseInput') {
          //货币大写
          const valArr = val.split('.');
          if (valArr.length > 1) {
            const decimals = valArr[1].substr(0, componentProps.decimals);
            value = `${valArr[0]}.${decimals}`;
          } else {
            value = val;
          }
        } else if (componentType === 'ApiCascader') {
          //级联
          const options = await apiConfigFunc(
            componentProps.apiConfig,
            isCamelCase,
            formModel,
            index,
          );
          const labelArr = val?.split('/');
          if (labelArr?.length) {
            const valueArr = getCascaderVal(options, labelArr);
            value =
              componentProps.showFormat === 'all'
                ? valueArr.toString()
                : valueArr[valueArr.length - 1];
          }
        } else if (componentType === 'Area') {
          //行政区域
          const options = await getAreaList({ id: '0' });
          const labelArr = val?.split('/');
          if (labelArr?.length) {
            const valueArr = await getAreaVal(options, labelArr);
            value = valueArr.toString();
          }
        } else if (componentType === 'TreeSelectComponent') {
          //树选择
          if (componentProps.treeConfig.id) {
            let formState = await getInfo(componentProps.treeConfig?.id);
            let config = JSON.parse(formState.config);
            let options = config.staticData;
            if (formState.type == TreeStructureType.API) {
              const res = await apiConfigFunc(
                config.apiData.apiConfig,
                isCamelCase,
                formModel,
                index,
              );
              options = res?.data || [];
            }
            const valueArr: string[] = [];
            if (val) {
              const arr = val.split(',');
              arr.forEach((item) => {
                valueArr.push(getTreeSelectVal(options, item).reverse().join('---') as string);
              });
              value = valueArr.toString();
            }
          }
        }
        if (componentType?.includes('RangePicker')) {
          //时间范围、日期范围
          const rangeArr = col.dataIndex!.split(',');
          returnItem[rangeArr[0]] = item[col.title + '开始时间'];
          returnItem[rangeArr[1]] = item[col.title + '结束时间'];
        } else {
          returnItem[col.dataIndex!] = value;
        }
      }
      tableInfo.push(returnItem);
    }
    data.value.push(...tableInfo);
    emit('change', unref(data));
    emit('update:value', unref(data));
  };

  const getDepartmentVal = (dept, label) => {
    for (let item of dept) {
      if (item.name === label) {
        deptId.value = item.id;
        return false;
      } else if (item.children?.length) {
        getDepartmentVal(item.children, label);
      }
    }
  };

  const getCascaderLabel = (options, valArr, level = 0, labelArr: string[] = []) => {
    const info = options?.find((option) => option.value === valArr[level]);
    labelArr.push(info?.label);
    if (info.children && info.children.length) {
      getCascaderLabel(info.children, valArr, level + 1, labelArr);
    }
    return labelArr;
  };

  const getCascaderVal = (options, labelArr, level = 0, valueArr: string[] = []) => {
    const info = options?.find((option) => option.label === labelArr[level]);
    valueArr.push(info?.value);
    if (info?.children && info.children.length) {
      getCascaderVal(info.children, labelArr, level + 1, valueArr);
    }
    return valueArr;
  };

  const getTreeSelectLabel = (options, valArr, labelArr, level = 0) => {
    const info = options?.find((option) => option.label === valArr[level]);
    if (level === valArr.length - 1) {
      labelArr.push(info?.label);
    } else if (level < valArr.length - 1 && info.children && info.children.length) {
      getTreeSelectLabel(info.children, valArr, labelArr, level + 1);
    }
    return labelArr;
  };

  const getTreeSelectVal = (options, label) => {
    for (const item of options) {
      if (item.label === label) return [item.label];
      if (item.children) {
        let value = getTreeSelectVal(item.children, label);
        if (value !== undefined) return value.concat(item.label);
      }
    }
  };

  const getAreaLabel = async (options, valArr, level = 0, labelArr: string[] = []) => {
    for (const item of options) {
      if (item.id === valArr[level]) {
        labelArr.push(item?.name);
        if (level < valArr.length - 1) {
          const info = await getAreaList({ id: valArr[level] });
          await getAreaLabel(info, valArr, level + 1, labelArr);
        }
        break;
      }
    }
    return labelArr;
  };

  const getAreaVal = async (options, labelArr, level = 0, valueArr: string[] = []) => {
    for (const item of options) {
      if (item.name === labelArr[level]) {
        valueArr.push(item?.id);
        if (level < labelArr.length - 1) {
          const info = await getAreaList({ id: item.id });
          await getAreaVal(info, labelArr, level + 1, valueArr);
        }
        break;
      }
    }
    return valueArr;
  };
</script>
<style lang="less" scoped>
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  :deep(.ant-table-cell) {
    padding: 8px 0;
  }

  :deep(.ant-table .ant-table-thead tr th) {
    padding: 11px;
  }

  :deep(.ant-input-number),
  :deep(.ant-input-affix-wrapper),
  :deep(.ant-input),
  :deep(.ant-select-selector),
  :deep(.ant-picker) {
    border: 0 !important;
    box-shadow: none !important;
  }

  :deep(.anticon) {
    padding-bottom: 0 !important;
  }

  :deep(.ant-table-cell-fix-right) {
    text-align: center;
    width: 60px;
  }

  :deep(.ant-radio-group),
  :deep(.ant-checkbox-group),
  :deep(.ant-rate),
  :deep(.ant-upload),
  :deep(.ant-form-item-explain-error) {
    padding: 0 11px;
  }

  :deep(.ant-switch),
  :deep(input[type='color']) {
    margin: 0 11px;
  }

  .select-btn {
    margin-bottom: 8px;

    :deep(.ant-upload) {
      padding: 0;
    }
  }
</style>
