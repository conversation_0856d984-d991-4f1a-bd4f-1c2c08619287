<template>
  <div>
    <FormItemRest>
      <MapModal v-model:value="address" @success="handleSuccess">
        <a-input
          readonly
          :disabled="disabled"
          :placeholder="placeholder"
          v-model:value="address"
          :size="size"
          :bordered="bordered"
          @blur="emit('blur')"
        >
          <template #prefix v-if="prefix">
            <Icon :icon="prefix" />
          </template>
          <template #suffix v-if="suffix">
            <Icon :icon="suffix" />
          </template>
        </a-input>
      </MapModal>
    </FormItemRest>
  </div>
</template>
<script lang="ts" setup>
  import { Form } from 'ant-design-vue';
  import { MapModal } from '/@/components/Map/index';
  import { watch, ref, inject } from 'vue';
  import { Icon } from '/@/components/Icon';

  import { camelCaseString } from '/@/utils/event/design';
  // 用于包裹弹窗的form组件 因为一个FormItem 只能收集一个表单组件  所以弹窗的form 必须排除
  const FormItemRest = Form.ItemRest;

  const props = defineProps({
    value: String,
    prefix: String,
    suffix: String,
    placeholder: String,
    readonly: Boolean,
    disabled: Boolean,
    size: String,
    bordered: {
      type: Boolean,
      default: true,
    },
    address: String,
    latiAndLong: String,
    province: String,
    city: String,
    district: String,
    adcode: String,
    index: Number,
    mainKey: String,
  });

  const address = ref<string>();

  const emit = defineEmits(['update:value', 'change', 'blur']);

  const formModel = inject<any>('formModel', null); // 注入表单数据
  const isCamelCase = inject<boolean>('isCamelCase', false);

  watch(
    () => props.value,
    () => {
      address.value = props.value;
      if (!props.value) {
        //预览页面 重置
        address.value = '';
      }
    },
    {
      immediate: true,
    },
  );
watch(
  () => props.latiAndLong,
  (val) => { },
  {
    immediate: true,
  },
);

  function handleSuccess(v) {
    console.log('MAP handleSuccess ', v);
    changeFieldData(v);
    emit('update:value', v.address);
    emit('change', v.address,v);
  }
  function changeFieldData(v) {
    if (!formModel) return;
    if (props.address) {
      const addressField: any = !isCamelCase ? props.address : camelCaseString(props.address);
      if (props.mainKey && props.index !== undefined && props.index !== null) {
        formModel[props.mainKey][props.index][addressField] = v.address;
      } else {
        formModel[addressField] = v.address;
      }
    }
    if (props.latiAndLong) {
      const latiAndLong: any = !isCamelCase
        ? props.latiAndLong
        : camelCaseString(props.latiAndLong);
      if (props.mainKey && props.index != undefined && props.index !== null) {
        formModel[props.mainKey][props.index][latiAndLong] = v.lnglat.join(',');
      } else {
        formModel[latiAndLong] = v.lnglat.join(',');
      }
    }
    //省份
    if (props.province) {
      const provinceField: any = !isCamelCase ? props.province : camelCaseString(props.province);

      if (props.mainKey && props.index !== undefined && props.index !== null) {
        formModel[props.mainKey][props.index][provinceField] = v.regeo.province;
      } else {
        formModel[provinceField] = v.regeo.province;
      }
    }
    // 城市
    if (props.city) {
      const cityField: any = !isCamelCase ? props.city : camelCaseString(props.city);
      if (props.mainKey && props.index !== undefined && props.index !== null) {
        formModel[props.mainKey][props.index][cityField] = v.regeo.city;
      } else {
        formModel[cityField] = v.regeo.city;
      }
    }
    // 地区
    if (props.district) {
      const districtField: any = !isCamelCase ? props.district : camelCaseString(props.district);
      if (props.mainKey && props.index !== undefined && props.index !== null) {
        formModel[props.mainKey][props.index][districtField] = v.regeo.district;
      } else {
        formModel[districtField] = v.regeo.district;
      }
    }
    // 地区编码
    if (props.adcode) {
      const adcodeField: any = !isCamelCase ? props.adcode : camelCaseString(props.adcode);
      if (props.mainKey && props.index !== undefined && props.index !== null) {
        formModel[props.mainKey][props.index][adcodeField] = v.regeo.adcode;
      } else {
        formModel[adcodeField] = v.regeo.adcode;
      }
    }
  }
</script>
