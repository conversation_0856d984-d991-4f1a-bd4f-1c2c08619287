<template>
  <div>
    <div v-if="listType === 'dragger'">
      <a-upload-dragger
        :file-list="fileList"
        :maxCount="maxNumber"
        :accept="accept"
        :name="name"
        :disabled="disabled"
        :multiple="multiple"
        :beforeUpload="beforeUpload"
        listType="picture"
        :show-upload-list="{ showDownloadIcon, showPreviewIcon, showRemoveIcon }"
        @remove="handleRemove"
        @download="handleDownload"
        @preview="handlePreview"
        @drop="handleClick"
        @click="handleClick"
        class="list-upload dragger-upload"
        :style="style"
      >
        <div class="dragger-text">
          <Icon icon="ep:upload-filled" color="#5e95ff" :size="24" />
          <div class="mt-2 text-xs">点击或将文件拖拽到这里上传</div>
        </div>
        <div class="dragger-tip">{{ tip }}</div>
      </a-upload-dragger>
    </div>
    <div v-else-if="listType === 'picture'">
      <a-upload
        :file-list="fileList"
        :maxCount="maxNumber"
        :accept="accept"
        :name="name"
        :disabled="disabled"
        :multiple="multiple"
        :beforeUpload="beforeUpload"
        :listType="listType"
        :show-upload-list="{ showDownloadIcon, showPreviewIcon, showRemoveIcon }"
        @remove="handleRemove"
        @download="handleDownload"
        @preview="handlePreview"
        @click="handleClick"
        class="list-upload"
        :style="style"
      >
        <plus-outlined />
      </a-upload>
    </div>
    <a-upload
      :file-list="fileList"
      :maxCount="maxNumber"
      :accept="accept"
      :name="name"
      :disabled="disabled"
      :multiple="multiple"
      :beforeUpload="beforeUpload"
      :listType="listType"
      :show-upload-list="{ showDownloadIcon, showPreviewIcon, showRemoveIcon }"
      @remove="handleRemove"
      @download="handleDownload"
      @preview="handlePreview"
      @click="handleClick"
      v-else
    >
      <plus-outlined v-if="listType == 'picture-card'" />
      <div :style="style" v-else>
        <a-button :loading="loading" :disabled="loading">
          <upload-outlined />
          点击上传
        </a-button>
      </div>
    </a-upload>
    <BasicModal @register="registerModal" :title="previewTitle">
      <iframe v-if="previewFile" :src="previewFile" class="iframe-box"></iframe>
    </BasicModal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Upload } from 'ant-design-vue';
  import { UploadOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { deleteSingleFile, getFileList } from '/@/api/system/file';
  import { downloadByUrl } from '/@/utils/file/download';
  import { uploadMultiApi } from '/@/api/sys/upload';
  import Icon from '/@/components/Icon/index';
  import { Base64 } from 'js-base64';
  import { getAppEnvConfig } from '/@/utils/env';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { onMounted } from 'vue';

  const props = defineProps({
    value: String,
    maxNumber: Number,
    accept: String,
    name: String,
    disabled: Boolean,
    multiple: Boolean,
    maxSize: Number,
    api: Function,
    style: Object,
    listType: {
      type: String,
      default: 'text',
    },
    tip: String,
    showPreviewIcon: {
      type: Boolean,
      default: true,
    },
    showRemoveIcon: {
      type: Boolean,
      default: true,
    },
    showDownloadIcon: {
      type: Boolean,
      default: true,
    },
  });

  const fileList = ref<any[]>([]);
  const list = ref<any[]>([]);
  const { notification } = useMessage();
  const folderId = ref<string>('');
  const deleteFlag = ref(false);
  const emit = defineEmits(['update:value', 'change', 'click']);
  const loading = ref(false);

  const previewVisible = ref(false);
  const previewFile = ref('');
  const previewTitle = ref('');

  watch(
    () => props.value,
    async (val) => {
      if (val) {
        fileList.value = await getFileList({ folderId: props.value });
        if (fileList.value.length) {
          fileList.value.forEach((x) => {
            x.name = x.fileName;
            x.url = x.fileUrl;
            x.thumbUrl = x.thUrl;
            x.status = 'done'; //没有则不会展示下载按钮
          });
          folderId.value = fileList.value[0].folderId;
        }
      }
      if (!val) {
        fileList.value = [];
      }
    },
    {
      immediate: true,
    },
  );
  watch(
    () => list.value,
    async (val) => {
      if (deleteFlag.value) return;
      if (val.length) {
        let arr: any[] = val.filter((o) => {
          return !o.status;
        });
        if (arr.length <= 0) return;
        try {
          let res = await uploadMultiApi(
            {
              name: 'file',
              file: arr,
            },
            folderId.value,
          );
          folderId.value = res[0].folderId;
          fileList.value.forEach((x) => {
            x.status = 'done'; //没有则不会展示下载按钮
            x.url = x.fileUrl;
            x.thumbUrl = x.thUrl;
          });

          console.log(folderId.value, '12345');
          emit('update:value', folderId.value);
          emit('change', folderId.value);
          loading.value = false;
        } catch (error) {
          console.error(error);
          loading.value = false;
        }
      }
    },
  );

  const [registerModal, { openModal, setModalProps }] = useModal();

  onMounted(() => {
    setModalProps({
      destroyOnClose: true,
      footer: null,
      fixedHeight: true,
      width: 800,
    });
  });

  const beforeUpload = (file) => {
    if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
      notification.error({
        message: 'Tip',
        description: `文件大小不能超过${props.maxSize}MB！`,
      });
      return false || Upload.LIST_IGNORE;
    }
    if (props.maxNumber && fileList.value.length + list.value.length === props.maxNumber!) {
      notification.error({
        message: 'Tip',
        description: `文件数量不能超过${props.maxNumber}个！`,
      });
      return false;
    }

    list.value = [...list.value, file];
    deleteFlag.value = false;
    loading.value = true;
    return Upload.LIST_IGNORE;
  };
  function handleClick() {
    list.value = [];
    emit('click');
  }
  const handleRemove = async (info) => {
    const id = info.response ? info.response.data.id : info.id;

    const newFolderId = await deleteSingleFile(id);

    folderId.value = newFolderId;

    deleteFlag.value = true;

    const index = fileList.value.findIndex((x) => x.id === id);
    fileList.value.splice(index, 1);

    fileList.value.forEach((x) => {
      x.folderId = newFolderId;
    });

    emit('update:value', folderId.value);
    emit('change');
    notification.success({
      message: 'Tip',
      description: '删除成功！',
    });
  };

  const handleDownload = (info) => {
    const url = info.response ? info.response.data.fileUrl : info.fileUrl;
    const fileName = info.response ? info.response.data.fileName : info.fileName;
    const fileType = info.response ? info.response.data.fileType : info.fileType;

    downloadByUrl({ url, fileName: fileName + fileType });
  };

  const handlePreview = async (file) => {
    const fileUrl = file.response?.data?.fileUrl || file.fileUrl;
    previewFile.value =
      getAppEnvConfig().VITE_GLOB_UPLOAD_PREVIEW +
      encodeURIComponent(
        Base64.encode(
          fileUrl.includes('http://') || fileUrl.includes('https://')
            ? fileUrl
            : getAppEnvConfig().VITE_GLOB_API_URL + fileUrl,
        ),
      );

    previewVisible.value = true;
    previewTitle.value = file.name || file.fileName;
    openModal();
    console.log(
      fileUrl.includes('http://') || fileUrl.includes('https://')
        ? fileUrl
        : getAppEnvConfig().VITE_GLOB_API_URL + fileUrl,
    );
  };
</script>
<style lang="less" scoped>
  .list-upload {
    :deep(.ant-upload) {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 128px;
      height: 128px;
      background: #fafafa;
      border: 1px dashed #d9d9d9;
    }

    :deep(.anticon-plus) {
      font-size: 20px;
      color: #999;
    }

    .dragger-tip {
      background: rgb(0 0 0 / 60%);
      color: #fff;
      height: 100%;
      border-radius: 2px;
      display: none;
      padding: 8px 5px;
      width: 100%;
    }

    .dragger-text {
      padding: 16px 0;
    }

    &:hover .dragger-tip {
      display: block;
    }

    &:hover .dragger-text {
      display: none;
    }
  }

  :deep(.ant-upload) {
    padding: 0 !important;
  }

  .iframe-box {
    width: 100%;
    height: 100%;
  }
</style>
