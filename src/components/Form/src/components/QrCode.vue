<template>
  <div class="code-image">
    <QrCode :value="imgSrc" v-if="imgSrc" :width="200" />
    <div class="placebox" v-else>
      <div v-if="codeType == 'fixed'">
        <Icon icon="el:qrcode" class="my-6" size="24" color="rgba(0, 0, 0, 0.43)" />
        <div class="tips">{{ t('请先进行配置') }}</div>
      </div>
      <div class="flex items-center justify-center p-4" v-if="codeType == 'api'">
        <div class="tips">{{ t('接口配置的二维码会在页面加载时显示') }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { inject, onMounted, ref, watch } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { QrCode } from '/@/components/Qrcode/index';
  import { apiConfigFunc } from '/@/utils/event/design';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    defaultValue: String,
    disabled: Boolean,
    codeType: String,
    apiConfig: {
      type: Object,
    },
  });
  const imgSrc = ref();

  watch(
    () => props.defaultValue,
    (val) => {
      imgSrc.value = val;
    },
  );

  const formModel = inject<any>('formModel', null);
  const isCamelCase = inject<boolean>('isCamelCase', false);
  onMounted(async () => {
    if (props.codeType == 'api') {
      imgSrc.value = await apiConfigFunc(props.apiConfig, isCamelCase, formModel);
    } else if (props.codeType == 'fixed') {
      imgSrc.value = props.defaultValue;
    }
  });
</script>
<style scoped lang="less">
  .placebox {
    width: 120px;
    height: 120px;
    background: rgb(0 0 0 / 2%);
    border: 2px dashed #d9d9d9;
    text-align: center;

    .tips {
      color: rgb(0 0 0 / 43%);
      flex: 1;
    }
  }
</style>
