<!--
 * @Author: Vben
 * @Description: logo component
-->
<template>
  <div class="anticon logo_box" :class="getAppLogoClass" @click="goHome">
    <!-- <a href="http://www.zilueit.com/" target="_blank"> -->
    <!-- <img :src="logoConfig.menuLogoUrl || logo" width="24" style="height: 30px" /> -->
    <!-- <div class="truncate md:opacity-100" :class="getTitleClass" v-show="showTitle">
        ・{{ logoConfig.shortName || title }}
      </div> -->
    <!-- </a> -->
    <img v-if="showTitle" :src="logo_dark" class="logo_img1" />
    <img v-if="!showTitle" :src="logo" />
  </div>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useGo } from '/@/hooks/web/usePage';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useUserStore } from '/@/store/modules/user';
  import { useAppStore } from '/@/store/modules/app';
  import logo_dark from '/@/assets/images/logo-dark.png';
  import logo from '/@/assets/images/logo.png';
  const props = defineProps({
    /**
     * The theme of the current parent component
     */
    theme: { type: String, validator: (v: string) => ['light', 'dark'].includes(v) },
    /**
     * Whether to show title
     */
    showTitle: { type: Boolean, default: true },
    /**
     * The title is also displayed when the menu is collapsed
     */
    alwaysShowTitle: { type: Boolean },
  });

  const { prefixCls } = useDesign('app-logo');
  const { getCollapsedShowTitle } = useMenuSetting();
  const userStore = useUserStore();
  const { title } = useGlobSetting();
  const go = useGo();
  const appStore = useAppStore();
  const logoConfig = appStore.getLogoConfig;
  const getAppLogoClass = computed(() => [
    prefixCls,
    props.theme,
    { 'collapsed-show-title': unref(getCollapsedShowTitle) },
  ]);

  const getTitleClass = computed(() => [
    `${prefixCls}__title`,
    {
      'xs:opacity-0': !props.alwaysShowTitle,
    },
  ]);

  function goHome() {
    go(userStore.getUserInfo.homePath || PageEnum.BASE_HOME);
  }
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-app-logo';

  .@{prefix-cls} {
    display: flex;
    align-items: center;
    padding-left: 7px;
    cursor: pointer;
    transition: all 0.2s ease;

    &.light {
      border-bottom: 1px solid @border-color-base;
    }

    &.collapsed-show-title {
      padding-left: 20px;
    }

    &.light &__title {
      color: @primary-color;
    }

    &.dark &__title {
      color: #0096df;
    }

    &__title {
      font-size: 20px;
      font-weight: 400;
      transition: all 0.5s;
      line-height: normal;
    }
  }

  a {
    display: flex;
    align-items: center;
  }
  .logo_box {
    display: flex;
    align-items: center;
    justify-content: center;
    .logo_img1 {
      width: 80%;
      height: auto;
    }
  }
</style>
