<template>
  <div class="wrap">
    <div class="empty-box">
      <IconFontSymbol v-if="hasIcon" icon="tishi" class="empty-icon" />
      <div class="msg">{{ msg }}</div>
    </div>
  </div>
</template>
<script lang="ts">
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const defaultMsg = t('暂无数据');

  export default {};
</script>
<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  defineProps({
    msg: {
      type: String,
      default: defaultMsg,
    },
    hasIcon: {
      type: Boolean,
      default: true,
    },
  });
</script>

<style scoped>
  .wrap {
    width: 100%;
    height: 100%;
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .empty-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .empty-icon {
    font-size: 36px;
  }

  .msg {
    font-size: 14px;
    color: #9e9d9d;
    padding: 10px 40px;
  }
</style>
