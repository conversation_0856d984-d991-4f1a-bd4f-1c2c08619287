<template>
  <div class="search-box">
    <a-input v-model:value="keyword" :placeholder="t('请填写搜索内容')" style="width: 300px" />
    <a-button type="primary" @click="search" class="serach-btn">{{ t('搜索') }}</a-button>
    <a-button @click="cancel" class="serach-btn">{{ t('重置') }}</a-button>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emits = defineEmits(['search']);
  let keyword = ref('');
  function search() {
    emits('search', keyword.value);
  }
  function cancel() {
    keyword.value = '';
    emits('search', keyword.value);
  }
</script>

<style scoped>
  .search-box {
    padding: 10px 20px;
  }

  .serach-btn {
    margin-left: 10px;
  }
</style>
