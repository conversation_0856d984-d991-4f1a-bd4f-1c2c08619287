<template>
  <div class="layout" :class="layoutClass || ''">
    <div class="left-width overflow-hidden" v-if="hasLeftSlot">
      <slot name="left"></slot>
      <!-- 左侧树 -->
    </div>
    <div
      class="p-3 bg-color"
      :class="[hasLeftSlot ? 'right-width' : 'full-width']"
      v-if="hasRightSlot"
    >
      <slot name="search"></slot>
      <!-- 搜索栏 -->
      <div v-if="layoutOne">
        <div v-if="props.title" class=" ">
          <div v-if="props.title" class="l-panel--title">{{ props.title }}</div>
          <div class="flex justify-between">
            <SearchBox
              class="w-full"
              v-if="hasSearchSlot"
              :searchConfig="props.searchConfig"
              @search="search"
              @scroll-height="$emit('scrollHeight')"
            />
            <slot name="operation"></slot>
          </div>
          <!-- 搜索下操作按钮 -->
        </div>
      </div>
      <div v-else>
        <div v-if="hasOperationSlot || props.title || hasSearchSlot" class="node-box">
          <NodeHead v-if="props.title" class="mb-3 mt-3" :nodeName="props.title" />
          <div class="flex">
            <SearchBox
              v-if="hasSearchSlot"
              :searchConfig="props.searchConfig"
              @search="search"
              @scroll-height="$emit('scrollHeight')"
            />
            <slot name="operation"></slot>
          </div>
          <!-- 搜索下操作按钮 -->
        </div>
      </div>
      <slot name="right"></slot>
      <!-- 右边 table列表 -->
    </div>
    <div v-if="hasFullSlot" :class="hasLeftSlot ? 'right-width' : 'full-width'">
      <slot name="full"></slot>
      <!-- 列表<无搜索、按钮> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useSlots } from 'vue';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import SearchBox from './WorkflowSearchBox.vue';
  const props = defineProps(['title', 'searchConfig', 'layoutClass', 'layoutOne']);
  const emits = defineEmits(['search', 'scrollHeight']);
  const hasLeftSlot = computed(() => {
    return !!useSlots().left;
  });
  const hasSearchSlot = computed(() => {
    return !!useSlots().search;
  });
  const hasOperationSlot = computed(() => {
    return !!useSlots().operation;
  });
  const hasRightSlot = computed(() => {
    return !!useSlots().right;
  });
  const hasFullSlot = computed(() => {
    return !!useSlots().full;
  });
  function search(params: any) {
    emits('search', params);
  }
</script>
<style lang="less" scoped>
  .layout {
    display: flex;
    height: 100%;
    padding: 10px;
  }

  .left-width {
    flex-basis: 20%;
    border-right: 1px solid #e5e7eb;
  }

  .right-width {
    flex-basis: 80%;
  }

  .full-width {
    flex-basis: 100%;
  }

  .bg-color {
    background-color: #fff;
  }

  .node-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :deep(.button-box) {
    button {
      margin-right: 6px;
    }
  }
  // 操作列

  :deep(.delete-icon) {
    cursor: pointer;
    color: @clear-color;
  }

  :deep(.edit-icon) {
    cursor: pointer;
    color: @primary-color;
    margin-right: 10px;
  }

  // 左侧树结构样式
  :deep(.vben-tree-header) {
    height: 60px;
    border-bottom: 1px solid #f1f1f1;
  }

  :deep(.ant-tree) {
    padding: 0;
    margin-top: 10px;
    border-top: 0;
    color: #666;
  }

  :deep(.ant-tree-switcher) {
    width: 0;
  }

  :deep(.ant-tree-title .anticon) {
    padding-left: 10px;
  }

  :deep(.ant-tree .ant-tree-treenode) {
    height: 40px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .l-panel--title {
    align-items: center;
    color: #262626;
    font-size: 16px;
    padding-left: 8px;
    padding-right: 8px;
    border-bottom: 1px solid #d9d9d9;
    height: 24px;
    line-height: 24px;
    padding-bottom: 32px;
    margin-bottom: 8px;
  }
</style>
<style>
  html[data-theme='dark'] {
    .bg-color {
      background-color: #151515 !important;
    }
  }
</style>
