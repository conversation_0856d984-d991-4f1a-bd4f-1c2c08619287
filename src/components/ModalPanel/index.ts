import { withInstall } from '/@/utils';
import resizeFixedLayout from './src/ResizeFixedLayout.vue';
import ModalPanelVue from './src/ModalPanel.vue';
import SearchBoxVue from './src/SearchBox.vue';
import EmptyBoxVue from './src/EmptyBox.vue';
import NodeHeadVue from './src/NodeHead.vue';
import designLogo from './src/DesignLogo.vue';
import loadingBox from './src/LoadingBox.vue';

import fewerLeft from './src/FewerLeft.vue';
import fewerRight from './src/FewerRight.vue';
import zoomInOrOut from './src/ZoomInOrOut.vue';

import pageLayout from './src/PageLayout.vue';

import desktopDesignEmpty from './src/DesktopDesignEmpty.vue';

export const ResizeFixedLayout = withInstall(resizeFixedLayout);
export const DesignLogo = withInstall(designLogo); // 设计页面公司LOGO图标
export const DesktopDesignEmpty = withInstall(desktopDesignEmpty); // 桌面设计空数据显示图片
export const ModalPanel = withInstall(ModalPanelVue);
export const SearchBox = withInstall(SearchBoxVue);
export const EmptyBox = withInstall(EmptyBoxVue);
export const NodeHead = withInstall(NodeHeadVue); // 标题
export const LoadingBox = withInstall(loadingBox); // Loading

export const FewerLeft = withInstall(fewerLeft); //收取左边图标
export const FewerRight = withInstall(fewerRight); //收取右边图标
export const ZoomInOrOut = withInstall(zoomInOrOut); //放大缩小

export const PageLayout = withInstall(pageLayout); // 布局
