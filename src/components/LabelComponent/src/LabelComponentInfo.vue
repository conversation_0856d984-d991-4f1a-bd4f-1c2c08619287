<template>
  <div>
    <a-popover title="">
      <template #content>
        <a-tag v-for="(item, index) in state.tags" :key="index" :style="getClass(index)">{{
          item
        }}</a-tag>
      </template>
      <a-tag v-for="(item, index) in state.tags" :key="index" :style="getClass(index)">{{
        item
      }}</a-tag>
    </a-popover>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    styleConfig: {
      type: Object,
      default: () => {
        return {
          colors: [], //颜色配置
          isLoop: true, //是否循环选择样式
        };
      },
    },
  });

  const state = reactive({
    tags: [] as Array<string>,
    value: '',
  });
  onMounted(async () => {
    state.tags = props.value.split(',');
  });

  function getClass(index) {
    const defaultBgColor = '#fafafa';
    const defaultFontColor = '#000000';
    const defaultBorderWidth = 1;
    const defaultBorderColor = '#d9d9d9';
    if (props.styleConfig.colors && Array.isArray(props.styleConfig.colors)) {
      if (props.styleConfig.colors[index]) {
        let styleConfig = {
          bgColor: props.styleConfig.colors[index].bgColor
            ? props.styleConfig.colors[index].bgColor
            : defaultBgColor,
          fontColor: props.styleConfig.colors[index].fontColor
            ? props.styleConfig.colors[index].fontColor
            : defaultFontColor,
          borderWidth: props.styleConfig.colors[index].borderWidth
            ? props.styleConfig.colors[index].borderWidth
            : defaultBorderWidth,
          borderColor: props.styleConfig.colors[index].borderColor
            ? props.styleConfig.colors[index].borderColor
            : defaultBorderColor,
        };
        return `
        background-color: ${styleConfig.bgColor};
        border-width: ${styleConfig.borderWidth}px;
        border-color: ${styleConfig.borderColor};
        color: ${styleConfig.fontColor};
        margin: 2px;
        `;
      } else {
        if (props.styleConfig.isLoop) {
          let loopIndex = index % props.styleConfig.colors.length;
          if (props.styleConfig.colors[loopIndex]) {
            let styleConfig = {
              bgColor: props.styleConfig.colors[loopIndex].bgColor
                ? props.styleConfig.colors[loopIndex].bgColor
                : defaultBgColor,
              fontColor: props.styleConfig.colors[loopIndex].fontColor
                ? props.styleConfig.colors[loopIndex].fontColor
                : defaultFontColor,
              borderWidth: props.styleConfig.colors[loopIndex].borderWidth
                ? props.styleConfig.colors[loopIndex].borderWidth
                : defaultBorderWidth,
              borderColor: props.styleConfig.colors[loopIndex].borderColor
                ? props.styleConfig.colors[loopIndex].borderColor
                : defaultBorderColor,
            };
            return `
              background-color: ${styleConfig.bgColor};
              border-width: ${styleConfig.borderWidth}px;
              border-color: ${styleConfig.borderColor};
              color: ${styleConfig.fontColor};
              margin: 2px;
              `;
          } else {
            return `
            background-color: ${defaultBgColor};
            border-width: ${defaultBorderWidth}px;
            border-color: ${defaultBorderColor};
            color: ${defaultFontColor};
            margin: 2px;
        `;
          }
        } else {
          return `
            background-color: ${defaultBgColor};
            border-width: ${defaultBorderWidth}px;
            border-color: ${defaultBorderColor};
            color: ${defaultFontColor};
            margin: 2px;
        `;
        }
      }
    } else {
      return `
            background-color: ${defaultBgColor};
            border-width: ${defaultBorderWidth}px;
            border-color: ${defaultBorderColor};
            color: ${defaultFontColor};
            margin: 2px;
        `;
    }
  }
</script>
<style lang="less" scoped></style>
