<template>
  <div style="height: 100%">
    <div class="event-box">
      <div>
        <EventArea
          :columnList="generatorConfig!.formEventConfig[0]"
          :index="0"
          @delete-event="deleteEvent"
          @click-node="clickNode"
        />
        <AddEvent :lineHeight="`${lineHeight[0]}px`" @click="addEvent(0)" />
      </div>
      <div class="box-second">
        <EventArea
          :columnList="generatorConfig!.formEventConfig[1]"
          :index="1"
          @delete-event="deleteEvent"
          @click-node="clickNode"
        />
        <AddEvent :lineHeight="`${lineHeight[1]}px`" @click="addEvent(1)" />
      </div>
      <div class="box-third">
        <EventArea
          :columnList="generatorConfig!.formEventConfig[2]"
          :index="2"
          @delete-event="deleteEvent"
          @click-node="clickNode"
        />
        <AddEvent :lineHeight="`${lineHeight[2]}px`" @click="addEvent(2)" />
      </div>
      <div class="box-fourth">
        <EventArea
          :columnList="generatorConfig!.formEventConfig[3]"
          :index="3"
          @delete-event="deleteEvent"
          @click-node="clickNode"
        />
        <AddEvent :isLast="true" @click="addEvent(3)" />
        <EventArea
          :columnList="generatorConfig!.formEventConfig[4]"
          :index="4"
          @click-node="clickNode"
        />
      </div>
    </div>
    <NodeEvent v-model:columnList="generatorConfig!.formEventConfig" :selectedNode="selectedNode" />
  </div>
</template>

<script lang="ts" setup>
  import { ref, Ref, reactive, inject, watch } from 'vue';
  import EventArea from './components/EventArea.vue';
  import NodeEvent from './components/NodeEvent.vue';
  import AddEvent from './components/AddEvent.vue';
  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import {
    FormEventColumnConfig,
    FormEventStyleConfig,
    selectedNodeConfig,
  } from '/@/model/generator/formEventConfig';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { changeCompsApiConfig, changeEventApiConfig, getMainTable } from '/@/utils/event/design';
  import { cloneDeep } from 'lodash-es';

  const { t } = useI18n();
  const { notification } = useMessage();
  const current = inject<Ref<number>>('current');
  const isCustomForm = inject<boolean>('isCustomForm', false);
  const designType = inject<string>('designType');
  const widgetForm = inject<any>('widgetForm');
  const fromMobile = inject<Boolean>('fromMobile', false);
  const generatorConfig = inject<GeneratorConfig>('generatorConfig');
  let lineHeight = reactive<number[]>([]);
  let column = reactive<FormEventColumnConfig>({
    0: [
      {
        type: 'circle',
        color: '#2774ff',
        text: t('开始节点'),
        icon: '#icon-kaishi',
        bgcColor: '#D8E5FF',
        isUserDefined: false,
      },
      {
        color: '#F6AB01',
        icon: '#icon-chushihua',
        text: t('初始化表单'),
        bgcColor: '#f9f5ea',
        isUserDefined: false,
        nodeInfo: {
          processEvent: [],
        },
      },
    ],
    1: [
      {
        color: '#B36EDB',
        icon: '#icon-shujufenxi',
        text: t('获取表单数据'),
        detail: t('(新增无此操作)'),
        bgcColor: '#F8F2FC',
        isUserDefined: false,
        nodeInfo: {
          processEvent: [],
        },
      },
    ],
    2: [
      {
        color: '#F8625C',
        icon: '#icon-jiazai',
        text: t('加载表单'),
        bgcColor: '#FFF1F1',
        isUserDefined: false,
        nodeInfo: {
          processEvent: [],
        },
      },
    ],
    3: [
      {
        color: '#6C6AE0',
        icon: '#icon-jsontijiao',
        text: t('提交表单'),
        bgcColor: '#F5F4FF',
        isUserDefined: false,
        nodeInfo: {
          processEvent: [],
        },
      },
    ],
    4: [
      {
        type: 'circle',
        color: '#F8625C',
        text: t('结束节点'),
        icon: '#icon-jieshuzhiliao',
        bgcColor: '#FFD6D6',
        isLast: true,
        isUserDefined: false,
      },
    ],
  });

  watch(
    () => current?.value,
    (val) => {
      //自定义表单当前步骤为2 或 代码生成器 数据优先当前步骤为2 或 代码生成器 界面优先、简易模板 当前步骤为1
      if (
        (isCustomForm && val === 2) ||
        (!isCustomForm &&
          ((designType === 'data' && val === 2) || (designType !== 'data' && val === 1)))
      ) {
        if (
          ((isCustomForm && designType == 'code') || (!isCustomForm && designType !== 'data')) &&
          generatorConfig!.tableStructureConfigs
        ) {
          let tableFieldConfigs = getMainTable(generatorConfig!.tableStructureConfigs);
          changeCompsApiConfig(generatorConfig!.formJson.list, designType, tableFieldConfigs);
          changeEventApiConfig(generatorConfig!.formEventConfig, designType, tableFieldConfigs);
          widgetForm.value.list = cloneDeep(generatorConfig!.formJson.list);
        }

        if (
          generatorConfig?.formEventConfig &&
          Object.keys(generatorConfig!.formEventConfig)?.length
        ) {
          column = generatorConfig!.formEventConfig!;
          lineHeight[0] = 135 + (column[0].length - 2) * 158;
          lineHeight[1] = 50 + (column[1].length - 1) * 158;
          lineHeight[2] = 50 + (column[2].length - 1) * 158;
        } else {
          generatorConfig!.formEventConfig = column;
          lineHeight[0] = 135;
          lineHeight[1] = 50;
          lineHeight[2] = 50;
        }
      }
    },
  );

  const selectedNode = ref<selectedNodeConfig>();
  const addEvent = (index) => {
    if (fromMobile) return;
    const columnInfo: FormEventStyleConfig = {
      color: '#2774FF',
      icon: '#icon-yonghu-xianxing',
      text: t('用户自定义节点'),
      bgcColor: '#F5F8FA',
      isUserDefined: true,
      isLogRecord: false,
      nodeInfo: {
        processEvent: [],
      },
    };
    column[index].push(columnInfo);

    if (index === 3) return;
    lineHeight[index] += 158;
  };
  const deleteEvent = ({ index, columnIndex }) => {
    if (fromMobile) return;
    column[columnIndex].splice(index, 1);
    lineHeight[columnIndex] -= 158;
  };
  const clickNode = (selected) => {
    for (let key in column) {
      column[key].map((item) => {
        item.isClick = false;
      });
    }
    column[selected.columnIndex][selected.index]['isClick'] = true;
    selectedNode.value = selected;
  };

  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    const hasNodeName = Object.values(generatorConfig!.formEventConfig).every((config) => {
      return config.every((item) => {
        return !!item.text;
      });
    });
    if (!hasNodeName) {
      notification.error({
        message: t('提示'),
        description: t('节点名称不能为空！'),
      });
      return false;
    }

    return true;
  };
  defineExpose({ validateStep });
</script>

<style lang="less" scoped>
  .event-box {
    position: relative;
    padding: 10px 50px;
    font-size: 13px;
    width: v-bind("!!selectedNode ? 'calc(100% - 410px)' : '100%'");
    height: 100%;
    overflow: auto;

    .box-second {
      position: absolute;
      top: 60px;
      left: 310px;
    }

    .box-third {
      position: absolute;
      top: 120px;
      left: 570px;
    }

    .box-fourth {
      position: absolute;
      top: 185px;
      left: 830px;
    }
  }
</style>
