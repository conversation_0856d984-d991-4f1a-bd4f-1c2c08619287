<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      @register="registerDrawer"
      :isDetail="true"
      @close="handleCloser"
      :title="t('表单预览1')"
    >
      <div>
        <BasicForm
          @register="registerForm"
          autoFocusFirstItem
          :labelWidth="100"
          :schemas="schemas"
          :actionColOptions="{ span: 24 }"
          :submitButtonOptions="{ text: t('提交') }"
          @submit="handleSubmit"
          @reset="handleReset"
        />
      </div>
      <template #titleToolbar> {{ t('重置') }} </template>
    </BasicDrawer>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, FormProps, FormSchema, useForm } from '/@/components/Form/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { buildOption } from '/@/utils/helper/designHelper';
  // import { buildCode } from '/@/utils/helper/generatorHelper';

  const formProps = ref<FormProps>();

  const schemas: FormSchema[] = [];
  // [
  //   {
  //     field: 'radio_1527c74b77dd43ceb8daceb35e22d657',
  //     label: '单选框组',
  //     component: 'ApiRadioGroup',
  //     colProps: {
  //       span: 24,
  //     },
  //     rules: [
  //       {
  //         trigger: 'blur',
  //         enum: '',
  //         message: '',
  //         pattern: '',
  //         required: false,
  //         type: 'any',
  //       },
  //     ],
  //     required: false,
  //     defaultValue: '',
  //     componentProps: {
  //       api: getMenuList,
  //       params: {
  //         count: 2,
  //       },
  //       resultField: 'data',
  //       // use name as label
  //       labelField: 'name',
  //       // use id as value
  //       valueField: 'id',
  //       isBtn: false,
  //     },
  //   },
  // ];

  //使用表单钩子  注册表单  获取到 操作表单的方法
  const [registerForm, { setProps, resetFields, validate }] = useForm();

  //使用抽屉内部钩子   获取到 操作抽屉的方法
  const [registerDrawer] = useDrawerInner((option) => {
    formProps.value = buildOption(option);
    // const codes = buildCode(generatorConfig, tableInfo, formProps.value.schemas as FormSchema[]);
    // console.log(JSON.stringify(formProps.value), '@@@@@@@@@@', codes);
    setProps(formProps.value);
  });

  //提交方法
  const handleSubmit = async (formData: Recordable) => {
    //提交之后将按钮设置为loading 不可操作状态
    try {
      const values = await validate();
      console.log(values);
    } finally {
      console.log(formData);
    }
  };

  const handleReset = () => {};

  //关闭方法
  const handleCloser = () => {
    resetFields(); //重置表单
    // clearValidate(); //清空验证
  };

  const { t } = useI18n();
</script>
