<template>
  <div class="step1-form">
    <BasicForm @register="register" />
  </div>
</template>
<script lang="ts" setup>
  import { inject, Ref, watch } from 'vue';
  import { getSystemAllList } from '/@/api/system/subSystem';
  import { getMenuTree } from '/@/api/system/menu';
  import { FormSchema, useForm, BasicForm } from '/@/components/Form';
  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const generatorConfig = inject<GeneratorConfig>('generatorConfig') as GeneratorConfig;
  const current = inject<Ref<number>>('current') as Ref<number>;

  const formSchema: FormSchema[] = [
    {
      field: 'code',
      label: t('菜单编号'),
      required: true,
      title: t('菜单信息'),
      component: 'Input',
      componentProps: {
        placeholder: t('请输入菜单编号'),
      },
      colProps: { span: 12 },
    },
    {
      field: 'name',
      label: t('菜单名称'),
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: t('请输入菜单名称'),
      },
      colProps: { span: 12 },
    },
    {
      field: 'systemId',
      label: '主子系统',
      component: 'ApiSelect',
      required: true,
      componentProps: ({ formModel, formActionType }) => {
        return {
          api: getSystemAllList,
          labelField: 'name',
          valueField: 'id',
          getPopupContainer: () => document.body,
          showSearch: true,
          onChange: async (val) => {
            formModel.parentId = '';
            const { updateSchema } = formActionType;
            updateSchema({
              field: 'parentId',
              componentProps: {
                api: getMenuTree,
                params: { systemId: val },
                fieldNames: {
                  key: 'id',
                  label: 'title',
                  value: 'id',
                },
              },
            });
          },
        };
      },
      colProps: { span: 12 },
    },
    {
      field: 'parentId',
      label: t('上级菜单'),
      component: 'ApiTreeSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getMenuTree,
          params: { systemId: formModel.systemId },
          fieldNames: {
            key: 'id',
            label: 'title',
            value: 'id',
          },
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 12 },
    },
    {
      field: 'sortCode',
      label: t('排序'),
      required: true,
      component: 'InputNumber',
      componentProps: {
        placeholder: t('请输入排序号'),
        min: 0,
      },
      colProps: { span: 12 },
    },
    {
      field: 'icon',
      label: t('图标'),
      required: true,
      component: 'IconPicker',
      componentProps: {
        placeholder: t('请选择图标'),
      },
      colProps: { span: 12 },
    },
    {
      field: 'remark',
      label: t('备注'),
      component: 'InputTextArea',
      componentProps: {
        placeholder: t('请填写备注'),
      },
      colProps: { span: 24 },
    },
  ];
  watch(
    () => current.value,
    (val) => {
      if (val === 5) {
        setFieldsValue(generatorConfig.menuConfig!);
      }
    },
  );
  const [register, { validate, getFieldsValue, setFieldsValue }] = useForm({
    labelWidth: 100,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
  //验证当前步骤的数据
  const validateStep = async (): Promise<boolean> => {
    try {
      const formData = await validate();
      setData(formData);
    } catch (error) {
      return false;
    }
    return true;
  };
  function getFormData() {
    const formData = getFieldsValue();
    setData(formData);
  }
  function setData(formData) {
    generatorConfig.menuConfig!.code = formData.code;
    generatorConfig.menuConfig!.name = formData.name;
    generatorConfig.menuConfig!.parentId = formData.parentId;
    generatorConfig.menuConfig!.remark = formData.remark;
    generatorConfig.menuConfig!.sortCode = formData.sortCode;
    generatorConfig.menuConfig!.icon = formData.icon;
    generatorConfig.menuConfig!.systemId = formData.systemId;
  }
  defineExpose({ validateStep, getFormData });
</script>
<style lang="less" scoped>
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
