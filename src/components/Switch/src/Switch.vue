<template>
  <div>
    <a-switch
      :size="size"
      v-model:checked="value"
      :checkedValue="checkedValue"
      :unCheckedValue="unCheckedValue"
      :disabled="disabled"
      :checkedChildren="checkedChildren"
      :unCheckedChildren="unCheckedChildren"
      v-bind="$attrs"
      @change="handleChange"
    />
  </div>
</template>
<script lang="ts">
  const appStore = useAppStore();
</script>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { isBoolean } from '/@/utils/is';
  import { useAppStore } from '/@/store/modules/app';

  const props = defineProps({
    size: String,
    checked: [String, Boolean, Number],
    disabled: Boolean,
    checkedChildren: String,
    unCheckedChildren: String,
    checkedColor: { type: String, default: appStore.getProjectConfig.themeColor },
    unCheckedColor: { type: String, default: '#bbbdbf' },
    checkedValue: { type: [String, Number, Boolean], default: 1 },
    unCheckedValue: { type: [String, Number, Boolean], default: 0 },
  });

  const value = ref();
  const emit = defineEmits(['update:checked', 'change']);
  watch(
    () => props.checked,
    (val) => {
      value.value = isBoolean(val) ? val : Number(val)!;
    },
    {
      immediate: true,
    },
  );

  const handleChange = (value) => {
    emit('update:checked', value);
    emit('change', value);
  };
</script>
<style lang="less" scoped>
  :deep(.ant-switch) {
    background-color: v-bind('props.unCheckedColor');
  }

  :deep(.ant-switch-checked) {
    background-color: v-bind('props.checkedColor');
  }
</style>
