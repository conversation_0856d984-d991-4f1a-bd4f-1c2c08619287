<template>
  <p
    :align="align"
    :style="{
      fontFamily: fontFamily,
      fontSize: fontSize + 'px',
      fontWeight: fontWeight,
      color: color,
      fontStyle: fontStyle,
    }"
  >
    {{ defaultValue }}</p
  >
</template>
<script lang="ts" setup>
  defineProps({
    defaultValue: { type: String },
    color: { type: String, default: '#000' },
    fontFamily: { type: String, default: 'Arial' },
    fontSize: { type: Number, default: 12 },
    fontWeight: { type: [Number, String], default: 'normal' },
    fontStyle: { type: String, default: 'normal' },
    align: { type: String, default: 'left' },
  });
</script>
