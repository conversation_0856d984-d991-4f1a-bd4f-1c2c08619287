import { DesktopComponent } from '/@/enums/desktop';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export interface ComponentConfig {
  x: number;
  y: number;
  w: number;
  h: number;
  id: number;
  type: DesktopComponent;
  props?: null;
}

export const basicComponents = [
  {
    type: 'dashboard',
    label: t('数据面板'),
    icon: 'el-icon-price-tag',
    w: 2,
    h: 2,
    minW: 2,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {
      title: t('默认值'),
    },
    theme: 0,
  },
  {
    type: 'infolist',
    label: t('信息列表'),
    icon: 'fa fa-list-alt',
    w: 6,
    h: 4,
    minW: 4,
    minH: 2,
    maxW: 12,
    maxH: 12,
    props: {},
  },
];

export const chartComponents = [
  {
    type: 'chartbar',
    label: t('柱状图'),
    icon: 'el-icon-s-data',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {},
  },
  {
    type: 'chartline',
    label: t('折线图'),
    icon: 'fa fa-line-chart',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {},
  },
  {
    type: 'chartpie',
    label: t('饼图'),
    icon: 'el-icon-pie-chart',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {},
  },
  {
    type: 'chartradar',
    label: t('雷达图'),
    icon: 'fa fa-first-order',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {},
  },
  {
    type: 'chartgauge',
    label: t('仪表盘'),
    icon: 'fa fa-tachometer',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
    props: {},
  },
];

export const systemComponents = [
  {
    type: 'mytask',
    label: t('我的任务'),
    icon: 'fa fa-tasks',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
  },
  {
    type: 'mytasklist',
    label: t('待办事项'),
    icon: 'el-icon-s-order',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
  },
  {
    type: 'modules',
    label: t('常用功能'),
    icon: 'el-icon-receiving',
    w: 3,
    h: 2,
    minW: 3,
    minH: 2,
    maxW: 12,
    maxH: 2,
  },
];

export interface FieldInfo {
  name: string;
  type: string;
  length: number;
}

export interface TableInfo {
  name: string;
  master: boolean;
  fields: FieldInfo[];
}

export interface RemoteInfo {
  label: string;
  value: string;
}
