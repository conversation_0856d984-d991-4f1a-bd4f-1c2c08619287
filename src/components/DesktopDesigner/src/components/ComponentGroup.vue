<template>
  <div class="widget-cate">{{ title }}</div>
  <Draggable
    tag="ul"
    item-key="type"
    ghostClass="ghost"
    :group="{ name: 'people', pull: 'clone', put: false }"
    :sort="false"
    :list="list"
  >
    <template #item="{ element }">
      <li
        v-if="fields.includes(element.type)"
        class="form-edit-widget-label"
        :class="{ 'no-put': element.type === 'divider' }"
        @click="handleClick(element)"
      >
        <a>
          <Icon :icon="element.icon" />
          <span>{{ element.label }}</span>
        </a>
      </li>
    </template>
  </Draggable>
</template>

<script lang="ts">
  import { defineComponent, PropType } from 'vue';
  import Draggable from 'vuedraggable';
  import { Icon } from '/@/components/Icon';

  export default defineComponent({
    name: 'ComponentGroup',
    components: {
      Draggable,
      Icon,
    },
    props: {
      title: {
        type: String,
        required: true,
      },
      fields: {
        type: Array as PropType<Array<string>>,
        required: true,
      },
      list: {
        type: Array as PropType<Array<any>>,
        required: true,
      },
    },
    emits: ['copy'],
    setup(_, { emit }) {
      const handleClick = (element: any) => {
        emit('copy', element);
      };

      return { handleClick };
    },
  });
</script>

<style scoped lang="less">
  @import '/@/assets/style/designer/index.css';
</style>
