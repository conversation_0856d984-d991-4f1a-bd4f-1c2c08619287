<template>
  <div>
    <a-form layout="vertical" v-if="config" :key="config.key">
      <a-form-item label="显示文字">
        <a-input v-model:value="config.title" />
      </a-form-item>

      <a-form-item label="数据源">
        <a-select @change="handleDataSourceChange" v-model:value="config.sourceId">
          <a-select-option v-for="(dic, idx) in sourceOptions" :value="dic.value" :key="idx">{{
            dic.label
          }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="显示字段">
        <a-select @change="handleSelectShowFied" v-model:value="config.showField">
          <a-select-option v-for="(dic, idx) in showfieldOptions" :value="dic.value" :key="idx">{{
            dic.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>
<script lang="ts" setup>
  import { watch, ref } from 'vue';
  import { RemoteInfo } from '../../types';

  const emit = defineEmits(['update:data']);

  const props = defineProps({
    data: Object,
  });

  const config = ref(props.data);
  const sourceOptions = ref<RemoteInfo[]>();
  const showfieldOptions = ref<RemoteInfo[]>();

  watch(props, (val) => (config.value = val.data));

  watch(config, (val) => emit('update:data', val));

  const handleDataSourceChange = () => {
    console.log();
  };

  const handleSelectShowFied = () => {
    console.log();
  };
</script>
<style></style>
