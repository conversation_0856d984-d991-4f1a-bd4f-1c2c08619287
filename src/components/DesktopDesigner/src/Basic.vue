<template>
  <div class="basic-box">
    <div class="form-box">
      <a-form :model="data.formState">
        <a-form-item label="编号">
          <a-input v-model:checked="data.formState.code" />
        </a-form-item>
        <a-form-item label="名称">
          <a-input v-model:value="data.formState.name" />
        </a-form-item>
        <a-form-item label="图标">
          <IconPicker v-model:value="data.formState.icon" />
        </a-form-item>
        <a-form-item label="上级">
          <MenuSelect v-model:value="data.formState.parentId" />
        </a-form-item>
        <a-form-item label="菜单">
          <a-switch v-model:checked="data.formState.isMenu" />
        </a-form-item>
        <a-form-item label="首页">
          <a-switch v-model:checked="data.formState.isFirst" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:checked="data.formState.remark" :rows="8" />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { IconPicker } from '/@/components/Icon';
  import { MenuSelect } from '/@/components/MenuSelect';
  const data = reactive({
    formState: {
      code: '', //编码
      name: '', //名称
      icon: '', //图标
      parentId: '', //上级
      sortCode: '', //排序
      isFirst: false, //首屏
      isMenu: false, //菜单
      remark: '', //描述
    },
  });
</script>

<style lang="less" scoped>
  .basic-box {
    height: 100%;
    padding: 24px;
  }

  .form-box {
    height: calc(100vh - 114px);
    width: 100%;
    max-width: 794px;
    overflow: hidden auto;
    background-color: #fff;
    border-radius: 4px;
    margin: auto;
    padding: 24px;
  }
</style>
