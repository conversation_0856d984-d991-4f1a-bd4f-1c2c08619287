<template>
  <div v-if="!isSave">
    <a-input
      :class="className"
      :size="size"
      v-model:value="value"
      :placeholder="placeholder"
      :maxlength="parseInt(maxlength)"
      :addonBefore="addonBefore"
      :addonAfter="addonAfter"
      :allowClear="allowClear"
      :disabled="disabled"
      :readonly="readonly"
      :bordered="bordered"
      @change="handleChange"
      @blur="emit('blur')"
    >
      <template #prefix v-if="prefix">
        <Icon :icon="prefix" />
      </template>
      <template #suffix v-if="suffix">
        <Icon :icon="suffix" />
      </template>
    </a-input>
  </div>
  <div v-else>{{ value }}</div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Icon } from '/@/components/Icon';

  const props = defineProps({
    size: String,
    value: {
      type: String,
      default: '',
    },
    defaultValue: {
      type: String,
      default: '',
    },
    placeholder: String,
    maxlength: {
      type: [String, Number],
      default: '',
    },
    addonBefore: String,
    addonAfter: String,
    allowClear: Boolean,
    disabled: Boolean,
    readonly: Boolean,
    prefix: String,
    suffix: String,
    bordered: {
      type: Boolean,
      default: true,
    },
    isSave: Boolean,
    style: Object,
    className: String,
  });
  const value = ref('');
  const emit = defineEmits(['update:value', 'change', 'blur']);

  watch(
    () => props.value,
    (val) => {
      value.value = val;
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.defaultValue,
    (val) => {
      if (val) {
        emit('update:value', val);
      }
    },
    {
      immediate: true,
    },
  );
  const handleChange = (e) => {
    emit('update:value', e.target.value);
    emit('change', e);
    value.value = props.value === undefined ? e.target.value : props.value;
  };
</script>
