import { GeneratorConfig } from '/@/model/generator/generatorConfig';
import { ColumnType, TableInfo } from '/@/model/generator/tableInfo';

/**
 * 单表 数据库表结构json
 */
export const generatorConfig: GeneratorConfig = {
  databaseId: 'master',
  formJson: {
    list: [],
    config: {},
    hiddenComponent: [],
  },
  isAdvancedQuery: false, //是否配置高级搜索
  listConfig: {
    buttonConfigs: [],
    columnConfigs: [
      {
        label: '字符串',
        alignType: 'center',
        columnName: 'field_string',
        columnWidth: '20',
      },
      {
        label: '数字',
        alignType: 'center',
        columnName: 'field_long',
        columnWidth: '20',
      },
      {
        label: '整型',
        alignType: 'center',
        columnName: 'field_int',
        columnWidth: '20',
      },
      {
        label: '浮点',
        alignType: 'center',
        columnName: 'field_double',
        columnWidth: '20',
      },
      {
        label: '时间',
        alignType: 'center',
        columnName: 'field_datetime',
        columnWidth: '20',
      },
    ],
    isLeftMenu: true,
    isPage: true,
    leftMenuConfig: {
      childIcon: '',
      dataSourceId: '0',
      datasourceType: 'api',
      dictionaryItemId: '0',
      fieldName: '',
      isDictionary: true,
      // isTree: true,
      leftWidth: 1,
      menuName: '',
      parentFiledName: '',
      parentIcon: '',
      showFieldName: '',
      apiConfig: {},
      staticData: [],
    },
    orderBy: '',
    orderType: 'desc',
    queryConfigs: [
      {
        fieldName: 'field_string',
        label: 'q1',
      },
      {
        fieldName: 'field_datetime',
        label: 'q2',
      },
      {
        fieldName: 'field_int',
        label: 'q3',
      },
    ],
    totalConfigs: [],
  },
  menuConfig: {
    code: 'xx',
    icon: 'xx',
    name: 'xx',
    system: '',
    parentId: '',
    remark: 'xxx',
    sortCode: 0,
  },
  outputConfig: {
    className: 'demo',
    comment: 'xx',
    creator: 'xx',
    onlyInterface: true,
    onlyFront: true,
    outputArea: 'bi',
    isApp: true,
    isMenu: false,
  },
  tableConfigs: [
    {
      isMain: true,
      pkField: 'id',
      relationField: '',
      tableName: 'xjr_demo',
      relationTableField: '',
    },
  ],
};

/**
 * 单表 数据库表结构json
 */
export const tableInfo: TableInfo[] = [
  {
    tableName: 'xjr_demo',
    columnInfo: [
      {
        columnName: 'id',
        columnType: ColumnType.NUMBER,
        columnLength: '',
        isPrimaryKey: true,
        isNullable: false,
      },
      {
        columnName: 'field_string',
        columnType: ColumnType.STRING,
        columnLength: '50',
        isPrimaryKey: false,
        isNullable: true,
      },
      {
        columnName: 'field_datetime',
        columnType: ColumnType.STRING,
        columnLength: '50',
        isPrimaryKey: false,
        isNullable: true,
      },
      {
        columnName: 'field_double',
        columnType: ColumnType.NUMBER,
        columnLength: '50',
        isPrimaryKey: false,
        isNullable: true,
      },
      {
        columnName: 'field_int',
        columnType: ColumnType.NUMBER,
        columnLength: '50',
        isPrimaryKey: false,
        isNullable: true,
      },
      {
        columnName: 'field_long',
        columnType: ColumnType.NUMBER,
        columnLength: '50',
        isPrimaryKey: false,
        isNullable: true,
      },
    ],
  },
];

// /**
//  * 多表 代码生成器配置json
//  */
// export const generatorConfig: GeneratorConfig = {
//   databaseId: 'master',
//   formJson: {
//     list: [],
//     config: {},
//   },
//   listConfig: {
//     buttonConfigs: [],
//     columnConfigs: [
//       {
//         label: '字符串',
//         alignType: 'center',
//         columnName: 'field_string',
//         columnWidth: '20',
//       },
//       {
//         label: '数字',
//         alignType: 'center',
//         columnName: 'field_long',
//         columnWidth: '20',
//       },
//       {
//         label: '整型',
//         alignType: 'center',
//         columnName: 'field_int',
//         columnWidth: '20',
//       },
//     ],
//     isLeftMenu: true,
//     isPage: true,
//     leftMenuConfig: {
//       childIcon: '',
//       dataSourceId: '0',
//       dictionaryItemId: '0',
//       fieldName: '',
//       isDictionary: true,
//       isTree: true,
//       leftWidth: 1,
//       menuName: '',
//       parentFiledName: '',
//       parentIcon: '',
//       showFieldName: '',
//     },
//     orderBy: '',
//     orderType: 'desc',
//     queryConfigs: [
//       {
//         fieldName: 'field_string',
//         isDate: false,
//         label: 'q1',
//       },
//       {
//         fieldName: 'field_long',
//         isDate: false,
//         label: 'q2',
//       },
//       {
//         fieldName: 'field_int',
//         isDate: false,
//         label: 'q3',
//       },
//     ],
//     totalConfigs: [],
//   },
//   menuConfig: {
//     code: 'xx',
//     icon: 'xx',
//     name: 'xx',
//     parentId: '',
//     remark: 'xxx',
//     sortCode: 0,
//   },
//   outputConfig: {
//     className: 'demo',
//     comment: 'xx',
//     creator: 'xx',
//     onlyInterface: true,
//     onlyFront: true,
//     outputArea: 'bi',
//     isApp: true,
//     isMenu: false,
//   },
//   tableConfigs: [
//     {
//       isMain: true,
//       pkField: 'id',
//       relationField: '',
//       tableName: 'xjr_parent',
//       relationTableField: '',
//     },
//     {
//       isMain: false,
//       pkField: 'id',
//       relationField: 'parent_id',
//       tableName: 'xjr_child',
//       relationTableField: 'id',
//     },
//   ],
// };

// /**
//  * 多表 数据库表结构json
//  */
// export const tableInfo: TableInfo[] = [
//   {
//     tableName: 'xjr_parent',
//     columnInfo: [
//       {
//         columnName: 'id',
//         columnType: ColumnType.NUMBER,
//         columnLength: '',
//         isPrimaryKey: true,
//         isNullable: false,
//       },
//       {
//         columnName: 'field_string',
//         columnType: ColumnType.STRING,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//       {
//         columnName: 'field_int',
//         columnType: ColumnType.NUMBER,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//       {
//         columnName: 'field_long',
//         columnType: ColumnType.NUMBER,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//     ],
//   },
//   {
//     tableName: 'xjr_child',
//     columnInfo: [
//       {
//         columnName: 'id',
//         columnType: ColumnType.NUMBER,
//         columnLength: '',
//         isPrimaryKey: true,
//         isNullable: false,
//       },
//       {
//         columnName: 'field_string',
//         columnType: ColumnType.STRING,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//       {
//         columnName: 'field_int',
//         columnType: ColumnType.NUMBER,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//       {
//         columnName: 'field_long',
//         columnType: ColumnType.NUMBER,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//       {
//         columnName: 'parent_id',
//         columnType: ColumnType.NUMBER,
//         columnLength: '50',
//         isPrimaryKey: false,
//         isNullable: true,
//       },
//     ],
//   },
// ];
