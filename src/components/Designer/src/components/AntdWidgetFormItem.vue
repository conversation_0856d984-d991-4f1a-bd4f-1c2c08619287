<template>
  <div class="widget-item-container">
    <a-form-item
      class="widget-view"
      v-if="element"
      :key="element.key"
      :class="{
        active: selectWidget?.key === element.key,
        'widget-col': element.type === 'grid' || element.type === 'card',
      }"
      :label="!noHaveTitle.includes(element.type) && element.options.showLabel ? element.label : ''"
      :rules="element.options.rules"
      :required="element.options.required"
      :labelCol="itemLabelCol"
    >
      <template v-if="element.type === 'input'">
        <a-input
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :maxlength="parseInt(element.options.maxlength)"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
        >
          <template #prefix v-if="element.options.prefix">
            <Icon :icon="element.options.prefix" />
          </template>
          <template #suffix v-if="element.options.suffix">
            <Icon :icon="element.options.suffix" />
          </template>
        </a-input>
      </template>

      <template v-if="element.type === 'password'">
        <a-input-password
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :maxlength="element.options.maxlength"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
          :visibilityToggle="element.options.visibilityToggle"
        >
          <template #prefix v-if="element.options.prefix">
            <Icon :icon="element.options.prefix" />
          </template>
        </a-input-password>
      </template>
      <template v-if="element.type === 'labelComponent'">
        <XjrLabelComponent
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :styleConfig="element.options.styleConfig"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
        >
          <template #prefix v-if="element.options.prefix">
            <Icon :icon="element.options.prefix" />
          </template>
        </XjrLabelComponent>
      </template>
      <template v-if="element.type === 'textarea' || element.type === 'opinion'">
        <a-textarea
          :key="updateKey"
          style="resize: none"
          :size="config.size"
          :rows="element.options.rows"
          v-model:value="element.options.defaultValue"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :maxlength="element.options.maxlength"
          :showCount="element.options.showCount"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly || element.type === 'opinion'"
          :autoSize="element.options.autoSize"
        />
      </template>

      <template v-if="element.type === 'area'">
        <SelectArea
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
        />
      </template>
      <template v-if="element.type === 'auto-code'">
        <AutoCodeRule
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :prefix="element.options.prefix"
          :suffix="element.options.suffix"
          :autoCodeRule="element.options.autoCodeRule"
        />
      </template>

      <template v-if="element.type === 'number'">
        <a-input-number
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :style="{ width: element.options.width }"
          :max="element.options.max"
          :min="element.options.min"
          :step="element.options.step"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
          :maxlength="element.options.maxlength"
        />
      </template>

      <template v-if="element.type === 'radio'">
        <ApiRadioGroup
          :size="config.size"
          v-model:value="element.options.defaultSelect"
          :style="{
            width: element.options.width,
          }"
          :disabled="element.options.disabled"
          :optionType="element.options.optionType"
          :params="element.options.params"
          :labelField="element.options.labelField"
          :valueField="element.options.valueField"
          :datasourceType="element.options.datasourceType"
          :staticOptions="element.options.staticOptions"
          :defaultSelect="element.options.defaultSelect"
          :apiConfig="element.options.apiConfig"
          :isDesign="true"
          :class="[element.options.optionType === 'button' ? 'radio-button' : 'radio-default']"
        />
      </template>

      <template v-if="element.type === 'checkbox'">
        <ApiCheckboxGroup
          :size="config.size"
          v-model:value="element.options.defaultSelect"
          :disabled="element.options.disabled"
          :params="element.options.params"
          :labelField="element.options.labelField"
          :valueField="element.options.valueField"
          :datasourceType="element.options.datasourceType"
          :staticOptions="element.options.staticOptions"
          :defaultSelect="element.options.defaultSelect"
          :apiConfig="element.options.apiConfig"
        />
      </template>

      <template v-if="element.type === 'time'">
        <TimePicker
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :placeholder="element.options.placeholder"
          :allowClear="element.options.allowClear"
          :format="element.options.format"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        />
      </template>

      <template v-if="element.type === 'date'">
        <XjrDatePicker
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :placeholder="element.options.placeholder"
          :allowClear="element.options.allowClear"
          :format="element.options.format"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        />
      </template>

      <template v-if="element.type === 'date-range'">
        <a-range-picker
          :size="config.size"
          :format="element.options.format"
          :show-time="element.options.showTime"
          :placeholder="[element.options.startTimePlaceholder, element.options.endTimePlaceholder]"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        />
      </template>

      <template v-if="element.type === 'time-range'">
        <a-time-range-picker
          :size="config.size"
          :format="element.options.format"
          :show-time="element.options.showTime"
          :placeholder="[element.options.startTimePlaceholder, element.options.endTimePlaceholder]"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        />
      </template>

      <template v-if="element.type === 'rate'">
        <a-rate
          v-model:value="element.options.defaultValue"
          :count="element.options.count"
          :allowHalf="element.options.allowHalf"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
        />
      </template>

      <template v-if="element.type === 'select'">
        <ApiSelect
          :size="config.size"
          :mode="element.options.mode"
          v-model:value="element.options.defaultSelect"
          :placeholder="element.options.placeholder"
          :filter-option="handleFilterOption"
          :allowClear="element.options.clearable"
          :showSearch="element.options.showSearch"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
          :style="{ width: element.options.width }"
          :params="element.options.params"
          :labelField="element.options.labelField"
          :valueField="element.options.valueField"
          :datasourceType="element.options.datasourceType"
          :staticOptions="element.options.staticOptions"
          :defaultSelect="element.options.defaultSelect"
          :apiConfig="element.options.apiConfig"
          :isMultiple="element.options.isMultiple"
        />
      </template>

      <template v-if="element.type === 'switch'">
        <XjrSwitch
          :size="config.size"
          v-model:checked="element.options.defaultValue"
          :checkedChildren="element.options.checkedChildren"
          :unCheckedChildren="element.options.unCheckedChildren"
          :disabled="element.options.disabled"
          :checkedColor="element.options.checkedColor"
          :unCheckedColor="element.options.unCheckedColor"
        />
      </template>

      <template v-if="element.type === 'slider'">
        <a-slider
          v-model:value="element.options.defaultValue"
          :min="element.options.min"
          :max="element.options.max"
          :step="element.options.step"
          :reverse="element.options.reverse"
          :disabled="element.options.disabled"
        />
      </template>

      <template v-if="element.type == 'text'">
        <p
          :style="{
            fontFamily: element.options.fontFamily,
            fontSize: element.options.fontSize + 'px',
            fontWeight: element.options.fontWeight,
            color: element.options.color,
            textAlign: element.options.align,
            fontStyle: element.options.fontStyle,
          }"
          >{{ element.options.defaultValue }}</p
        >
      </template>

      <template v-if="element.type === 'multiple-popup' || element.type === 'associate-popup'">
        <a-input
          :readonly="element.options.readonly"
          v-model:value="element.options.defaultValue"
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :maxlength="parseInt(element.options.maxlength)"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
        />
      </template>

      <template v-if="element.type === 'associate-select'">
        <AssociateSelect
          :size="config.size"
          :mode="element.options.mode"
          v-model:value="element.options.defaultSelect"
          :placeholder="element.options.placeholder"
          :filter-option="handleFilterOption"
          :showSearch="element.options.showSearch"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
          :labelField="element.options.labelField"
          :valueField="element.options.valueField"
          :datasourceType="element.options.datasourceType"
          :params="element.options.params"
          :staticOptions="element.options.staticOptions"
          :defaultSelect="element.options.defaultSelect"
          :apiConfig="element.options.apiConfig"
        />
      </template>

      <template v-if="element.type === 'picker-color'">
        <ColorPicker
          v-model:value="element.options.defaultValue"
          :disabled="element.options.disabled"
        />
      </template>

      <template v-if="element.type === 'button'">
        <a-button
          type="primary"
          :size="config.size"
          :disabled="element.options.disabled"
          :style="{
            height: element.options.buttonHeight ? element.options.buttonHeight + 'px' : '',
            width: element.options.buttonWidth ? element.options.buttonWidth + 'px' : '',
          }"
        >
          <Icon :icon="element.options.prefix" />
          {{ element.options.name }}
          <Icon :icon="element.options.suffix" />
        </a-button>
      </template>

      <template v-if="element.type == 'image'">
        <Image
          v-model:value="element.options.defaultValue"
          :key="updateKey"
          :isUpload="element.options.isUpload"
        />
      </template>

      <template v-if="element.type == 'file'">
        <File v-model:defaultValue="element.options.defaultValue" :isShowDel="true" />
      </template>

      <template v-if="element.type == 'divider'">
        <a-divider
          :orientation="element.options.orientation"
          :style="{
            marginTop: element.options.marginTop + 'px',
            marginBottom: element.options.marginBottom + 'px',
          }"
        >
          {{ element.options.defaultValue }}
        </a-divider>
      </template>

      <template v-if="element.type === 'upload'">
        <Upload
          :accept="element.options.accept"
          :maxSize="element.options.maxSize"
          :maxNumber="element.options.maxNumber"
          :multiple="element.options.multiple"
          :disabled="element.options.disabled"
          v-model:value="uploadValue"
        />
        <p style="color: #ccc; font-size: 13px">{{
          t('支持扩展名：.rar .zip .doc .docx .pdf .jpg...')
        }}</p>
      </template>

      <template v-if="element.type === 'richtext-editor'">
        <RichTextEditor
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
          v-model:value="element.options.defaultValue"
          :width="element.options.width"
        />
      </template>

      <template v-if="element.type === 'cascader'">
        <ApiCascader
          :size="config.size"
          :placeholder="element.options.placeholder"
          :separator="element.options.separator"
          :showFormat="element.options.showFormat"
          :selectedConfig="element.options.selectedConfig"
          :apiConfig="element.options.apiConfig"
          :allowClear="element.options.allowClear"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        />
      </template>
      <template v-if="element.type === 'title'">
        <a-row
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
        >
          <a-col :span="24">
            <h2
              :align="element.options.align"
              :style="{
                fontSize: element.options.fontSize + 'px',
                color: element.options.color,
                fontWeight: 'bold',
              }"
            >
              {{ element.options.defaultValue }}
            </h2>
          </a-col>
        </a-row>
      </template>

      <template v-if="element.type === 'grid'">
        <a-row
          type="flex"
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
          :gutter="element.options.gutter ?? 0"
          :justify="element.options.justify"
          :align="element.options.align"
        >
          <a-col v-for="(col, colIndex) of element.layout" :key="colIndex" :span="col.span ?? 0">
            <Draggable
              class="widget-col-list"
              item-key="key"
              ghostClass="ghost"
              handle=".drag-widget"
              :animation="200"
              :group="{ name: 'people' }"
              :no-transition-on-drag="true"
              :list="col.list"
              @add="handleColMoveAdd($event, col.list, element)"
            >
              <template #item="{ element: childElement, index: childIndex }">
                <div>
                  <AntdWidgetFormItem
                    v-if="childElement.key"
                    :key="childElement.key"
                    :element="childElement"
                    class="drag-widget"
                    :config="config"
                    :selectWidget="selectWidget"
                    @click.stop="handleItemClick(childElement)"
                    @copy="handleCopyClick(col.list, childIndex)"
                    @delete="handleDeleteClick(col.list, childIndex)"
                  />
                </div>
              </template>
            </Draggable>
          </a-col>
        </a-row>
      </template>

      <template v-if="element.type === 'tab'">
        <a-row
          class="widget-col widget-view"
          type="flex"
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
          :gutter="element.options.gutter ?? 0"
          :justify="element.options.justify"
          :align="element.options.align"
          @click="handleItemClick(element)"
        >
          <a-tabs
            style="width: 100%"
            :type="element.options.type"
            :tab-position="element.options.tabPosition"
            default-active-key="0"
          >
            <a-tab-pane v-for="(col, colIndex) of element.layout" :key="colIndex" :tab="col.name">
              <Draggable
                class="widget-col-list"
                item-key="key"
                ghostClass="ghost"
                handle=".drag-widget"
                :animation="200"
                :group="{ name: 'people' }"
                :no-transition-on-drag="true"
                :list="col.list"
                @add="handleColMoveAdd($event, col.list, element)"
              >
                <template #item="{ element: childElement, index: childIndex }">
                  <div>
                    <AntdWidgetFormItem
                      v-if="childElement.key"
                      :key="childElement.key"
                      :element="childElement"
                      class="drag-widget"
                      :config="config"
                      :selectWidget="selectWidget"
                      :labelCol="config.labelCol"
                      @click.stop="handleItemClick(childElement)"
                      @copy="handleCopyClick(col.list, childIndex)"
                      @delete="handleDeleteClick(col.list, childIndex)"
                    />
                  </div>
                </template>
              </Draggable>
            </a-tab-pane>
          </a-tabs>
        </a-row>
      </template>

      <template
        v-if="
          element.type === 'form' || element.type === 'one-for-one' || element.type === 'sun-form'
        "
      >
        <a-row
          class="widget-col widget-view"
          type="flex"
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
          :gutter="element.options.gutter ?? 0"
          :justify="element.options.justify"
          :align="element.options.align"
          @click="handleItemClick(element)"
        >
          <a-col :span="24">
            <Draggable
              class="widget-col-list"
              item-key="key"
              ghostClass="ghost"
              handle=".drag-widget"
              :animation="200"
              :group="{ name: 'people' }"
              :no-transition-on-drag="true"
              :list="element.children"
              @add="handleColMoveAdd($event, element.children, element)"
            >
              <template #item="{ element: childElement, index: childIndex }">
                <transition-group name="fade" tag="div">
                  <AntdWidgetFormItem
                    v-if="childElement.key"
                    :key="childElement.key"
                    :element="childElement"
                    class="drag-widget"
                    :config="config"
                    :selectWidget="selectWidget"
                    :labelCol="config.labelCol"
                    @click.stop="handleItemClick(childElement)"
                    @copy="handleCopyClick(element.children, childIndex)"
                    @delete="handleDeleteClick(element.children, childIndex)"
                  />
                </transition-group>
              </template>
            </Draggable>
          </a-col>
        </a-row>
      </template>

      <template v-if="element.type === 'form-view'">
        <FormView
          :preloadType="element.options.preloadType"
          :apiConfig="element.options.apiConfig"
          :itemId="element.options.itemId"
          :isPagination="element.options.isPagination"
          :showIndex="element.options.showIndex"
          :showFormBorder="element.options.showFormBorder"
        />
      </template>

      <template v-if="element.type === 'iframe'">
        <!-- iframe 传值 -->
        <XjrIframe :url="element.options.url" :list="element.options.list" />
      </template>

      <template v-if="element.type === 'card'">
        <span>{{ element.options.title }}</span>
        <a-row
          type="flex"
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
          :gutter="element.options.gutter ?? 0"
          :justify="element.options.justify"
          :align="element.options.align"
        >
          <a-col v-for="(col, colIndex) of element.layout" :key="colIndex" :span="col.span ?? 0">
            <Draggable
              class="widget-col-list"
              item-key="key"
              ghostClass="ghost"
              handle=".drag-widget"
              :animation="200"
              :group="{ name: 'people' }"
              :no-transition-on-drag="true"
              :list="col.list"
              @add="handleColMoveAdd($event, col.list, element)"
            >
              <template #item="{ element: childElement, index: childIndex }">
                <div>
                  <AntdWidgetFormItem
                    v-if="childElement.key"
                    :key="childElement.key"
                    :element="childElement"
                    class="drag-widget"
                    :config="config"
                    :selectWidget="selectWidget"
                    :labelCol="config.labelCol"
                    :childIndex="childIndex"
                    @click.stop="handleItemClick(childElement)"
                    @copy="handleCopyClick(col.list, childIndex)"
                    @delete="handleDeleteClick(col.list, childIndex)"
                  />
                </div>
              </template>
            </Draggable>
          </a-col>
        </a-row>
      </template>
      <template v-if="element.type === 'table-layout'">
        <TableLayout
          :element="element"
          @click="handleTableLayoutClick(element)"
          v-if="element.key"
          :key="element.key"
        >
          <template #tdElement="{ tdElement }">
            <div class="h-full">
              <Draggable
                :style="{
                  height: tdElement.height ? tdElement.height + 'px' : '',
                  minHeight: (tdElement.height || '42') + 'px',
                  overflow: 'hidden',
                  padding: '10px',
                }"
                item-key="key"
                ghostClass="ghost"
                handle=".drag-widget"
                :animation="200"
                :group="{ name: 'people' }"
                :no-transition-on-drag="true"
                :list="tdElement.children"
                @add="handleColMoveAdd($event, tdElement.children, element)"
              >
                <template #item="{ element: childElement, index: childIndex }">
                  <div>
                    <AntdWidgetFormItem
                      v-if="childElement.key"
                      :key="childElement.key"
                      :element="childElement"
                      class="drag-widget"
                      :config="config"
                      :selectWidget="selectWidget"
                      :childIndex="childIndex"
                      @click.stop="handleItemClick(childElement)"
                      @copy="handleCopyClick(tdElement.children, childIndex)"
                      @delete="handleDeleteClick(tdElement.children, childIndex)"
                    />
                  </div>
                </template>
              </Draggable>
            </div>
          </template>
        </TableLayout>
      </template>
      <!-- <template v-if="element.type === 'one-for-one'">
        <span>{{ element.options.title }}</span>
        <a-row
          type="flex"
          v-if="element.key"
          :key="element.key"
          :class="{ active: selectWidget?.key === element.key }"
          :gutter="element.options.gutter ?? 0"
          :justify="element.options.justify"
          :align="element.options.align"
        >
          <a-col v-for="(col, colIndex) of element.layout" :key="colIndex" :span="col.span ?? 0">
            <Draggable
              class="widget-col-list"
              item-key="key"
              ghostClass="ghost"
              handle=".drag-widget"
              :animation="200"
              :group="{ name: 'people' }"
              :no-transition-on-drag="true"
              :list="col.list"
              @add="handleColMoveAdd($event, col.list, element)"
            >
              <template #item="{ element: childElement, index: childIndex }">
                <div>
                  <AntdWidgetFormItem
                    v-if="childElement.key"
                    :key="childElement.key"
                    :element="childElement"
                    class="drag-widget"
                    :config="config"
                    :selectWidget="selectWidget"
                    :labelCol="config.labelCol"
                    :childIndex="childIndex"
                    @click.stop="handleItemClick(childElement)"
                    @copy="handleCopyClick(col.list, childIndex)"
                    @delete="handleDeleteClick(col.list, childIndex)"
                  />
                </div>
              </template>
            </Draggable>
          </a-col>
        </a-row>
      </template> -->

      <template v-if="element.type === 'user'">
        <a-input
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :placeholder="element.options.placeholder"
          :disabled="element.options.disabled"
          :style="{ width: element.options.width }"
        >
          <template #prefix v-if="element.options.prefix">
            <Icon :icon="element.options.prefix" />
          </template>
          <template #suffix v-if="element.options.suffix">
            <Icon :icon="element.options.suffix" />
          </template>
        </a-input>
      </template>
      <template v-if="element.type === 'computational'">
        <Computation
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :maxlength="parseInt(element.options.maxlength)"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :disabled="element.options.disabled"
          :readonly="element.options.readonly"
          :prefix="element.options.prefix"
          :decimals="element.options.decimals"
        />
      </template>

      <template v-if="element.type === 'money-chinese'">
        <MoneyChineseInput
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :maxlength="parseInt(element.options.maxlength)"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
          :disabled="element.options.disabled"
          :prefix="element.options.prefix"
          :suffix="element.options.suffix"
          :decimals="element.options.decimals"
        />
      </template>
      <template v-if="element.type === 'info'">
        <CommonInfo
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :infoType="element.options.infoType"
          :isShowAllName="element.options.isShowAllName"
          disabled
        />
      </template>
      <template v-if="element.type === 'organization'">
        <SelectDepartment
          :size="config.size"
          :style="{ width: element.options.width }"
          :placeholder="element.options.placeholder"
          :disabled="element.options.disabled"
          :isShowAllName="element.options.isShowAllName"
        />
      </template>
      <template v-if="element.type === 'map'">
        <SelectMap
          :size="config.size"
          v-model:value="element.options.defaultValue"
          :placeholder="element.options.placeholder"
          :disabled="element.options.disabled"
          :prefix="element.options.prefix"
          :suffix="element.options.suffix"
          :address="element.options.address"
          :latiAndLong="element.options.latiAndLong"
          :tableIndex="childIndex"
          :mainKey="element.bindTable"
          :style="{ width: element.options.width }"
        >
        </SelectMap>
      </template>
      <template v-if="element.type === 'qrcode'">
        <XjrQrcode
          :defaultValue="element.options.defaultValue"
          :disabled="element.options.disabled"
          :apiConfig="element.options.apiConfig"
          :codeType="element.options.codeType"
          :style="{ width: element.options.width }"
        >
        </XjrQrcode>
      </template>
      <template v-if="element.type === 'barcode'">
        <BarCode
          :defaultValue="element.options.defaultValue"
          :disabled="element.options.disabled"
          :apiConfig="element.options.apiConfig"
          :format="element.options.format"
          :codeType="element.options.codeType"
          :displayValue="element.options.displayValue"
          :componentKey="element.key"
        />
      </template>
      <template v-if="element.type === 'signature'">
        <a-input
          placeholder="请选择签字人"
          :disabled="element.options.disabled"
          :addonBefore="element.options.addonBefore"
          :addonAfter="element.options.addonAfter"
        >
          <template #prefix v-if="element.options.prefix">
            <Icon :icon="element.options.prefix" />
          </template>
          <template #suffix v-if="element.options.suffix">
            <Icon :icon="element.options.suffix" />
          </template>
        </a-input>
      </template>
      <template v-if="element.type === 'tree-component'">
        <TreeComponent
          v-model:value="element.options.defaultValue"
          :disabled="element.options.disabled"
          :treeConfig="element.options.treeConfig"
          :style="{ width: element.options.width }"
        >
        </TreeComponent>
      </template>
      <template v-if="element.type === 'tree-select-component'">
        <TreeSelectComponent
          v-model:value="element.options.defaultValue"
          :disabled="element.options.disabled"
          :treeConfig="element.options.treeConfig"
          :style="{ width: element.options.width }"
        >
        </TreeSelectComponent>
      </template>
      <template v-if="element.type === 'auto-complete'">
        <ApiComplete
          v-model:value="element.options.defaultValue"
          :placeholder="element.options.placeholder"
          :disabled="element.options.disabled"
          :options="element.options.staticOptions"
          :style="{ width: element.options.width }"
          :labelField="element.options.labelField"
          :valueField="element.options.valueField"
          :datasourceType="element.options.datasourceType"
          :apiConfig="element.options.apiConfig"
        />
      </template>
    </a-form-item>
    <div
      class="widget-view-action"
      :class="{
        'widget-col-action': element.type === 'grid',
      }"
      v-if="selectWidget?.key === element.key"
    >
      <a-popover placement="top" v-if="element.type === 'table-layout'">
        <template #content>
          <span>添加行</span>
        </template>
        <span class="svgicon icon-add">
          <SvgIcon name="add" @click.stop="$emit('addRow')" />
        </span>
      </a-popover>
      <a-popover placement="top" v-if="element.type === 'table-layout'">
        <template #content>
          <span>添加列</span>
        </template>
        <span class="svgicon icon-add">
          <SvgIcon name="add" @click.stop="$emit('addCol')" />
        </span>
      </a-popover>

      <span class="svgicon icon-copy">
        <SvgIcon name="copy" @click.stop="$emit('copy')" />
      </span>
      <span class="svgicon icon-delete">
        <SvgIcon name="delete" @click.stop="$emit('delete')" />
      </span>
    </div>
    <div
      class="widget-view-drag"
      :class="{
        'widget-col-action': element.type === 'grid',
      }"
      v-if="selectWidget?.key === element.key"
    >
      <span class="svgicon icon-move">
        <SvgIcon name="move" className="drag-widget" />
      </span>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, inject, PropType, computed, watch, ref, Ref } from 'vue';
  import TableLayout from '/@/components/Form/src/components/TableLayout.vue';
  import { SvgIcon, Icon } from '/@/components/Icon';
  import { RichTextEditor } from '/@/components/RichTextEditor/index';
  import AutoCodeRule from '/@/components/Form/src/components/AutoCodeRule.vue';
  import { XjrLabelComponent } from '/@/components/LabelComponent';
  import ApiSelect from '../../../Select/src/Select.vue';
  import ApiCascader from '/@/components/Form/src/components/ApiCascader.vue';
  import { XjrSwitch } from '/@/components/Switch';
  import { ColorPicker } from '/@/components/ColorPicker';
  import { Computation } from '/@/components/Computation';

  import { AssociateSelect } from '/@/components/AssociateSelect';
  import ApiRadioGroup from '/@/components/Form/src/components/ApiRadioGroup.vue';
  import ApiCheckboxGroup from '/@/components/Form/src/components/ApiCheckboxGroup.vue';
  import SelectArea from '/@/components/Form/src/components/SelectArea.vue';
  import CommonInfo from '/@/components/Form/src/components/CommonInfo.vue';
  import SelectDepartment from '/@/components/Form/src/components/SelectDepartment.vue';
  import SelectMap from '/@/components/Form/src/components/SelectMap.vue';
  import XjrQrcode from '/@/components/Form/src/components/QrCode.vue';
  import BarCode from '/@/components/Form/src/components/BarCode.vue';
  import ApiComplete from '/@/components/Form/src/components/ApiComplete.vue';
  import MoneyChineseInput from '/@/components/Form/src/components/MoneyChineseInput.vue';
  import FormView from '/@/components/Form/src/components/FormView.vue';
  import XjrIframe from '/@/components/Form/src/components/XjrIframe.vue';
  import { TimePicker } from '/@/components/TimePicker';
  import { XjrDatePicker } from '/@/components/DatePicker';
  import Upload from '/@/components/Form/src/components/Upload.vue';
  import Image from '/@/components/Form/src/components/Image.vue';
  import File from '/@/components/Form/src/components/File.vue';
  import { TreeComponent, TreeSelectComponent } from '/@/components/TreeStructure';
  import {
    WidgetForm,
    noHaveTitle,
    noHaveField,
    noHaveTableAndField,
    subFormUnUseComponents,
    oneForOneUnUseComponents,
    TableCell,
    TableTh,
  } from '../types';
  import Draggable from 'vuedraggable';
  import { buildUUID } from '/@/utils/uuid';
  import { changeToPinyin } from '/@/utils/event/design';
  import { random } from 'lodash-es';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export default defineComponent({
    name: 'AntdWidgetFormItem',
    components: {
      SvgIcon,
      RichTextEditor,
      Draggable,
      Icon,
      AutoCodeRule,
      ApiSelect,
      ApiCascader,
      AssociateSelect,
      ApiRadioGroup,
      ApiCheckboxGroup,
      SelectArea,
      CommonInfo,
      SelectDepartment,
      MoneyChineseInput,
      XjrSwitch,
      XjrLabelComponent,
      ColorPicker,
      Computation,
      TimePicker,
      XjrDatePicker,
      SelectMap,
      XjrQrcode,
      Upload,
      ApiComplete,
      FormView,
      XjrIframe,
      TableLayout,
      Image,
      File,
      TreeComponent,
      TreeSelectComponent,
      BarCode,
    },
    props: {
      config: {
        type: Object as PropType<WidgetForm['config']>,
        required: true,
      },
      element: {
        type: Object,
        required: true,
      },
      selectWidget: {
        type: Object,
      },
      labelCol: {
        type: Object,
        default: () => {},
      },
      childIndex: {
        type: Number,
      },
    },
    emits: ['copy', 'delete', 'update:widgetFormSelect', 'addRow', 'addCol'],
    setup(props) {
      const { notification } = useMessage();
      let state = inject('state') as any;
      const tablecell = inject<Ref<TableCell>>('tableCell');
      const tableth = inject<Ref<TableTh>>('tableTh');
      const handleFilterOption = (input: string, option) => {
        const label = option.label || option.name;
        return label.toLowerCase().includes(input.toLowerCase());
      };
      const updateKey = ref(0);
      const uploadValue = ref();

      const widgetForm = inject('widgetForm') as any;

      //一对一组件是否包含无法使用的组件
      const oneForOneIsHaveUnUseComp = (compList: any[], isChild: boolean = false): boolean => {
        for (let item of compList) {
          //如果当前遍历到的组件是一对一
          if (item.type === 'one-for-one') {
            //判断一对一组件下面
            const result = oneForOneIsHaveUnUseComp(item.children, true);
            if (result) return true;
          }
          if (item.type === 'tab' || item.type === 'card' || item.type === 'grid') {
            for (const child of item.layout) {
              const result = oneForOneIsHaveUnUseComp(child.list, isChild);
              if (result) return true;
            }
          }
          //如果遍历的是one-for-one 组件里面的子组件 就需要判断是否包含不允许使用的组件
          if (isChild && oneForOneUnUseComponents.includes(item.type)) {
            return true;
          }
        }
        return false;
      };

      const handleColMoveAdd = (event: any, list: any, parent: any = null) => {
        const { newIndex } = event;

        if (oneForOneIsHaveUnUseComp(widgetForm.value.list)) {
          list.splice(newIndex, 1);
          notification.error({
            message: t('提示'),
            description: t('单表组件无法使用子表单组件 以及 单表组件！'),
          });
          return;
        }

        //如果parent参数不为空 就是子表单添加事件 如果是上级未绑定表不允许添加子组件
        if (
          parent &&
          (parent.type === 'form' || parent.type === 'one-for-one' || parent.type === 'sun-form') &&
          !parent.bindTable
        ) {
          list.splice(newIndex, 1);
          notification.error({
            message: t('提示'),
            description:
              parent.type === 'form'
                ? t('子表单组件未绑定表，不允许添加子组件')
                : '单表组件未绑定表，不允许添加子组件',
          });
          return;
        }
        //如果parent参数不为空 就是子表单添加事件 判断是否为子表单不允许的组件
        if (
          parent &&
          parent.type === 'form' &&
          subFormUnUseComponents.includes(list[newIndex].type)
        ) {
          list.splice(newIndex, 1);
          notification.error({
            message: t('提示'),
            description: t('子表单组件不允许使用此类组件'),
          });
          return;
        }

        if (
          parent &&
          parent.type === 'sun-form' &&
          subFormUnUseComponents.includes(list[newIndex].type)
        ) {
          list.splice(newIndex, 1);
          notification.error({
            message: t('提示'),
            description: '次级表格组件不允许使用此类组件',
          });
          return;
        }
        if (list[newIndex].type === 'sun-form') {
          if (parent) {
            if (parent.type !== 'form') {
              list.splice(newIndex, 1);
              notification.error({
                message: t('提示'),
                description:
                  '次级表格组件仅允许拖入表格组件当中，并且一个表格组件仅允许拖入一个次级表格组件，请确认后再拖入组件。',
              });
              return;
            }
            if (parent.type === 'form') {
              const sunFormArr = parent.children.filter((x) => x.type === 'sun-form');
              if (sunFormArr.length > 1) {
                list.splice(newIndex, 1);
                notification.error({
                  message: t('提示'),
                  description: '一个表格组件仅允许拖入一个次级表格组件，请确认后再拖入组件。',
                });
                return;
              }
            }
          }
        }

        //如果parent参数不为空 就是子表单添加事件 判断是否为子表单不允许的组件
        if (
          parent &&
          parent.type === 'one-for-one' &&
          oneForOneUnUseComponents.includes(list[newIndex].type)
        ) {
          list.splice(newIndex, 1);
          notification.error({
            message: t('提示'),
            description: t('单表组件无法使用子表单组件 以及 单表组件！'),
          });
          return;
        }

        //递归向最上级查 如果最上级是单表组件 所有下级组件不允许使用 表格组件 与  单表组件
        if (parent && parent.hasOwnProperty('children')) {
        }

        if (parent && parent.bindTable) {
          //如果parent参数不为空 就是子表单添加事件  并且已经绑定过表  所有子组件也默认绑定
          if (list[newIndex].type === 'sun-form') {
            list[newIndex].parentTable = parent.bindTable;
          } else {
            list[newIndex].bindTable = parent.bindTable;
          }
        }
        if (!list[newIndex].key) {
          const key = buildUUID().replaceAll('-', '');
          list[newIndex] = JSON.parse(JSON.stringify(list[newIndex]));
          list[newIndex].key = key;
        }

        //如果是父级是 子表单组件 默认把下级组件 都加上标识
        if (parent && (parent.type === 'form' || parent.isSubFormChild)) {
          list[newIndex].isSubFormChild = true;
        } else {
          //从子表拖入布局组件时
          list[newIndex].isSubFormChild = false;
        }

        if (parent && (parent.type === 'one-for-one' || parent.isSingleFormChild)) {
          list[newIndex].isSingleFormChild = true;
        } else {
          //从子表拖入布局组件时
          list[newIndex].isSingleFormChild = false;
        }

        //判断是否是栅格布局内组件
        list[newIndex].isGridChild = parent && parent.type === 'grid';
        let bindTable = '';
        if (['tab', 'grid', 'card'].includes(parent.type)) {
          bindTable = parent.bindTable;
        }
        addBindTableAndField(list[newIndex], bindTable);
      };

      const isFieldUpper = inject<Ref<boolean>>('isFieldUpper', ref(false));
      const designType = inject<string>('designType');
      let mainTableName;
      if (designType !== 'data') {
        mainTableName = inject<string>('mainTableName');
      }

      watch(
        () => props.element.label,
        (val) => {
          if (val && designType !== 'data') {
            props.element.bindField = (
              changeToPinyin(val, isFieldUpper.value) + random(1000, 9999)
            ).substr(0, 30);
          }
        },
      );
      watch(
        () => props.element,
        (val, oldVal) => {
          if (val.type === 'form' && oldVal.type !== 'form') {
            state.widgetFormSelect = val;
          }
          updateKey.value++;
        },
        {
          deep: true,
        },
      );
      const itemLabelCol = computed(() => {
        return props.element.options.span ? { span: props.element.options.span } : props.labelCol;
      });

      const handleItemClick = (row: any) => {
        state.widgetFormSelect = row;
        if (row.type === 'table-layout') {
          if (tablecell) tablecell.value = {};
          if (tableth) tableth.value = {};
        }
      };
      const handleTableLayoutClick = (row: any) => {
        state.widgetFormSelect = row;
      };

      const handleCopyClick = (list: any, index: number) => {
        const newObj = JSON.parse(JSON.stringify(list[index]));
        newObj.key = buildUUID().replaceAll('-', '');
        newObj.bindField = '';

        addBindTableAndField(newObj);
        copyLayoutData([newObj], newObj.bindTable);
        list.push(newObj);
      };

      const copyLayoutData = (data, tableName) => {
        data.map((item) => {
          if (['tab', 'grid', 'card'].includes(item.type)) {
            item.bindTable = '';
            item.key = buildUUID().replaceAll('-', '');
            addBindTableAndField(item, tableName);
            for (const child of item.layout!) {
              copyLayoutData(child.list, tableName);
            }
          } else if (['form', 'one-for-one'].includes(item.type)) {
            item.key = buildUUID().replaceAll('-', '');
            addBindTableAndField(item);
            copyLayoutData(item.children, item.bindTable);
          } else {
            item.bindField = '';
            item.bindTable = tableName;
            item.key = buildUUID().replaceAll('-', '');
            addBindTableAndField(item, tableName);
          }
        });
      };
      const handleDeleteClick = async (list: any, index: number) => {
        await deleteAdoptedCalc(index, list);
        list.splice(index, 1);
      };

      const addBindTableAndField = (component: any, bindTable?) => {
        //非代码优先、需要绑定字段、组件没有绑定字段的情况下新增绑定字段
        if (designType !== 'data') {
          const rangeComponents = ['time-range', 'date-range'];
          if (rangeComponents.includes(component.type)) {
            component.bindStartTime =
              changeToPinyin(component.label, isFieldUpper.value) + random(1000, 9999);
            component.bindEndTime =
              changeToPinyin(component.label, isFieldUpper.value) + random(1000, 9999);
          } else if (!noHaveField.includes(component.type) && !component.bindField) {
            component.bindField =
              changeToPinyin(component.label, isFieldUpper.value) + random(1000, 9999);
          }
          if (
            !noHaveTableAndField.includes(component.type) &&
            (!component.isSubFormChild || component.type === 'sun-form') &&
            !component.isSingleFormChild
          ) {
            if (
              component.type === 'form' ||
              component.type === 'one-for-one' ||
              component.type === 'sun-form'
            ) {
              component.bindTable = isFieldUpper.value
                ? `${mainTableName.value}_CHILD_${random(1000, 9999)}`
                : `${mainTableName.value}_child_${random(1000, 9999)}`;
            } else if (!noHaveTableAndField.includes(component.type)) {
              component.bindTable = bindTable ? bindTable : mainTableName.value;
            }
          }
        }
        if (component.isGridChild) component.options.span = 7;
      };

      //删除引用已删除的财务组件的计算式配置
      const deleteAdoptedCalc = (index: number, list: any[]) => {
        const component = list[index];
        if (['computational', 'money-chinese'].includes(component.type)) {
          component.options.beAdoptedComponent?.map((key) => {
            getLayoutComponent(state.widgetForm.list, key);
          });
        }
      };

      const getLayoutComponent = (list, key) => {
        list?.map((item) => {
          if (['tab', 'grid', 'card'].includes(item.type)) {
            for (const child of item.layout) {
              getLayoutComponent(child.list, key);
            }
          } else if (item.type === 'table-layout') {
            for (const child of item.layout) {
              for (const list of child.list) {
                getLayoutComponent(list.children, key);
              }
            }
          } else if (item.type === 'form') {
            item.children.map((child: any) => {
              if (['computational', 'money-chinese'].includes(child.type) && child.key === key) {
                child.options.computationalConfig = [];
                child.options.computationalConfigValue = '== 请填写计算式配置 ==';
              }
            });
          } else {
            if (['computational', 'money-chinese'].includes(item.type) && item.key === key) {
              item.options.computationalConfig = [];
              item.options.computationalConfigValue = '== 请填写计算式配置 ==';
            }
          }
        });
      };

      return {
        uploadValue,
        itemLabelCol,
        handleItemClick,
        handleTableLayoutClick,
        handleCopyClick,
        handleDeleteClick,
        handleFilterOption,
        noHaveTitle,
        handleColMoveAdd,
        addBindTableAndField,
        deleteAdoptedCalc,
        t,
        updateKey,
      };
    },
  });
</script>
<style scoped lang="less">
  .widget-item-container .widget-view {
    padding: 10px;
    margin: 2px;
    margin-bottom: 10px;
    cursor: pointer;
  }

  .widget-item-container > div.active {
    background: #eef4ff;
  }

  .widget-item-container {
    position: relative;
  }

  .widget-view-action {
    position: absolute;
    top: -12px;
    right: 43px;
  }

  .widget-view-drag {
    position: absolute;
    top: -12px;
    right: 10px;
  }

  .svgicon {
    color: #0960bd;
    border: 1px solid #0960bd;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    line-height: 20px;
    display: inline-block;
    text-align: center;
    cursor: pointer;
    margin-left: 10px;
  }

  .svgicon svg {
    width: 12px !important;
    height: 12px !important;
  }

  .icon-delete {
    border: 1px solid #f64c4c;
    color: #f64c4c;
  }

  .icon-move {
    border: 1px solid #90b665;
    color: #90b665;
  }

  .radio-default {
    display: inline-block !important;
  }

  .radio-button {
    flex-wrap: wrap;
  }

  :deep(.ant-form-item-control-input-content .ant-radio-group .ant-radio-button-wrapper) {
    padding: 3px 7px !important;
    flex: auto;
  }

  :deep(.ant-form-item-label > label) {
    white-space: normal;
    display: inline;
    line-height: 28px;
    cursor: pointer;
  }

  .fc-style .widget-form-container .widget-form-list .widget-col.active {
    border: 1px solid #e6a23c !important;
  }

  .fc-style .widget-form-container .widget-form-list .widget-col:hover {
    background: #fdf6ec;
    outline: 1px solid #e6a23c;
    outline-offset: 0;
  }

  .fc-style .widget-form-container .widget-form-list .widget-col-list {
    min-height: 100px;
    border: 1px dashed #ccc;
    background: #fff;
  }

  /* @import '/@/assets/style/designer/index.css'; */
</style>
