<template>
  <div class="box-top">
    <div class="title">{{ t('隐藏组件列表') }}</div>
    <a-button type="primary" size="small" @click="addComponent">{{ t('添加组件') }}</a-button>
  </div>
  <a-form
    v-for="(item, index) in hiddenComponent"
    :key="item.key"
    :labelCol="{ span: 8 }"
    :wrapperCol="{ span: 16 }"
    style="margin-top: 20px; position: relative"
    @mouseenter="showDeleteIndex = index"
    @mouseleave="showDeleteIndex = -1"
  >
    <a-form-item :label="t('组件编码')" required>
      <a-input v-model:value="item.code" :placeholder="t('请输入组件编码')" />
    </a-form-item>
    <a-form-item :label="t('组件名称')" required>
      <a-input
        v-model:value="item.label"
        :placeholder="t('请输入组件名称')"
        @change="handleChangeLabel(item)"
      />
    </a-form-item>
    <a-form-item :label="t('组件值')" required>
      <a-input v-model:value="item.value" :placeholder="t('请输入组件值')" />
    </a-form-item>
    <template v-if="designType === 'data'">
      <a-form-item :label="t('绑定表')" required>
        <a-select
          v-model:value="item.bindTable"
          :placeholder="t('请选择数据表')"
          size="mini"
          @change="changeTable"
        >
          <a-select-option v-for="(table, idx) in mainTable" :value="table.name" :key="idx" />
        </a-select>
      </a-form-item>
      <a-form-item :label="t('绑定字段')" required>
        <a-select v-model:value="item.bindField" :placeholder="t('请选择表字段')" size="mini">
          <a-select-option v-for="(field, idx) in tableField" :value="field.name" :key="idx" />
        </a-select>
      </a-form-item>
    </template>
    <div class="icon-delete" v-show="index === showDeleteIndex">
      <SvgIcon name="delete" @click.stop="deleteHiddenComponent(index)" class="svg-delete" />
    </div>
  </a-form>
</template>

<script lang="ts" setup>
  import { ref, watch, inject, Ref } from 'vue';
  import { SvgIcon } from '/@/components/Icon';
  import { TableInfo, FieldInfo, HiddenComponentInfo } from '../types';
  import { changeToPinyin } from '/@/utils/event/design';
  import { random } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    component: {
      type: Array as PropType<HiddenComponentInfo[]>,
    },
  });

  const tableInfo = inject<Ref<TableInfo[]>>('tableInfo') as Ref<TableInfo[]>;

  const designType = inject<string>('designType');
  const isFieldUpper = inject<Ref<boolean>>('isFieldUpper', ref(false));

  const hiddenComponent = ref<HiddenComponentInfo[]>(props.component!);
  const mainTable = ref<TableInfo[]>([]);
  const tableField = ref<FieldInfo[]>([]);
  const showDeleteIndex = ref<number>(-1);
  let mainTableName;
  if (designType !== 'data') {
    mainTableName = inject<string>('mainTableName');
  }

  watch(
    () => tableInfo,
    () => {
      mainTable.value = tableInfo.value?.filter((item: any) => item.isMain); //隐藏组件只能选择主表的字段进行绑定
    },
    {
      deep: true,
      immediate: true,
    },
  );
  const addComponent = () => {
    const key = `hiddenComponent${Date.now()}`;
    const com: HiddenComponentInfo = {
      key,
      type: 'hiddenComponent',
      code: '',
      label: '',
      value: '',
      bindTable: '',
      bindField: '',
    };
    if (designType !== 'data') {
      com.bindTable = mainTableName.value!;
    }
    hiddenComponent.value.push(com);
  };

  const handleChangeLabel = (info) => {
    if (designType !== 'data') {
      info.bindField = changeToPinyin(info.label, isFieldUpper.value) + random(1000, 9999);
    }
  };
  const deleteHiddenComponent = (index: number) => {
    hiddenComponent.value.splice(index, 1);
  };
  const changeTable = (val: string) => {
    const chooseTable = mainTable.value.find((table: any) => table.name === val)!;
    tableField.value = chooseTable?.fields;
  };
</script>

<style lang="less" scoped>
  .box-top {
    display: flex;
    justify-content: space-between;
    margin: 5px 0 10px 10px;
    align-items: center;

    .title {
      font-size: 14px;
      line-height: 18px;
      padding-left: 6px;
      border-left: 6px solid #5e95ff;
    }
  }

  .icon-delete {
    position: absolute;
    top: -12px;
    right: -5px;
    width: 24px;
    text-align: center;
    border: 1px solid #f64c4c;
    color: #f64c4c;
    border-radius: 50%;
    cursor: pointer;

    .svg-delete {
      width: 13px !important;
      height: 13px !important;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 10px !important;
  }
</style>
