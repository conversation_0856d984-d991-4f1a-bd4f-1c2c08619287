<template>
  <div class="awc-containter">
    <a-form
      v-bind="layout"
      v-if="tablecell && Object.keys(tablecell).length > 0"
      :key="1"
      size="small"
    >
      <a-form-item label="类型">
        <a-input v-model:value="tablecell.class" size="mini" />
      </a-form-item>
      <a-form-item label="高度">
        <a-input-number v-model:value="tablecell.height" size="mini" />
      </a-form-item>
    </a-form>
    <a-form v-bind="layout" v-if="tableth && Object.keys(tableth).length > 0" :key="2" size="small">
      <a-form-item label="类型">
        <a-input v-model:value="tableth.class" size="mini" />
      </a-form-item>
      <a-form-item label="宽度">
        <a-input-number v-model:value="tableth.width" size="mini" />
      </a-form-item>
    </a-form>
    <a-form v-bind="layout" v-if="showData" :key="3" size="small">
      <a-form-item :label="t('标题')">
        <a-input v-model:value="data.label" size="mini" />
      </a-form-item>
      <a-form-item label="边框宽度">
        <a-input-number v-model:value="data.options.borderWidth" size="mini" />
      </a-form-item>
      <a-form-item label="边框颜色">
        <ColorPicker v-model:value="data.options.borderColor" />
      </a-form-item>
      <a-form-item label="自定义类">
        <a-input v-model:value="data.options.class" size="mini" />
      </a-form-item>
      <a-form-item :label="t('是否显示')">
        <a-switch v-model:checked="data.options.isShow" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts">
  import { defineComponent, inject, ref, Ref, watch } from 'vue';

  import Draggable from 'vuedraggable';
  import { SvgIcon, IconPicker, Icon } from '/@/components/Icon';

  import { ColorPicker } from '/@/components/ColorPicker';

  import { useI18n } from '/@/hooks/web/useI18n';
  import { TableCell, TableTh } from '../../types';

  const { t } = useI18n();
  export default defineComponent({
    name: 'PropertyOption',
    components: {
      Draggable,
      SvgIcon,
      IconPicker,
      Icon,
      ColorPicker,
    },
    emits: ['update:select'],
    props: {
      //所选组件配置
      select: {
        type: Object,
      },
      widgetForm: {
        type: Object,
      },
    },
    setup(props, context) {
      const showData = ref(false);
      const data = ref<any>();
      const tablecell = inject<Ref<TableCell>>('tableCell');
      const tableth = inject<Ref<TableTh>>('tableTh');
      const layout = {
        labelCol: { span: 8 },
        wrapperCol: { span: 16 },
      };
      watch(
        () => props.select,
        (val) => {
          data.value = val;
          if (
            (tableth?.value && Object.keys(tableth.value).length > 0) ||
            (tablecell?.value && Object.keys(tablecell.value).length > 0)
          ) {
            showData.value = false;
          } else {
            showData.value = true;
          }
        },
        {
          deep: true,
          immediate: true,
        },
      );
      watch(
        () => tablecell?.value,
        (val) => {
          if (val && Object.keys(val).length > 0) {
            showData.value = false;
          } else if (tableth?.value && Object.keys(tableth.value).length <= 0) {
            showData.value = true;
          }
        },
        {
          deep: true,
          immediate: true,
        },
      );
      watch(
        () => tableth?.value,
        (val) => {
          if (val && Object.keys(val).length > 0) {
            showData.value = false;
          } else if (tablecell?.value && Object.keys(tablecell.value).length <= 0) {
            showData.value = true;
          }
        },
        {
          deep: true,
          immediate: true,
        },
      );
      watch(
        data,
        (val) => {
          context.emit('update:select', val);
        },
        {
          immediate: true,
          deep: true,
        },
      );

      return {
        layout,
        tablecell,
        tableth,
        showData,
        data,
        t,
      };
    },
  });
</script>
<style scoped lang="less">
  *,
  :deep(.ant-input) {
    font-size: 12px !important;
  }

  :deep(.ant-row) {
    align-items: center;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
