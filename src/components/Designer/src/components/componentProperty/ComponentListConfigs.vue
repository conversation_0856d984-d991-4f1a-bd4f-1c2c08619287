<template>
  <div>
    <a-row type="flex" align="middle">
      <a-col :offset="1" :span="8">
        <span>{{ t('列表样式配置：') }}</span>
      </a-col>
      <a-col :span="14">
        <a-input
          :placeholder="t('点击配置列表样式')"
          :value="props.select.options.listStyle ? t('已配置') : t('点击配置列表样式')"
          @click="showConfigModal()"
        >
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </a-col>
    </a-row>
  </div>
  <ListSettingConfig @register="registerModal" @success="submitConfig" :disabled="disabled" />
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import ListSettingConfig from './settings/ListSettingConfig.vue';
  import { noBlurComponentEvent, noChangeComponentEvent, noClickComponentEvent } from '../../types';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    select: { type: Object, default: {} },
    widgetForm: { type: Object, default: {} },
    disabled: { type: Boolean, default: false },
  });
  const [registerModal, { openModal }] = useModal();

  const showConfigModal = (val) => {
    openModal(true, {
      content: props.select.options.listStyle,
      list: props.widgetForm?.list,
    });
  };

  const configTxt = (type) => {
    return props.select.options.events[type] ? t('已配置') : '';
  };
  const submitConfig = (value) => {
    console.log('value: ', value);
    console.log('props.select.options: ', props.select.options.listStyle);
    props.select.options.listStyle = value;
  };
</script>

<style lang="less" scoped>
  .event-box {
    margin-bottom: 25px;
  }

  .event-title {
    margin: 5px 0 10px 10px;
    font-size: 14px;
    line-height: 16px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }
</style>
