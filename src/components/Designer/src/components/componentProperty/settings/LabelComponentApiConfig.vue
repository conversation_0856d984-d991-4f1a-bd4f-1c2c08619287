<template>
  <div>
    <a-input
      :value="state?.apiConfig?.path ? t('已配置') : t('点击进行接口配置')"
      :placeholder="t('点击进行接口配置')"
      @click="handleOpen()"
    >
      <template #suffix>
        <Icon icon="ant-design:ellipsis-outlined" />
      </template>
    </a-input>
    <ApiConfig
      v-if="state.visible"
      :isLabelComponent="true"
      v-model:apiConfigDialog="state.visible"
      :apiConfig="state.apiConfig"
      @update:apiConfig="changeApiConfig"
      :title="t('API配置')"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { ApiConfig } from '/@/components/ApiConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    value: {
      type: Object,
      default: () => {
        return {
          path: 'CodeGeneration/selection',
          method: 'GET',
          apiId: '93d735dcb7364a0f8102188ec4d77ac7',
        };
      },
    },
  });
  const state = reactive({
    visible: false,
    apiConfig: {
      path: 'CodeGeneration/selection',
      method: 'GET',
      apiId: '93d735dcb7364a0f8102188ec4d77ac7',
    } as { path: string; method: string; apiId: string },
  });
  const emits = defineEmits(['update:value']);
  onMounted(() => {
    if (props.value) {
      state.apiConfig = props.value;
    }
  });

  function handleOpen() {
    state.visible = true;
  }
  function changeApiConfig(val) {
    state.apiConfig.path = val.path;
    state.apiConfig.method = val.method;
    state.apiConfig.apiId = val.apiId;
    emits('update:value', state.apiConfig);
  }
</script>

<style lang="less" scoped>
  .loop-box {
    display: flex;
    align-items: center;
    height: 40px;

    .title {
      color: #000;
      font-size: 12px;
    }

    .desc {
      color: #ccc;
      font-size: 12px;
      margin-left: 6px;
    }
  }
</style>
