<template>
  <div>
    <a-input
      :value="props.data?.options?.styleConfig ? t('已配置') : t('点击进行样式配置')"
      :placeholder="t('点击进行样式配置')"
      @click="handleOpen()"
    >
      <template #suffix>
        <Icon icon="ant-design:ellipsis-outlined" />
      </template>
    </a-input>
    <a-modal
      :width="1000"
      v-model:visible="state.visible"
      title="样式配置"
      :maskClosable="false"
      @ok="handleSubmit"
      @cancel="handleClose"
      :bodyStyle="{ padding: '0 10px 10px' }"
    >
      <div class="list-title">{{ t('样式列表') }}</div>

      <a-table
        :dataSource="state.dataSource"
        :columns="columns"
        :pagination="false"
        :scroll="{ y: '400px' }"
        >、
        <template #bodyCell="{ column, record }">
          <template
            v-if="
              column.key === 'bgColor' || column.key === 'fontColor' || column.key === 'borderColor'
            "
          >
            <ColorPicker v-model:value="record[column.key]" />
          </template>
          <template v-if="column.key === 'borderWidth'">
            <a-input-number v-model:value="record[column.key]" addonAfter="px" />
          </template>
        </template>
      </a-table>
      <a-button @click="handleCreate" class="ant-btn ant-btn-dashed ant-btn-block">
        <template #icon><PlusOutlined /></template>
        {{ t('新增') }}
      </a-button>
      <div class="loop-box">
        <div class="title">样式循环：</div>
        <a-switch v-model:checked="state.checked" />
        <div class="desc"
          >备注：样式循环是指在超出样式配置部分的标签，会从第一个样式开始循环展示，如果不开启样式循环，未配置样式的标签则按基础样式显示。</div
        >
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, reactive } from 'vue';
  import { ColorPicker } from '/@/components/ColorPicker';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Icon } from '/@/components/Icon';
  import { PlusOutlined } from '@ant-design/icons-vue';
  const { t } = useI18n();
  const columns = [
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 50,
    },
    {
      title: t('背景色'),
      dataIndex: 'bgColor',
      key: 'bgColor',
      align: 'center',
      width: 200,
    },
    {
      title: t('字体颜色'),
      dataIndex: 'fontColor',
      key: 'fontColor',
      align: 'center',
    },
    {
      title: t('边框大小'),
      dataIndex: 'borderWidth',
      key: 'borderWidth',
      align: 'center',
      width: 200,
    },
    {
      title: t('边框颜色'),
      dataIndex: 'borderColor',
      key: 'borderColor',
      align: 'center',
      width: 200,
    },
  ];
  const defaultBgColor = '#fafafa';
  const defaultFontColor = '#000000';
  const defaultBorderWidth = 1;
  const defaultBorderColor = '#d9d9d9';
  const props = defineProps({
    styleConfig: {
      type: Object,
      default: () => {
        return {
          colors: [], //颜色配置
          isLoop: true, //是否循环选择样式
        };
      },
    },
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const emits = defineEmits(['update:value']);
  onMounted(() => {
    if (props.data?.options?.styleConfig) {
      if (
        Array.isArray(props.data.options.styleConfig.colors) &&
        props.data.options.styleConfig.colors.length > 0
      ) {
        state.dataSource = props.data.options.styleConfig.colors;
      }
      state.checked = props.data.options.styleConfig.isLoop;
    }
  });
  const state = reactive({
    visible: false,
    dataSource: [] as Array<{
      bgColor: string;
      fontColor: string;
      borderWidth: Number;
      borderColor: string;
    }>,
    checked: false,
  });
  function emitValue() {
    emits('update:value', {
      colors: state.dataSource,
      isLoop: state.checked,
    });
  }
  function handleCreate() {
    state.dataSource.push({
      bgColor: defaultBgColor,
      fontColor: defaultFontColor,
      borderWidth: defaultBorderWidth,
      borderColor: defaultBorderColor,
    });
  }
  function handleSubmit() {
    emitValue();
    handleClose();
  }
  function handleClose() {
    state.visible = false;
  }
  function handleOpen() {
    state.visible = true;
  }
</script>

<style lang="less" scoped>
  .loop-box {
    display: flex;
    align-items: center;
    height: 40px;

    .title {
      color: #000;
      font-size: 12px;
    }

    .desc {
      color: #ccc;
      font-size: 12px;
      margin-left: 6px;
    }
  }
</style>
