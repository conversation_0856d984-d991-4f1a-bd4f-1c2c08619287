<template>
  <div v-for="(item, index) in render" :key="item.type">
    <div :class="index == render.length - 1 ? 'mb-0' : 'event-box'" v-if="isShowEvent(item.name)">
      <div class="event-title">{{ showTitle(item.name) }}</div>
      <div>
        <a-row type="flex" align="middle">
          <a-col :offset="1" :span="6">
            <span>{{ t('配置脚本：') }}</span>
          </a-col>
          <a-col :span="17">
            <a-input
              :placeholder="t('点击配置脚本')"
              :value="configTxt(item.type)"
              @click="showConfigModal(item.type)"
            >
              <template #suffix>
                <Icon icon="ant-design:ellipsis-outlined" />
              </template>
            </a-input>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
  <ScriptConfig @register="registerModal" @success="submitConfig" :disabled="disabled" />
</template>

<script lang="ts" setup>
  import { Icon } from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import ScriptConfig from './settings/ScriptConfig.vue';
  import {
    noBlurComponentEvent,
    noChangeComponentEvent,
    noClickComponentEvent,
    noBlurMobileComponentEvent,
    noHaveTableAndField,
  } from '../../types';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    select: { type: Object, default: {} },
    widgetForm: { type: Object, default: {} },
    disabled: { type: Boolean, default: false },
  });
  const render = [
    {
      name: t('失焦事件'),
      type: 'blur',
    },
    {
      name: t('点击事件'),
      type: 'click',
    },
    {
      name: t('值改变事件'),
      type: 'change',
    },
  ];
  const [registerModal, { openModal }] = useModal();

  const getMainTableComponents = (list, mainTableComponent) => {
    const rangeComponents = ['time-range', 'date-range'];
    list.forEach((item) => {
      if (['tab', 'grid', 'card'].includes(item.type)) {
        for (const child of item.layout!) {
          getMainTableComponents(child.list, mainTableComponent);
        }
      } else if (rangeComponents.includes(item.type)) {
        //时间范围、日期范围需改为两个字段
        mainTableComponent.push({
          label: t(`{name}开始时间`, { name: item.label }),
          bindField: item.bindStartTime,
        });
        mainTableComponent.push({
          label: t(`{name}结束时间`, { name: item.label }),
          bindField: item.bindEndTime,
        });
      } else if (!noHaveTableAndField.includes(item.type)) {
        //除去不绑定表和字段的组件 以及表格组件
        mainTableComponent.push(item);
      }
    });
  };

  const showTitle = (name) => {
    const type = props.select?.type;
    switch (name) {
      case t('失焦事件'):
        if (noBlurMobileComponentEvent.includes(type)) {
          return t('失焦事件' + '（移动端不支持）');
        } else {
          return t('失焦事件');
        }
      case t('点击事件'):
        return t('点击事件');
      case t('值改变事件'):
        return t('值改变事件');
    }
  };
  const isShowEvent = (name) => {
    const type = props.select?.type;
    switch (name) {
      case t('失焦事件'):
        return !noBlurComponentEvent.includes(type);
      case t('点击事件'):
        return !noClickComponentEvent.includes(type);
      case t('值改变事件'):
        return !noChangeComponentEvent.includes(type);
    }
  };
  const showConfigModal = (val) => {
    openModal(true, {
      type: val,
      content: props.select?.options.events[val] || '',
      formList: props.widgetForm,
    });
  };

  const configTxt = (type) => {
    return props.select.options.events[type] ? t('已配置') : '';
  };
  const submitConfig = (type, value) => {
    if (props.select?.options.events) {
      props.select.options.events[type] = value;
    } else {
      props.select.options.events = {
        [type]: value,
      };
    }
  };
</script>

<style lang="less" scoped>
  .event-box {
    margin-bottom: 25px;
  }

  .event-title {
    margin: 5px 0 10px 10px;
    font-size: 14px;
    line-height: 16px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }
</style>
