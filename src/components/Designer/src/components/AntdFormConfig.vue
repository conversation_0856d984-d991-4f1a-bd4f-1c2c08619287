<template>
  <div class="form-config-container">
    <a-form v-bind="layout">
      <a-form-item :label="t('表单形式')">
        <a-radio-group v-model:value="data.formType" button-style="solid" size="small">
          <a-radio-button value="modal">{{ t('弹窗') }}</a-radio-button>
          <a-radio-button value="drawer">{{ t('抽屉') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item :label="t('表单布局')">
        <a-radio-group v-model:value="data.layout" button-style="solid" size="small">
          <a-radio-button value="horizontal">{{ t('水平排列') }}</a-radio-button>
          <a-radio-button value="vertical">{{ t('垂直排列') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item :label="t('标签对齐')">
        <a-radio-group v-model:value="data.labelAlign" button-style="solid" size="small">
          <a-radio-button value="left">{{ t('左对齐') }}</a-radio-button>
          <a-radio-button value="right">{{ t('右对齐') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="标签宽度">
        <a-input-number v-model:value="data.labelCol.span" :min="0" :max="24" addonAfter="/ 24" />
      </a-form-item>

      <a-form-item label="标签边距">
        <a-input-number v-model:value="data.labelCol.offset" :min="0" :max="24" addonAfter="/ 24" />
      </a-form-item>

      <a-form-item :label="t('组件尺寸')">
        <a-radio-group v-model:value="data.size" button-style="solid" size="small">
          <a-radio-button value="default">{{ t('默认') }}</a-radio-button>
          <a-radio-button value="large">{{ t('大') }}</a-radio-button>
          <a-radio-button value="small">{{ t('小') }}</a-radio-button>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="表单宽度">
        <a-input-number v-model:value="data.formWidth" addonAfter="px" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts">
  import { WidgetForm } from '../types';
  import { defineComponent, PropType, ref, watch } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export default defineComponent({
    name: 'AntdFormConfig',
    props: {
      config: {
        type: Object as PropType<WidgetForm['config']>,
        required: true,
      },
    },
    emits: ['update:config'],
    setup(props, context) {
      const data = ref(props.config);
      const layout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
      };
      watch(data, () => context.emit('update:config', data));

      return {
        data,
        layout,
        t,
      };
    },
  });
</script>
<style scoped lang="less">
  .fc-style .widget-config-container .config-content .ant-form-item,
  .fc-style .widget-config-container .config-content .el-form-item,
  .fc-style .widget-config-container .config-content h4 {
    padding-bottom: 12px;
    border-bottom: 1px dashed #e1e1e1;
    margin-bottom: 16px;
  }

  .form-config-container {
    padding: 10px;
  }

  :deep(.ant-input-number) {
    width: 100%;
  }

  // :deep(.ant-radio-group) {
  //   display: flex;

  //   .ant-radio-button-wrapper {
  //     flex: 1;
  //     text-align: center;
  //     padding: 3px 0 !important;
  //   }
  // }
  // @import '/@/assets/style/designer/index.css';
</style>
