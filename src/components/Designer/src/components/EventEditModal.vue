<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit">
    <div>
      <CodeEditor v-model:value="defaultValue" language="js" />
    </div>
    <div>
      <CodeEditor :value="tipContent" language="js" readonly />
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { computed, ref, unref } from 'vue';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { BasicModal, useModalInner } from '/@/components/Modal';

  const emit = defineEmits(['success', 'register']);

  const eventType = ref('');

  const tipContent = computed(() => {
    return `
      //事件方法参数 一共有3个
      {
        schema: FormSchema;  //当前表单组件的schema
        formActionType: FormActionType; // 操作当前表单的各种方法
        formModel: Recordable<any>; //当前表单数据对象
      }
      //例子
      function(){
        if(formModel.numOne === 1){
          formModel.sum = formModel.numOne + formModel.numTwo;
        }
        else{
          formModel.sum = formModel.numOne * formModel.numTwo;
        }
      }

      `;
  });

  const defaultValue = ref(``);

  const title = computed(() => {
    return `编辑${eventType.value}事件`;
  });

  const [registerModal, { closeModal }] = useModalInner(async (data) => {
    eventType.value = data.type;
    //如果已经设置过当前事件代码  则显示已经设置的值 反之  显示默认值
    if (data.content) {
      defaultValue.value = data.content;
    } else {
      defaultValue.value = `
console.log(schema,formModel,formActionType);
`;
    }
  });

  async function handleSubmit() {
    emit('success', unref(eventType), unref(defaultValue));
    closeModal();
  }
</script>
