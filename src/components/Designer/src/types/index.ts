import { useI18n } from '/@/hooks/web/useI18n';
import { useAppStore } from '/@/store/modules/app';
const appStore = useAppStore();
const { t } = useI18n();
export interface WidgetForm {
  list: any[];
  config: {
    formType: string;
    size: string;
    // hideRequiredMark: boolean;
    layout: string;
    labelAlign: string;
    labelCol: {
      span: number;
      offset: number;
    };
    formWidth: number;
  };
  hiddenComponent: HiddenComponentInfo[];
}

export const widgetForm: WidgetForm = {
  list: [],
  config: {
    formType: 'modal',
    size: 'default',
    layout: 'horizontal',
    labelAlign: 'right',
    labelCol: {
      span: 3,
      offset: 0,
    },
    formWidth: 900,
  },
  hiddenComponent: [],
};

export const basicComponents = [
  {
    label: t('单行文本'),
    typeName: t('单行文本'),
    type: 'input',
    options: {
      width: '100%',
      span: '',
      defaultValue: '',
      placeholder: t('请输入单行文本'),
      maxlength: null,
      prefix: '',
      suffix: '',
      addonBefore: '',
      addonAfter: '',
      disabled: false,
      allowClear: false,
      showLabel: true,
      required: false,
      rules: [],
      events: {},
      listStyle: '', //配置列表样式
      isSave: false,
      isShow: true,
      scan: false,
    },
  },
  {
    label: t('多行文本'),
    typeName: t('多行文本'),
    type: 'textarea',
    options: {
      width: '100%',
      span: '',
      defaultValue: '',
      placeholder: t('请输入多行文本'),
      maxlength: null,
      rows: 4,
      autoSize: false,
      showCount: false,
      disabled: false,
      showLabel: true,
      allowClear: false,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('密码框'),
    typeName: t('密码框'),
    type: 'password',
    options: {
      width: '100%',
      span: '',
      defaultValue: '',
      placeholder: t('请输入密码框'),
      maxlength: null,
      prefix: '',
      addonBefore: '',
      addonAfter: '',
      visibilityToggle: true,
      disabled: false,
      allowClear: false,
      showLabel: true,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('计数组件'),
    typeName: t('计数组件'),
    type: 'number',
    options: {
      width: '100%',
      span: '',
      defaultValue: 0,
      min: 0,
      max: 100,
      step: 1,
      maxlength: null,
      disabled: false,
      showLabel: true,
      controls: true,
      required: false,
      subTotal: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('编辑器'),
    typeName: t('编辑器'),
    type: 'richtext-editor',
    options: {
      span: '',
      defaultValue: '',
      width: '100%',
      disabled: false,
      showLabel: true,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('编码组件'),
    typeName: t('编码组件'),
    type: 'auto-code',
    options: {
      width: '100%',
      span: '',
      placeholder: t('请输入编码组件'),
      prefix: '',
      suffix: '',
      addonBefore: '',
      addonAfter: '',
      showLabel: true,
      autoCodeRule: null,
      required: false,
      isShow: true,
    },
  },
  {
    label: t('自动完成'),
    typeName: t('自动完成'),
    type: 'auto-complete',
    options: {
      width: '100%',
      showLabel: true,
      placeholder: t('请输入自动完成'),
      autofocus: false,
      backfill: false,
      required: false,
      disabled: false,
      bordered: true,
      defaultActiveFirstOption: true,
      defaultOpen: true,
      staticOptions: [
        {
          key: 1,
          label: 'Option 1',
          value: 'Option 1',
        },
        {
          key: 2,
          label: 'Option 2',
          value: 'Option 2',
        },
        {
          key: 3,
          label: 'Option 3',
          value: 'Option 3',
        },
      ],
      defaultSelect: '', //静态数据默认选择
      datasourceType: 'api',
      params: null,
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {},
      itemId: undefined,
      dicOptions: [],
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: '手写签名',
    typeName: '手写签名',
    type: 'signature',
    options: {
      width: '100%',
      span: '',
      associateComponents: [],
      defaultValue: '',
      prefix: '',
      suffix: '',
      addonBefore: '',
      addonAfter: '',
      isCallSign: false,
      disabled: false,
      showLabel: true,
      required: false,
      isShow: true,
    },
  },
  {
    label: t('标签组件'),
    typeName: t('标签组件'),
    type: 'labelComponent',
    options: {
      width: '100%',
      span: '',
      defaultValue: '',
      placeholder: t('请输入标签，回车完成标签填写'),
      disabled: false,
      showLabel: true,
      required: false,
      isShow: true,

      styleConfig: {
        colors: [], //颜色配置
        isLoop: true, //是否循环选择样式
      },
      datasourceType: 'dic', // static 客户填写 api API获取 dic 数据字典获取
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: '1718831555510091777',
      rules: [],
      events: {},
    },
  },
];

export const advanceComponents = [
  {
    label: t('下拉选择框'),
    typeName: t('下拉选择框'),
    type: 'select',
    options: {
      width: '100%',
      span: '',
      placeholder: t('请选择下拉选择'),
      showLabel: true,
      showSearch: false,
      isMultiple: false,
      clearable: false,
      disabled: false,
      staticOptions: [
        {
          key: 1,
          label: 'Option 1',
          value: 'Option 1',
        },
        {
          key: 2,
          label: 'Option 2',
          value: 'Option 2',
        },
        {
          key: 3,
          label: 'Option 3',
          value: 'Option 3',
        },
      ],
      defaultSelect: '', //静态数据默认选择
      datasourceType: 'api',
      params: null,
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('树选择'),
    typeName: t('树选择'),
    type: 'tree-select-component',
    options: {
      span: '',
      defaultValue: '',
      showLabel: true,
      isShow: true,
      events: {},
      rules: [],
      treeConfig: {
        id: '',
        isMultiple: true,
        height: 100,
      },
    },
  },
  {
    label: t('级联选择器'),
    typeName: t('级联选择器'),
    type: 'cascader',
    options: {
      width: '100%',
      span: '',
      placeholder: t('请选择级联选择器'),
      showFormat: 'all',
      separator: '/',
      selectedConfig: 'selectMostChildLevel',
      disabled: false,
      allowClear: false,
      showLabel: true,
      apiConfig: {
        path: 'CodeGeneration/cascade',
        method: 'GET',
        apiId: '0772d128d20f4f80aaafc784adba338a',
      },
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('联想下拉'),
    typeName: t('联想下拉'),
    type: 'associate-select',
    options: {
      width: '100%',
      span: '',
      placeholder: t('请选择联想下拉'),
      showLabel: true,
      showSearch: false,
      disabled: false,
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
        outputParams: [
          {
            name: 'label',
            bindField: '',
          },
          {
            name: 'value',
            bindField: '',
          },
        ],
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('联想弹层'),
    typeName: t('联想弹层'),
    type: 'associate-popup',
    options: {
      popupType: 'associate',
      width: '100%',
      span: '',
      placeholder: t('请选择联想弹层'),
      showLabel: true,
      disabled: false,
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
        outputParams: [
          {
            name: 'label',
            tableTitle: 'label',
            bindField: '',
            show: true,
            width: 150,
          },
          {
            name: 'value',
            tableTitle: 'value',
            bindField: '',
            show: true,
            width: 150,
          },
        ],
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('多选弹层'),
    typeName: t('多选弹层'),
    type: 'multiple-popup',
    options: {
      popupType: 'multiple',
      width: '100%',
      span: '',
      defaultValue: '',
      placeholder: t('请选择多选弹层'),
      showLabel: true,
      disabled: false,
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
        outputParams: [
          {
            name: 'label',
            tableTitle: 'label',
            show: true,
            width: 150,
          },
          {
            name: 'value',
            tableTitle: 'value',
            show: true,
            width: 150,
          },
        ],
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('行政区域'),
    typeName: t('行政区域'),
    type: 'area',
    options: {
      width: '100%',
      span: '',
      placeholder: t('请选择行政区域'),
      showLabel: true,
      disabled: false,
      allowClear: false,
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('多选组件'),
    typeName: t('多选组件'),
    type: 'checkbox',
    options: {
      span: '',
      showLabel: true,
      disabled: false,
      staticOptions: [
        {
          key: 1,
          label: 'option 1',
          value: 'option 1',
        },
        {
          key: 2,
          label: 'option 2',
          value: 'option 2',
        },
        {
          key: 3,
          label: 'option 3',
          value: 'option 3',
        },
      ],
      datasourceType: 'api',
      defaultSelect: '',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('单选组件'),
    typeName: t('单选组件'),
    type: 'radio',
    options: {
      span: '',
      showLabel: true,
      disabled: false,
      optionType: 'default',
      staticOptions: [
        {
          key: 1,
          label: 'option 1',
          value: 'option 1',
        },
        {
          key: 2,
          label: 'option 2',
          value: 'option 2',
        },
        {
          key: 3,
          label: 'option 3',
          value: 'option 3',
        },
      ],
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      defaultSelect: '',
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('开关组件'),
    typeName: t('开关组件'),
    type: 'switch',
    options: {
      span: '',
      defaultValue: 1,
      checkedChildren: '',
      unCheckedChildren: '',
      checkedColor: appStore.getProjectConfig.themeColor,
      unCheckedColor: '#bbbdbf',
      showLabel: true,
      disabled: false,
      events: {},
      isShow: true,
    },
  },
  {
    label: t('滑块组件'),
    typeName: t('滑块组件'),
    type: 'slider',
    options: {
      span: '',
      defaultValue: 0,
      min: 0,
      max: 100,
      step: 1,
      showLabel: true,
      disabled: false,
      reverse: false,
      events: {},
      isShow: true,
    },
  },
  {
    label: t('时间选择'),
    typeName: t('时间选择'),
    type: 'time',
    options: {
      span: '',
      defaultValue: '',
      width: '100%',
      placeholder: t('请选择时间选择'),
      format: 'HH:mm:ss',
      showLabel: true,
      allowClear: true,
      disabled: false,
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('时间范围'),
    typeName: t('时间范围'),
    type: 'time-range',
    options: {
      span: '',
      width: '100%',
      showTime: false,
      format: 'HH:mm:ss',
      startTimePlaceholder: t('请选择开始时间'),
      endTimePlaceholder: t('请选择结束时间'),
      showLabel: true,
      disabled: false,
      allowClear: true,
      required: false,
      rules: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('日期选择'),
    typeName: t('日期选择'),
    type: 'date',
    options: {
      span: '',
      defaultValue: '',
      width: '100%',
      placeholder: t('请选择日期选择'),
      format: 'YYYY-MM-DD HH:mm:ss',
      showLabel: true,
      allowClear: true,
      disabled: false,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('日期范围'),
    typeName: t('日期范围'),
    type: 'date-range',
    options: {
      span: '',
      defaultValue: [],
      width: '100%',
      showTime: false,
      format: 'YYYY-MM-DD',
      startTimePlaceholder: t('请选择开始日期'),
      endTimePlaceholder: t('请选择结束日期'),
      showLabel: true,
      disabled: false,
      allowClear: true,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
  {
    label: t('评分组件'),
    typeName: t('评分组件'),
    type: 'rate',
    options: {
      span: '',
      defaultValue: 0,
      count: 5,
      showLabel: true,
      allowHalf: false,
      disabled: false,
      isShow: true,
      events: {},
    },
  },
  {
    label: t('颜色选择'),
    typeName: t('颜色选择'),
    type: 'picker-color',
    options: {
      span: '',
      defaultValue: '#000000',
      showLabel: true,
      disabled: false,
      isShow: true,
      events: {},
    },
  },
  {
    label: t('上传组件'),
    typeName: t('上传组件'),
    type: 'upload',
    options: {
      span: '',
      defaultValue: [],
      // name: 'file',
      accept: '',
      maxNumber: 5,
      maxSize: 5,
      showLabel: true,
      multiple: false,
      disabled: false,
      required: false,
      isShow: true,
      events: {},
      listType: 'text',
    },
  },
  {
    label: t('图片组件'),
    typeName: t('图片组件'),
    type: 'image',
    options: {
      span: '',
      defaultValue: '',
      showLabel: true,
      isShow: true,
      isUpload: true,
      events: {},
    },
  },
  {
    label: t('地图选择'),
    typeName: t('地图选择'),
    type: 'map',
    options: {
      span: '',
      width: '100%',
      defaultValue: '',
      placeholder: t('请选择地址'),
      prefix: '',
      suffix: 'mdi:map-marker-radius-outline',
      showLabel: true,
      disabled: false,
      required: false,
      isShow: true,
      address: '',
      latiAndLong: '',
      rules: [],
      events: {},
    },
  },
  {
    label: t('二维码组件'),
    typeName: t('二维码组件'),
    type: 'qrcode',
    options: {
      span: '',
      defaultValue: ' ',
      showLabel: true,
      isShow: true,
      codeType: 'api',
      apiConfig: {
        path: 'CodeGeneration/qr-code',
        method: 'GET',
        apiId: '8aad311cb2b248b39e9227d4bad94c9b',
      },
    },
  },
  {
    label: t('树形组件'),
    typeName: t('树形组件'),
    type: 'tree-component',
    options: {
      span: '',
      defaultValue: '',
      showLabel: true,
      isShow: true,
      events: {},
      rules: [],
      treeConfig: {
        id: '',
        isMultiple: true,
        height: 100,
      },
    },
  },
  {
    label: t('一维码组件'),
    typeName: t('一维码组件'),
    type: 'barcode',
    options: {
      span: '',
      defaultValue: ' ',
      showLabel: true,
      isShow: true,
      displayValue: true,
      codeType: 'api',
      format: 'CODE128',
      apiConfig: {
        path: 'CodeGeneration/qr-code',
        method: 'GET',
        apiId: '8aad311cb2b248b39e9227d4bad94c9b',
      },
    },
  },
  {
    label: t('附件组件'),
    typeName: t('附件组件'),
    type: 'file',
    options: {
      span: '',
      defaultValue: '',
      showLabel: true,
      isShow: true,
      events: {},
    },
  },
];
//按钮组件
export const buttonComponents = [
  {
    label: t('普通按钮'),
    typeName: t('普通按钮'),
    type: 'button',
    options: {
      name: t('普通按钮'),
      buttonWidth: '',
      buttonHeight: '',
      prefix: '',
      suffix: '',
      disabled: false,
      isShow: true,
      events: {},
    },
  },
  {
    label: t('填表按钮'),
    typeName: t('填表按钮'),
    type: 'button',
    options: {
      name: t('填表按钮'),
      buttonWidth: '',
      buttonHeight: '',
      prefix: '',
      suffix: '',
      disabled: false,
      isSpecial: true,
      buttonType: 2,
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
        outputParams: [
          {
            name: 'label',
            tableTitle: 'label',
          },
          {
            name: 'value',
            tableTitle: 'value',
          },
        ],
      },
      itemId: undefined,
      dicOptions: [],
      selectTable: null,
      tableColumns: [],
      events: {},
      isShow: true,
    },
  },
  {
    label: t('弹层按钮'),
    typeName: t('弹层按钮'),
    type: 'button',
    options: {
      name: t('弹层按钮'),
      buttonWidth: '',
      buttonHeight: '',
      prefix: '',
      suffix: '',
      disabled: false,
      isSpecial: true,
      buttonType: 1,
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
        outputParams: [
          {
            name: 'label',
            tableTitle: 'label',
            bindField: '',
            show: true,
            width: 150,
          },
          {
            name: 'value',
            tableTitle: 'value',
            bindField: '',
            show: true,
            width: 150,
          },
        ],
      },
      itemId: undefined,
      dicOptions: [],
      events: {},
      isShow: true,
    },
  },
];
// 布局组件
export const layoutComponents = [
  {
    label: t('表格组件'),
    typeName: t('表格组件'),
    type: 'form',
    children: [],
    options: {
      span: '24',
      preloadType: 'api',
      apiConfig: {},
      itemId: '',
      dicOptions: [],
      useSelectButton: false,
      buttonName: t('选择数据'),
      showLabel: true,
      showComponentBorder: true,
      showFormBorder: true,
      showIndex: false,
      isShow: true,
      multipleHeads: [],
      isExport: false,
      isImport: false,
      isListView: false,
      viewList: [],
    },
  },
  {
    label: t('次级表格组件'),
    typeName: t('次级表格组件'),
    type: 'sun-form',
    children: [],
    options: {
      showLabel: true,
      showComponentBorder: true,
      showFormBorder: true,
      showIndex: false,
    },
  },
  {
    label: t('单表组件'),
    typeName: t('单表组件'),
    type: 'one-for-one',
    children: [],
    options: {
      span: '24',
      showLabel: true, // 显示标题文本或按钮名称/描述/选择按钮/显示位置/显示组件边
      isShow: true,
    },
  },
  {
    label: '表格视图组件',
    typeName: '表格视图组件',
    type: 'form-view',
    options: {
      preloadType: 'api',
      apiConfig: {
        path: 'CodeGeneration/assoc-selection',
        method: 'GET',
        apiId: 'ac61f579074c465aaf017d64284a44a3',
      },
      itemId: '',
      span: '',
      isPagination: true,
      showLabel: true,
      showFormBorder: false,
      showIndex: false,
      isShow: true,
    },
  },
  {
    label: t('表格布局'),
    typeName: t('表格布局'),
    type: 'table-layout',
    layout: [
      {
        list: [
          {
            width: '',
            height: '',
            class: 'tableLayoutTd',
            children: [],
            position: [0, 0],
          },
          {
            width: '',
            height: '',
            class: 'tableLayoutTd',
            children: [],
            position: [0, 1],
          },
        ],
      },
    ],
    options: {
      borderWidth: 1,
      borderColor: '#ddd',
      class: '',
      isShow: true,
    },
  },
  {
    label: t('选项卡组件'),
    typeName: t('选项卡组件'),
    type: 'tab',
    layout: [
      {
        name: 'tab1',
        span: 24,
        list: [],
      },
      {
        name: 'tab2',
        span: 24,
        list: [],
      },
      {
        name: 'tab3',
        span: 24,
        list: [],
      },
    ],
    options: {
      tabPosition: 'top',
      type: 'line',
      isShow: true,
    },
  },
  {
    label: t('卡片布局'),
    typeName: t('卡片布局'),
    type: 'card',
    icon: 'ant-design:credit-card-twotone',
    layout: [
      {
        name: 'card',
        span: 24,
        list: [],
      },
    ],
    options: {
      title: '卡片',
      isShow: true,
    },
  },
  {
    label: t('栅格布局'),
    typeName: t('栅格布局'),
    type: 'grid',
    layout: [
      {
        span: 12,
        list: [],
      },
      {
        span: 12,
        list: [],
      },
    ],
    options: {
      gutter: 16,
      justify: 'start',
      align: 'top',
      isShow: true,
    },
  },
  {
    label: 'iframe组件',
    typeName: 'iframe组件',
    type: 'iframe',
    options: {
      url: '',
      showLabel: true,
      isShow: true,
      list: [
        {
          name: '@id',
          value: '',
        },
      ],
    },
  },
  {
    label: t('标题'),
    typeName: t('标题'),
    type: 'title',
    options: {
      defaultValue: t('标题'),
      color: '',
      align: 'center',
      fontSize: 18,
      isShow: true,
    },
  },
  {
    label: t('文本'),
    typeName: t('文本'),
    type: 'text',
    options: {
      defaultValue: t('文本'),
      color: '',
      align: 'left',
      fontSize: 12,
      fontWeight: 'normal',
      fontFamily: 'Arial',
      fontStyle: 'normal',
      isShow: true,
    },
  },
  {
    label: t('分割线'),
    type: 'divider',
    options: {
      defaultValue: t('分割线'),
      orientation: 'left',
      marginTop: 0,
      marginBottom: 0,
      isShow: true,
    },
  },
];

export const workFlowComponents = [
  {
    label: t('意见簿'),
    typeName: t('意见簿'),
    type: 'opinion',
    options: {
      span: '',
      placeholder: t('（该组件仅用于接收工作流审批意见，不可手动编辑）'),
      width: '100%',
      showLabel: true,
      isShow: true,
    },
  },
];

export const infoComponents = [
  {
    label: t('信息体'),
    typeName: t('信息体'),
    type: 'info',
    options: {
      span: '',
      width: '100%',
      placeholder: '',
      infoType: 0, // 0当前用户 1当前组织 2当前时间
      isShowAllName: false,
      loadAgain: false, //二次加载
      showLabel: true,
      disabled: true,
      isShow: true,
    },
  },
  {
    label: t('组织架构'),
    typeName: t('组织架构'),
    type: 'organization',
    options: {
      span: '',
      width: '100%',
      orgzType: 0,
      placeholder: t('请选择组织架构'),
      isShowAllName: false,
      showLabel: true,
      disabled: false,
      required: false,
      isShow: true,
      events: {},
    },
  },
  {
    label: t('人员选择'),
    typeName: t('人员选择'),
    type: 'user',
    options: {
      span: '',
      width: '100%',
      defaultValue: '',
      placeholder: t('请选择人员'),
      userType: 0,
      prefix: '',
      suffix: 'ant-design:setting-outlined',
      showLabel: true,
      disabled: false,
      required: false,
      multiple: true,
      isShow: true,
      events: {},
    },
  },
];

export const financeComponents = [
  {
    label: t('计算组件'),
    typeName: t('计算组件'),
    type: 'computational',
    options: {
      span: '',
      width: '100%',
      defaultValue: 0,
      placeholder: t('请输入计算组件'),
      addonBefore: '',
      addonAfter: '',
      prefix: '',
      maxlength: null,
      showLabel: true,
      disabled: false,
      subTotal: false,
      computationalConfig: [],
      computationalConfigValue: t('== 请填写计算式配置 =='),
      beAdoptedComponent: [],
      decimals: 0,
      required: false,
      isShow: true,
      events: {},
    },
  },
  {
    label: t('货币大写'),
    typeName: t('货币大写'),
    type: 'money-chinese',
    options: {
      span: '',
      width: '100%',
      defaultValue: 0,
      placeholder: t('请输入货币大写'),
      addonBefore: '',
      addonAfter: '',
      prefix: '',
      suffix: '',
      maxlength: null,
      showLabel: true,
      disabled: false,
      subTotal: false,
      computationalConfig: [],
      computationalConfigValue: t('== 请填写计算式配置 =='),
      beAdoptedComponent: [],
      decimals: 0,
      required: false,
      isShow: true,
      rules: [],
      events: {},
    },
  },
];

export const questionnaireComponents = [
  {
    label: t('单行文本'),
    typeName: t('单行文本'),
    type: 'input',
    options: {
      defaultValue: '',
      placeholder: t('请输入单行文本'),
      maxlength: null,
      allowClear: false,
      required: false,
      showLabel: true,
      isQuestionnaire: true,
    },
  },
  {
    label: t('多行文本'),
    typeName: t('多行文本'),
    type: 'textarea',
    options: {
      defaultValue: '',
      placeholder: t('请输入多行文本'),
      maxlength: null,
      rows: 4,
      showCount: false,
      allowClear: false,
      required: false,
      showLabel: true,
      isQuestionnaire: true,
    },
  },
  {
    label: t('多选组件'),
    typeName: t('多选组件'),
    type: 'checkbox',
    options: {
      staticOptions: [
        {
          key: 1,
          label: 'option 1',
          value: 'option 1',
        },
        {
          key: 2,
          label: 'option 2',
          value: 'option 2',
        },
        {
          key: 3,
          label: 'option 3',
          value: 'option 3',
        },
      ],
      datasourceType: 'api',
      defaultSelect: '',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      showLabel: true,
      isQuestionnaire: true,
    },
  },
  {
    label: t('单选组件'),
    typeName: t('单选组件'),
    type: 'radio',
    options: {
      staticOptions: [
        {
          key: 1,
          label: 'option 1',
          value: 'option 1',
        },
        {
          key: 2,
          label: 'option 2',
          value: 'option 2',
        },
        {
          key: 3,
          label: 'option 3',
          value: 'option 3',
        },
      ],
      datasourceType: 'api',
      dataSource: undefined,
      dataSourceId: undefined,
      labelField: 'label',
      valueField: 'value',
      defaultSelect: '',
      apiConfig: {
        path: 'CodeGeneration/selection',
        method: 'GET',
        apiId: '93d735dcb7364a0f8102188ec4d77ac7',
      },
      itemId: undefined,
      dicOptions: [],
      required: false,
      showLabel: true,
      isQuestionnaire: true,
    },
  },
  {
    label: t('标题'),
    typeName: t('标题'),
    type: 'title',
    options: {
      defaultValue: t('标题'),
      color: '',
      align: 'center',
      fontSize: 18,
    },
  },
];
//地图组件不能映射的组件
export const filterType = [
  'number',
  'cascader',
  'area',
  'switch',
  'slider',
  'time',
  'time-range',
  'date',
  'date-range',
  'rate',
  'picker-color',
  'upload',
  'image',
  'button',
  'form',
  'title',
  'grid',
  'tab',
  'card',
  'divider',
  'computational',
  'money-chinese',
  'map',
  'one-for-one',
  'table-layout',
  'form-view',
  'iframe',
  'signature',
  'sun-form',
  'file',
];
// 不需要选择表与字段的组件
export const noHaveTableAndField = [
  'grid',
  'divider',
  'tab',
  'card',
  'title',
  'button',
  'opinion',
  'qrcode',
  'form-view',
  'iframe',
  'table-layout',
  'text',
  'barcode',
  'file',
];
// 不需要绑定字段的组件
export const noHaveField = [
  'grid',
  'divider',
  'tab',
  'card',
  'one-for-one',
  'title',
  'button',
  'opinion',
  'form',
  'time-range',
  'date-range',
  'qrcode',
  'form-view',
  'iframe',
  'table-layout',
  'text',
  'barcode',
  'sun-form',
  'file',
];
//不需要在列表页展示的
export const noShowList = [
  'grid',
  'divider',
  'tab',
  'card',
  'title',
  'button',
  'opinion',
  'form',
  'password',
  'upload',
  'image',
  'richtext-editor',
  'hiddenComponent',
  'qrcode',
  'one-for-one',
  'form-view',
  'iframe',
  'table-layout',
  'text',
  'barcode',
  'signature',
  'sun-form',
  'file',
];
// 没有标题的组件
export const noHaveTitle = ['grid', 'divider', 'card', 'button', 'title', 'text'];
// 使用栅格布局方式的组件
export const gridComponents = ['grid', 'form'];
//需要配置 数据源 的组件  ||  远程组件
export const remoteComponents = [
  'select',
  'radio',
  'checkbox',
  'associate-select',
  'associate-popup',
  'multiple-popup',
];
//子表单不能使用的组件
export const subFormUnUseComponents = [
  'card',
  'tab',
  'grid',
  'form',
  'title',
  'divider',
  'richtext-editor',
  'auto-code',
  'button',
  'slider',
  'image',
  'opinion',
  'qrcode',
  'one-for-one',
  'form-view',
  'text',
  'tree-component',
  'barcode',
  'signature',
  'iframe',
  'file',
];

//一对一子表单组件不能使用的组件
export const oneForOneUnUseComponents = [
  'form',
  'one-for-one',
  'form-view',
  'iframe',
  'tree-component',
  'signature',
  'sun-form',
];

//字段格式为短文本的组件
export const shortTextComponents = [
  'input',
  'password',
  'auto-code',
  'select',
  'cascader',
  'associate-select',
  'associate-popup',
  'area',
  'radio',
  'picker-color',
  'info',
  'organization',
];

//字段格式为长文本的组件
export const longTextComponents = [
  'textarea',
  'richtext-editor',
  'multiple-popup',
  'checkbox',
  'upload',
  'image',
  'user',
  'signature',
];

//字段格式为整数的组件
export const integerComponents = ['switch'];

//字段格式为小数的组件
export const decimalsComponents = ['number', 'slider', 'computational', 'money-chinese', 'rate'];

//字段格式为日期时间的组件
export const dateTimeComponents = ['date', 'date-range'];

//字段格式为时间的组件
export const timeComponents = ['time', 'time-range'];

//不能设置子表预加载的组件
export const unPreloadComponents = [
  'card',
  'tab',
  'grid',
  'form',
  'title',
  'divider',
  'image',
  'opinion',
  'upload',
  'picker-color',
  'organization',
  'form-view',
  'iframe',
  'text',
  'tree-component',
  'sun-form',
  'file',
];

//不能设置子表列表查看的组件
export const unListViewComponents = ['upload', 'sun-form'];
//无法配置触发事件
export const noConfigComponentEvent = [
  'auto-code',
  'form',
  'tab',
  'card',
  'grid',
  'title',
  'divider',
  'opinion',
  'info',
  'image',
  'qrcode',
  'form-view',
  'iframe',
  'table-layout',
  'text',
  'one-for-one',
  'barcode',
  'signature',
  'sun-form',
  'file',
];

//触发事件没有失焦事件的组件
export const noBlurComponentEvent = [
  'associate-popup',
  'multiple-popup',
  'radio',
  'checkbox',
  'switch',
  'slider',
  'rate',
  'picker-color',
  'upload',
  'button',
  'user',
  'tree-component',
];
//触发事件移动端没有失焦事件的组件
export const noBlurMobileComponentEvent = [
  'select', //下拉选择
  'tree-select-component', //树选择
  'cascader', //级联选择器
  'associate-select', //联想下拉
  'area', //行政区域
  'time', //时间
  'time-range', //时间范围
  'date', //日期
  'date-range', //日期范围
  'map', //地图
  'organization', //组织架构
  'user', //用户
];
//触发事件没有点击事件的组件
export const noClickComponentEvent = [
  'radio',
  'checkbox',
  'switch',
  'slider',
  'rate',
  'tree-component',
];
//触发事件没有值改变事件的组件
export const noChangeComponentEvent = ['button'];
//无法配置正则校验
export const noConfigRegularSetting = [
  'auto-code',
  'switch',
  'slider',
  'rate',
  'picker-color',
  'upload',
  'image',
  'button',
  'form',
  'tab',
  'card',
  'grid',
  'title',
  'divider',
  'opinion',
  'info',
  'organization',
  'user',
  'qrcode',
  'form-view',
  'iframe',
  'table-layout',
  'text',
  'barcode',
  'signature',
  'sun-form',
  'file',
];

//数据来源为数据字典的组件添加默认值选项
export const needDicDefaultValue = [
  'select',
  'associate-select',
  'associate-popup',
  'multiple-popup',
  'checkbox',
  'radio',
];
//需要字段名及表名为大写的数据库类型
export const upperFieldDb = ['oracle', 'dm', 'db2'];

export interface FieldInfo {
  name: string; //字段名
  type: ColumnType; //字段类型
  length: number; //字段长度
  isPk: boolean; // 是否主键
  isNullable: boolean; //是否可空
  disabled?: boolean; //字段是否可选
}

export interface TableInfo {
  name: string; //表名
  isMain: boolean; //是否主表
  fields: FieldInfo[]; //字段
  disabled?: boolean; //是否可选
  isSubForm?: boolean; //是否是子表
}
export interface TableCell {
  class?: string; //类名
  height?: number; //高
}
export interface TableTh {
  class?: string; //类名
  width?: number; //宽
}
export enum ColumnType {
  //字符串
  STRING,
  //数字
  NUMBER,
  //布尔
  BOOL,
  //日期
  DATE,
  //时间
  TIME,
  //LONG
  LONG,
}

export enum CodeType {
  Vue = 'vue',
  Html = 'html',
}

export enum PlatformType {
  Antd,
  Element,
}

export enum CommonInfoType {
  //当前用户
  USER_NAME,
  //当前组织
  DEPT_NAME,
  //当前时间
  TIME,
  //当前登录人编码
  USER_CODE,
  //当前登录人电话
  USER_MOBILE,
  //当前登录人邮箱
  USER_EMAIL,
  //当前登录人岗位
  POST_NAME,
  //当前登录人角色
  ROLE_NAME,
  //当前登录人联系地址
  USER_ADDRESS,
  //当前登录人组织ID
  DEPT_ID,
  //当前登录人组织编码
  DEPT_CODE,
}

export interface ComputationalConfig {
  $index: number; //表达式的唯一标识
  label: string; //名称
  type: string; //类型
  key: string; //唯一标识
  bindTable?: string; //绑定表
  bindField?: string; //绑定字段
  computationalMethod?: string; //计算方式
  isMainForm?: boolean; //是否是主表组件
}

export interface HiddenComponentInfo {
  key: string; //id
  type: 'hiddenComponent';
  code: string; //编码
  label: string; //名称
  value: string; //值
  bindTable: string; //绑定表
  bindField: string; //绑定字段
}

export interface DataSourceConfig {
  key: number; //唯一标识
  name: string; //名称
  show?: boolean; //是否展示
  width?: string | number; //宽度
  bindField?: string; //绑定字段
}

export interface MutipleHeadInfo {
  key: string; //id
  title: string; //名称
  dataIndex?: string; //名称
  children: string[] | object[]; //子列
  align?: string; //对齐
  isSingle?: boolean; //是否合并列，false-是，true-否
}

export interface FormDataProps {
  name?: string;
  schemas?: any;
  hiddenComponent?: HiddenComponentInfo[];
}

//可配置列表样式的组件
export const listConfigsComponents = [
  'input',
  'textarea',
  'number',
  'auto-code',
  'select',
  'cascader',
  'associate-select',
  'associate-popup',
  'multiple-popup',
  'area',
  'checkbox',
  'radio',
  'time',
  'time-range',
  'date',
  'date-range',
  'info',
  'organization',
  'user',
  'computational',
  'money-chinese',
];
