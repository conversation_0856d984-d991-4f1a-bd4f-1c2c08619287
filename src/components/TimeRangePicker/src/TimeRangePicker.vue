<template>
  <a-time-range-picker
    :size="size"
    v-model:value="modelValue"
    :placeholder="placeholder"
    :format="format"
    :valueFormat="format"
    :allowClear="allowClear"
    :disabled="disabled"
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
  import { ref, watchEffect, inject, watch } from 'vue';
  import { cloneDeep } from 'lodash-es';

  const props = defineProps({
    value: Array,
    size: String,
    placeholder: Array,
    format: String,
    allowClear: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    startField: String,
    endField: String,
    //子表名
    mainKey: String,
    record: Object,
  });

  const formModel = inject<any>('formModel', null);
  const modelValue = ref<string[]>([]);
  const emit = defineEmits(['update:value', 'change', 'update:record']);
  watch(
    () => props.value,
    (val: any) => {
      modelValue.value = val || [];
    },
    {
      immediate: true,
    },
  );

  watchEffect(() => {
    let emitData: any = null;
    if (props.mainKey) {
      if (props.record && props.record![props.startField!] && props.record![props.endField!]) {
        emitData = [props.record![props.startField!], props.record![props.endField!]];
      }
    } else {
      if (props.startField && props.endField && Object.keys(formModel).length) {
        emitData =
          !formModel[props.startField] || !formModel[props.endField]
            ? null
            : [formModel[props.startField], formModel[props.endField]];
      }
    }
    emit('update:value', emitData);
  });
  const handleChange = (time) => {
    try {
      const start = time?.length ? time[0] : '';
      const end = time?.length ? time[1] : '';
      if (props.mainKey) {
        const data = cloneDeep(props.record);
        if (data && props.startField && props.endField) {
          data[props.startField!] = start;
          data[props.endField!] = end;
        }
        emit('update:record', data);
      } else {
        formModel[props.startField!] = start;
        formModel[props.endField!] = end;
      }
    } catch (error) {
      console.log('error: ', error);
    }
    emit('update:value', time);
    emit('change', time);
  };
</script>
