<template>
  <div class="opinion-wrap">
    <div class="opinion-box" v-if="props.value.length > 0">
      <div class="opinion-item" v-for="(item, index) in props.value" :key="index">
        <div class="content">
          <span>审批结果：{{ getApproveTypeName(item.approveType, item.approveResult) }}</span
          ><span> 审批意见：{{ item.approveComment }}</span>
        </div>
        <div class="esignature-box" v-if="item.approveStampUrl">
          <img :src="item.approveStampUrl" />
        </div>
        <div class="approve">
          <span>{{ item.approveUserName ? item.approveUserName : '审批人' }}</span>
          <span>{{ item.approveTime }}</span>
        </div>
        <div class="hr"></div>
      </div>
    </div>
    <div class="opinion-box" v-else>
      <div class="placeholder">{{ placeholder }}</div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ApproveType } from '/@/enums/workflowEnum';
  import { TaskApproveOpinion } from '/@/model/workflow/bpmnConfig';

  let props = withDefaults(
    defineProps<{
      value: Array<TaskApproveOpinion>;
      placeholder: String;
    }>(),
    {
      value: () => {
        return [];
      },
    },
  );
  function getApproveTypeName(type: ApproveType, approveResult = '') {
    if (type == ApproveType.AGREE) {
      return '同意';
    } else if (type == ApproveType.DISAGREE) {
      return '拒绝';
    } else if (type == ApproveType.REJECT) {
      return '驳回';
    } else if (type == ApproveType.FINISH) {
      return '结束';
    } else if (type == ApproveType.OTHER) {
      return approveResult;
    } else {
      return '其他';
    }
  }
</script>
<style scoped>
  .opinion-box {
    min-height: 120px;
    border: 1px solid #dcdfe6;
    overflow-y: auto;
    padding: 4px 10px;
  }

  .opinion-item {
    position: relative;
    padding: 20px;
  }

  .esignature-box {
    position: absolute;
    width: 120px;
    top: 40px;
    right: 120px;
  }

  .esignature-box img {
    width: 100%;
    height: 100%;
    max-height: 62px;
  }

  .content {
    height: 80px;
    padding-left: 10px;
    display: flex;
    flex-direction: column;
  }

  .approve {
    position: absolute;
    right: 30px;
    top: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .hr {
    margin-top: 10px;
    border-top: 1px solid #dcdfe6;
  }

  .placeholder {
    color: #dcdfe6;
  }
</style>
