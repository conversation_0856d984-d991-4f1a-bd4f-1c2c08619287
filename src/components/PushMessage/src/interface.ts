import { MessageType } from '/@/enums/messageTemplate';
import { MemberConfig } from '/@/model/workflow/memberSetting';
//人员权限类型
export enum ReceiverConfigurationType {
  CANDIDATE_APPROVER = 0, //添加当前节点候选审批人
  POST = 1, //添加岗位
  ROLE = 2, //添加角色
  USER = 3, //添加人员
  NODE_APPROVER = 4, //节点审批人
  FORM_CREATOR = 5, //添加表单创建人
  FORM_FIELD = 6, //表单字段
}
// 推送消息参数配置
export interface MessageConfigItem {
  name: string; //参数名称
  description: string; //参数说明
  assignmentType: string; //赋值类型
  value: string; //值
  config: string; //赋值配置
}
// export interface ReceiverConfigurationItem {
//   type: ReceiverConfigurationType; //类型
//   name: string; //名称
// }
export interface MessageConfigure {
  id: string;
  name: string;
  type: MessageType;
  configs: Array<MessageConfigItem>;
  receiverConfiguration: Array<MemberConfig>;
}
