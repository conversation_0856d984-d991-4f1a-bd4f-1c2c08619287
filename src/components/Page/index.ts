import { withInstall } from '/@/utils';
import resizePageWrapper from './src/ResizePageWrapper.vue';
import pageWrapper from './src/PageWrapper.vue';
import pageFooter from './src/PageFooter.vue';
export const ResizePageWrapper = withInstall(resizePageWrapper);
export const PageWrapper = withInstall(pageWrapper);
export const PageFooter = withInstall(pageFooter);

export const PageWrapperFixedHeightKey = 'PageWrapperFixedHeight';
