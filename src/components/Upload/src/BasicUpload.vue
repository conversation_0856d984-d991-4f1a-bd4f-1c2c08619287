<template>
  <div>
    <Space>
      <a-button
        type="primary"
        @click="openUploadModal"
        :disabled="bindValue.disabled"
        preIcon="carbon:cloud-upload"
      >
        上传
      </a-button>
      <Tooltip placement="bottom" v-if="showPreview">
        <template #title>
          {{ t('component.upload.uploaded') }}
          <template v-if="fileList.length">
            {{ fileList.length }}
          </template>
        </template>
        <a-button :disabled="bindValue.disabled" @click="openPreviewModal">
          <Icon icon="bi:eye" />
          <template v-if="fileList.length && showPreviewNumber">
            {{ fileList.length }}
          </template>
        </a-button>
      </Tooltip>
    </Space>
    <UploadModal
      v-bind="bindValue"
      :previewFileList="fileList"
      :folderId="folderId"
      @register="registerUploadModal"
      @change="handleChange"
      @delete="handleDelete"
    />

    <UploadPreviewModal
      :value="fileList"
      :file-names="fileNameList"
      @register="registerPreviewModal"
      @list-change="handlePreviewChange"
      @delete="handlePreviewDelete"
    />
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, watch, unref, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import { Tooltip, Space } from 'ant-design-vue';
  import { useModal } from '/@/components/Modal';
  import { uploadContainerProps } from './props';
  import { omit } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  import UploadModal from './UploadModal.vue';
  import UploadPreviewModal from './UploadPreviewModal.vue';
  import { getFileList } from '/@/api/system/file';

  export default defineComponent({
    name: 'BasicUpload',
    components: { UploadModal, Space, UploadPreviewModal, Icon, Tooltip },
    props: uploadContainerProps,
    emits: ['change', 'delete', 'preview-delete', 'update:value'],

    setup(props, { emit, attrs }) {
      console.log('props.value1111', props.api);
      const { t } = useI18n();
      // 上传modal
      const [registerUploadModal, { openModal: openUploadModal }] = useModal();

      //   预览modal
      const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

      const fileList = ref<string[]>([]);
      const fileNameList = ref<string[]>([]);

      const folderId = computed(() => props.value);
      const showPreview = computed(() => {
        const { emptyHidePreview } = props;
        if (!emptyHidePreview) return true;
        return emptyHidePreview ? fileList.value.length > 0 : true;
      });

      const bindValue = computed(() => {
        const value = { ...attrs, ...props };
        return omit(value, 'onChange');
      });

      watch(
        () => props.value,
        async (value) => {
          //如果没有传入参数 默认不再请求文件列表
          if (value && value.length > 0) {
            const list = await getFileList({ folderId: value });
            fileList.value = list.map((item) => item.fileUrl);
            fileNameList.value = list.map((item) => item.fileName);
          } else {
            fileList.value = [];
          }
          emit('update:value', value);
          emit('change', value);
        },
        { immediate: true },
      );

      // 上传modal保存操作
      function handleChange(urls: string[], folderId: string, fileNames: string[]) {
        fileList.value = [...unref(fileList), ...(urls || [])];
        fileNameList.value = [...unref(fileNameList), ...(fileNames || [])];
        emit('update:value', folderId);
        emit('change', folderId);
      }

      // 预览modal保存操作
      function handlePreviewChange(urls: string[], folderId: string, fileNames: string[]) {
        fileList.value = [...(urls || [])];
        fileNameList.value = [...(fileNames || [])];
        emit('update:value', folderId);
        emit('change', folderId);
      }

      function handleDelete(record: Recordable) {
        emit('delete', record);
      }

      function handlePreviewDelete(url: string) {
        emit('preview-delete', url);
      }

      return {
        registerUploadModal,
        openUploadModal,
        handleChange,
        handlePreviewChange,
        registerPreviewModal,
        openPreviewModal,
        fileList,
        fileNameList,
        showPreview,
        bindValue,
        handleDelete,
        handlePreviewDelete,
        folderId,
        t,
      };
    },
  });
</script>
