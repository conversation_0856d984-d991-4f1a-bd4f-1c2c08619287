<template>
  <div>
    <div class="form-box">
      <div class="item">
        <div class="label"><em class="text-red-600">*</em>{{ t('接口名称') }}：</div>
        <a-input
          v-model:value="data.config.name"
          :placeholder="t('点击选择接口')"
          @click="open"
          style="width: 100%"
        >
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </div>
      <div class="item">
        <div class="label"><em class="text-red-600">*</em>{{ t('请求方法') }}：</div>
        <a-input
          v-model:value="data.config.method"
          disabled
          :placeholder="t('请求方法')"
          style="width: 100%"
        >
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </div>
    </div>

    <a-modal
      :width="1200"
      v-model:visible="visible"
      :title="t('选择接口')"
      :maskClosable="false"
      @ok="handleSubmit"
      @cancel="handleClose"
    >
      <InterfaceAddressList v-if="visible" v-model:id="data.config.id" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue';
  import { Icon } from '/@/components/Icon';
  import InterfaceAddressList from './InterfaceAddressList.vue';
  import { getInterfaceInfo } from '/@/api/system/interface/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ApiConfig } from '../interface';
  import { message } from 'ant-design-vue';
  const { t } = useI18n();
  const emit = defineEmits(['setApiConfig']);
  const props = withDefaults(defineProps<{ config: ApiConfig }>(), {
    config: () => {
      return {
        id: '',
        name: '',
        method: '',
        requestParamsConfigs: [], //Query Params
        requestHeaderConfigs: [], //Header
        requestBodyConfigs: [], //Body
      };
    },
  });
  let data: { config: ApiConfig } = reactive({
    config: {
      id: '',
      name: '',
      method: '',
      requestParamsConfigs: [], //Query Params
      requestHeaderConfigs: [], //Header
      requestBodyConfigs: [], //Body
    },
  });
  const visible = ref<boolean>(false);
  onMounted(() => {
    if (props.config.id) {
      data.config.id = props.config.id;
      // getApiData();
    }
    if (props.config.name) data.config.name = props.config.name;
    if (props.config.method) data.config.method = props.config.method;
  });
  function open() {
    visible.value = true;
  }
  async function handleSubmit() {
    if (data.config.id) {
      getApiData();
      visible.value = false;
    } else {
      message.warning('请选择一个接口');
    }
  }
  async function getApiData() {
    let interfaceInfo = await getInterfaceInfo({ id: data.config.id });
    if (interfaceInfo.id) data.config.id = interfaceInfo.id;
    if (interfaceInfo.name) data.config.name = interfaceInfo.name;
    if (interfaceInfo.method) data.config.method = interfaceInfo.method;
    emit('setApiConfig', interfaceInfo);
  }
  function handleClose() {
    data.config.id = props.config.id; //还原
    visible.value = false;
  }
</script>

<style lang="less" scoped>
  .title {
    display: flex;
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-box {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
  }

  .item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }

  .label {
    width: 120px;
    margin-left: 20px;
  }

  .select-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #d9d9d9;
    padding: 0 4px;
    height: 30px;
    width: 100%;
  }

  .rule-text {
    color: #d0cfd0;
  }

  .list {
    .row {
      height: 40px;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .common {
        flex-basis: 25%;
        margin-right: 4px;
      }
    }

    .head {
      background-color: #f9f9f9;
    }

    .item {
      border-bottom: 1px solid #f9f9f9;
    }

    .delete-icon {
      color: @clear-color;
    }
  }
</style>
