<template>
  <div class="box">
    <div class="box-left">
      <div class="title">{{ t('接口分类') }}</div>
      <a-tree
        v-model:expandedKeys="data.expandedKeys"
        v-model:selectedKeys="data.selectedKeys"
        :tree-data="data.interfaceTreeData"
        :fieldNames="data.fieldNames"
        @select="selectTree"
      />
    </div>
    <div class="box-right">
      <div class="title">{{ t('接口列表') }}</div>
      <a-row :gutter="12" style="padding: 10px 0; border-top: 1px solid #f0f0f0">
        <a-col :span="8">
          <a-input v-model:value="data.searchText" :placeholder="t('输入搜索关键字')" />
        </a-col>
        <a-col>
          <a-button type="primary" @click="handleSearch"> {{ t('搜索') }} </a-button>
          <a-button type="primary" @click="handleReset" class="ml-2"> {{ t('重置') }} </a-button>
        </a-col>
      </a-row>
      <a-table
        :dataSource="data.interfaceDataSource"
        :columns="data.apiConfigColumns"
        rowKey="id"
        :pagination="data.paginationProps"
        :row-selection="rowSelection"
        :customRow="customRow"
        :scroll="{ y: '200px' }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive } from 'vue';
  import { ColumnProps } from 'ant-design-vue/lib/table/Column';
  import { getInterfaceTree, getInterfaceList } from '/@/api/system/interface/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { InterfaceList } from '../interface';
  const { t } = useI18n();
  const emit = defineEmits(['update:id']);
  const props = defineProps(['id']);
  const data = reactive({
    interfaceTreeData: [] as any[],
    expandedKeys: [] as string[],
    selectedKeys: [] as string[],
    interfaceList: [] as InterfaceList[], //接口列表全部
    interfaceListSearch: [] as InterfaceList[], //接口列表搜索全部
    interfaceDataSource: [] as InterfaceList[], //接口列表分页
    selectedRowKeys: [] as string[],
    searchText: '' as string,
    isSearch: false,
    fieldNames: { children: 'children', title: 'name', key: 'id' },
    modalBodyStyle: {
      display: 'flex',
      padding: '15px 15px 10px 10px',
      minHeight: '400px',
    },
    apiConfigColumns: [
      {
        key: 'name',
        title: t('接口名称'),
        dataIndex: 'name',
        width: 300,
      },
      {
        key: 'path',
        title: t('接口地址'),
        dataIndex: 'path',
      },
    ] as ColumnProps[],
    paginationProps: {
      current: 1,
      total: 0,
      pageSize: 10,
      showQuickJumper: true,
      showSizeChanger: true,
      onChange: (page: number) => getInterfacePage(page),
      onShowSizeChange: (current: number, pageSize: number) => {
        data.paginationProps.pageSize = pageSize;
        getInterfacePage(current);
      },
    },
  });
  onMounted(() => {
    data.selectedRowKeys = [props.id];
    getInterfaceTreeData();
  });
  const rowSelection = computed(() => {
    return {
      checkStrictly: true,
      type: 'radio',
      selectedRowKeys: data.selectedRowKeys,
      onChange: (selectedRowKeys: string[]) => {
        data.selectedRowKeys = selectedRowKeys;
        emit('update:id', selectedRowKeys[0]);
      },
    };
  });
  const customRow = (record: InterfaceList) => {
    return {
      onClick: () => {
        let selectedRowKeys = [...data.selectedRowKeys];
        if (selectedRowKeys.indexOf(record.id) >= 0) {
          let index = selectedRowKeys.indexOf(record.id);
          selectedRowKeys.splice(index, 1);
        } else {
          selectedRowKeys = [record.id];
        }
        emit('update:id', selectedRowKeys[0]);
        data.selectedRowKeys = selectedRowKeys;
      },
    };
  };
  const getInterfacePage = (page) => {
    const size = data.paginationProps.pageSize;
    const startIndex = (page - 1) * size;
    const endIndex = page * size;

    const list = data.isSearch ? data.interfaceListSearch : data.interfaceList;

    data.paginationProps.current = page;
    data.paginationProps.total = list.length;
    data.interfaceDataSource = list.slice(startIndex, endIndex);
  };
  const handleSearch = () => {
    data.isSearch = true;
    data.interfaceListSearch = data.interfaceList.filter((item: InterfaceList) => {
      if (item.name.includes(data.searchText)) {
        return true;
      } else {
        return false;
      }
    });
    getInterfacePage(1);
  };
  const handleReset = () => {
    data.searchText = '';
    data.interfaceListSearch = data.interfaceList;
    getInterfacePage(1);
  };
  //获取左边树结构
  const getInterfaceTreeData = async () => {
    data.interfaceTreeData = await getInterfaceTree();
    //默认展示第一个接口分类的列表
    if (data.interfaceTreeData.length && data.interfaceTreeData[0].id) {
      const id = data.interfaceTreeData[0].id;
      data.selectedKeys.push(id);
      data.expandedKeys.push(id);
      selectTree(data.selectedKeys);
    }
  };
  //获取右边接口列表
  const selectTree = async (selectedKeys: string[]) => {
    if (selectedKeys.length > 0) {
      data.isSearch = false;
      const groupId = selectedKeys[selectedKeys.length - 1];
      const list = await getInterfaceList({ groupId });
      //清空接口列表
      data.interfaceList = list;
      getInterfacePage(1);
    } else {
      handleReset();
    }
  };
</script>

<style scoped>
  .box {
    display: flex;
    padding: 10px;
  }

  .box-left {
    flex-basis: 20%;
  }

  .box-right {
    flex-basis: 80%;
  }

  .title {
    font-size: 16px;
    line-height: 18px;
    margin-bottom: 15px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }

  :deep(.ant-table-body) {
    height: 200px;
  }
</style>
