<template>
  <a-input-password
    :size="size"
    v-model:value="value"
    :placeholder="placeholder"
    :maxlength="parseInt(maxlength)"
    :addonBefore="addonBefore"
    :addonAfter="addonAfter"
    :allowClear="allowClear"
    :disabled="disabled"
    :readonly="readonly"
    :visibilityToggle="visibilityToggle"
    @change="handleChange"
  >
    <template #prefix v-if="prefix">
      <Icon :icon="prefix" />
    </template>
  </a-input-password>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Icon } from '/@/components/Icon';

  const props = defineProps({
    size: String,
    value: {
      type: String,
      default: '',
    },
    defaultValue: String,
    placeholder: String,
    maxlength: {
      type: [String, Number],
      default: '',
    },
    addonBefore: String,
    addonAfter: String,
    allowClear: Boolean,
    disabled: Boolean,
    readonly: <PERSON><PERSON><PERSON>,
    prefix: String,
    visibilityToggle: Boolean,
  });

  const value = ref('');
  const emit = defineEmits(['update:value']);
  watch(
    () => props.value,
    (val) => {
      value.value = val;
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.defaultValue,
    (val) => {
      if (val) {
        emit('update:value', val);
      }
    },
    {
      immediate: true,
    },
  );
  const handleChange = (e) => {
    emit('update:value', e.target.value);
    value.value = props.value === undefined ? e.target.value : props.value;
  };
</script>
