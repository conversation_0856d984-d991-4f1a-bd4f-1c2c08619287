<template>
  <div class="list-item">
    <div class="item-box">
      <div class="item-left">
        <div class="icon-box"> <IconFontSymbol :icon="genderImg" fill-color="#5e95ff" /></div>
      </div>
      <div class="item-right">
        <a-popover>
          <template #content>
            <div class="item-role-box">
              <div class="item-title">{{ t('姓名') }} : {{ item?.name }}</div>
              <div class="item-title">{{ t('编码') }} : {{ item?.code || '-' }}</div>
            </div>
          </template>
          <div class="item-role-box">
            <div class="item-title">{{ t('姓名') }}</div>
            <div class="item-form-name">
              {{ item?.name }}
            </div>
          </div>
          <div class="item-role-box">
            <div class="item-title">{{ t('编码') }}</div>
            <div class="item-form-name">
              {{ item?.code || '-' }}
            </div>
          </div>
        </a-popover>
      </div>
      <div class="fixed-checked" v-if="hasCheckSlot">
        <slot name="check"></slot>
      </div>
      <div class="fixed-icon">
        <IconFontSymbol icon="user-copy" fill-color="#f8f8f8" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useSlots } from 'vue';
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { GenderEnum } from '/@/enums/userEnum';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = defineProps({
    item: Object,
    disabled: Boolean,
    isShowTree: {
      type: Boolean,
      default: true,
    },
  });
  const hasCheckSlot = computed(() => {
    return !!useSlots().check;
  });
  const genderImg = computed(() => {
    switch (props.item?.gender) {
      case GenderEnum.FEMALE:
        return 'a-nvshi1-copy';
      case GenderEnum.MALE:
        return 'touxiang1-copy';
      default:
        return 'touxiang3-copy';
    }
  });
</script>

<style lang="less" scoped>
  @custom-color: #5e95ff;
  @bg-color: #ffffff;

  .list-item {
    width: 261px;
    height: 100px;
    background: @bg-color;
    border-color: transparent;
    border-radius: 8px;
    margin-left: 20px;
    margin-bottom: 20px;
    overflow: hidden;

    &:hover {
      border: 1px solid @custom-color;
    }

    .item-box {
      display: flex;
      align-items: center;
      margin: 14px;
      position: relative;

      .item-left {
        .icon-box {
          width: 53px;
          height: 53px;
          font-size: 32px;
          border: 1px solid @custom-color;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .fixed-checked {
        position: absolute;
        bottom: -20px;
        z-index: 1;
        right: -6px;
      }

      .fixed-icon {
        position: absolute;
        right: -34px;
        font-size: 77px;
        transform: rotate(-30deg);
        top: -34px;
      }
    }
  }

  :deep(.ant-checkbox-inner) {
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: @custom-color;
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked::after),
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover),
  :deep(.ant-checkbox-inner),
  :deep(.ant-checkbox:hover),
  :deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: @custom-color;
  }

  .picked {
    border-width: 1px;
    border-style: solid;
    border-color: @custom-color;
  }

  .not-picked {
    border-width: 1px;
    border-style: solid;
    border-color: #f3f3f3;
  }

  .item-right {
    margin-left: 20px;

    .item-role-box {
      z-index: 3;
      position: relative;
      display: flex;
      height: 30px;
      line-height: 30px;
      align-items: center;

      .item-title {
        font-weight: 500;
        font-size: 14px;
        color: #636e80;
        margin-right: 10px;
      }

      .item-form-name {
        font-weight: bold;
        font-size: 16px;
        color: #1d2129;
        width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
