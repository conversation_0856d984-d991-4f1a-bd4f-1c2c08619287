<template>
  <div class="list-item">
    <div class="item-box">
      <div class="item-left">
        <div class="icon-box">
          <IconFontSymbol icon="jiaoseguanli1-copy" fill-color="#5e95ff"
        /></div>
      </div>
      <div class="item-role-box1">
        <div class="item-form-name">{{ props.item?.name }}</div>
        <div class="item-title">{{ t('角色名称') }}</div>
      </div>
      <div class="item-role-box2">
        <div class="item-form-name"> {{ props.item?.count ? props.item.count : 0 }}人</div>
        <div class="item-title">{{ t('角色人数') }}</div>
      </div>
      <div class="fixed-checked">
        <slot name="check"></slot>
      </div>
      <div class="fixed-icon">
        <IconFontSymbol icon="role1-copy" fill-color="#f8f8f8" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = defineProps({
    item: Object,
  });
</script>

<style lang="less" scoped>
  @custom-color: #5e95ff;
  @bg-color: #ffffff;

  .list-item {
    width: 268px;
    height: 100px;
    background: @bg-color;
    border-color: transparent;
    border-radius: 8px;
    margin-left: 20px;
    margin-bottom: 20px;
    overflow: hidden;

    &:hover {
      border: 1px solid @custom-color;
    }

    .item-box {
      display: flex;
      align-items: center;
      margin: 14px;
      position: relative;

      .item-left {
        .icon-box {
          width: 53px;
          height: 53px;
          font-size: 32px;
          border: 1px solid @custom-color;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .fixed-checked {
        position: absolute;
        bottom: -20px;
        z-index: 1;
        right: -6px;
      }

      .fixed-icon {
        position: absolute;
        right: -34px;
        font-size: 64px;
        transform: rotate(-30deg);
        top: -34px;
      }
    }
  }

  :deep(.ant-checkbox-inner) {
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: @custom-color;
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked::after),
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover),
  :deep(.ant-checkbox-inner),
  :deep(.ant-checkbox:hover),
  :deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: @custom-color;
  }

  .picked {
    border-width: 1px;
    border-style: solid;
    border-color: @custom-color;
  }

  .not-picked {
    border-width: 1px;
    border-style: solid;
    border-color: #f3f3f3;
  }

  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #636e80;
  }

  .item-form-name {
    font-weight: bold;
    font-size: 16px;
    color: #1d2129;
  }

  .item-role-box1 {
    position: absolute;
    z-index: 1;
    left: 72px;
    top: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .item-role-box2 {
    position: absolute;
    z-index: 1;
    left: 178px;
    top: 0;
  }
</style>
