<template>
  <div class="overflow-hidden">
    <NodeHead class="header-title" :node-name="t('组织架构')" />
    <BasicTree
      title=""
      search
      expandOnSearch
      :checkable="isCheckable"
      :treeData="treeData"
      :fieldNames="{ key: 'id', title: 'name' }"
      @select="handleSelect"
      @check="handleCheck"
    >
      <template #title="{ name, departmentType }">
        <a-tag color="processing" v-if="departmentType === 1">公司</a-tag>
        <a-tag color="warning" v-else-if="departmentType === 0">部门</a-tag>
        {{ name }}
      </template>
    </BasicTree>
  </div>
</template>

<script setup lang="ts">
  import { getDepartmentTree } from '/@/api/system/department';
  import { TreeItem } from '/@/components/Tree';
  import { BasicTree } from '/@/components/Tree';
  import { NodeHead } from '/@/components/ModalPanel/index';
  import { onMounted, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';

  defineProps({
    isCheckable: {
      type: Boolean,
      default: false,
    },
  });

  const { t } = useI18n();
  const emits = defineEmits(['select', 'check']);
  const treeData = ref<TreeItem[]>([]);
  async function getList() {
    treeData.value = (await getDepartmentTree()) as unknown as TreeItem[];
  }
  function handleSelect(keys: string) {
    emits('select', keys[0]);
  }

  function handleCheck(keys: string) {
    emits('check', keys);
  }
  onMounted(() => {
    getList();
  });
</script>
<style scoped>
  .header-title {
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-tree-treenode) {
    margin-bottom: 10px;
  }

  :deep(.vben-tree-header) {
    display: none;
  }
</style>
