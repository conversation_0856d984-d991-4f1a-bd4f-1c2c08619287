<template>
  <div ref="editor" :style="{ width }" class="mt-1.5"> </div>
</template>

<script lang="ts" setup>
  import { onBeforeUnmount, onMounted, ref, watchEffect } from 'vue';
  import WangEditor from 'wangeditor';

  import { uploadMultiApi } from '/@/api/sys/upload';

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
    },
  });
  const emits = defineEmits(['update:value', 'change', 'blur']);
  const editor = ref();
  const isFirst = ref(true);

  let instance: WangEditor | null;

  onMounted(() => {
    instance = new WangEditor(editor.value);
    instance.config.customUploadImg = async function (resultText) {
      try {
        let res = await uploadMultiApi(
          {
            name: 'file',
            file: resultText,
          },
          '',
        );
        res.forEach((x) => {
          instance?.cmd.do('insertHTML', '<img src="' + x.fileUrl + '"></img>');
        });
      } catch (error) {
        console.error(error);
      }
    };
    Object.assign(instance.config, {
      zIndex: 9,
      onchange: (html: string) => {
        emits('update:value', html);
        emits('change', html);
      },
      onblur: (html: string) => {
        if (!isFirst.value) {
          //第一次进入页面不触发blur
          emits('blur', html);
        }
        isFirst.value = false;
      },
    });
    instance.create();
    setTimeout(() => {
      instance?.txt.text(props.value || '');
    }, 500);
    watchEffect(() => (instance && props.disabled ? instance.disable() : instance?.enable()));
  });

  // watch(
  //   () => props.value,
  //   (val) => {
  //     instance?.txt.text(val);
  //   },
  //   {
  //     immediate: true,
  //   },
  // );

  onBeforeUnmount(() => {
    instance && instance.destroy();
    instance = null;
  });
</script>
<style lang="less" scoped>
  :deep(.w-e-text-container) {
    background-color: v-bind("props.disabled ? '#f5f5f5': '#fff'");
  }
</style>
