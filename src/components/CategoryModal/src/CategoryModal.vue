<template>
  <BasicModal @register="registerCategoryModal" v-bind="$attrs" wrapClassName="category-modal">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleCreate">
          <template #icon><PlusOutlined /></template>
          {{ t('新增') }}
        </a-button>
      </template>
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'clarity:note-edit-line',
              onClick: handleEdit.bind(null, record),
            },
            {
              icon: 'ant-design:delete-outlined',
              color: 'error',
              popConfirm: {
                title: t('是否确认删除'),
                confirm: handleDelete.bind(null, record),
              },
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
  <ChangeCategoryModal
    :title="title"
    :dicId="props.dicId"
    @register="registerChangeModal"
    @success="handleSuccess"
  />
</template>
<script lang="ts" setup>
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { BasicTable, useTable, TableAction, BasicColumn } from '/@/components/Table';
  import ChangeCategoryModal from './ChangeCategoryModal.vue';
  import { getDicDetailList, deleteDicDetail } from '/@/api/system/dic';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    dicId: String,
    title: String,
  });
  const columns: BasicColumn[] = [
    {
      dataIndex: 'name',
      title: t(props.title + '分类名称'),
    },

    {
      dataIndex: 'code',
      title: t(props.title + '分类编码'),
    },
    {
      dataIndex: 'sortCode',
      title: t('排序'),
      width: 70,
    },
    {
      dataIndex: 'enabledMark',
      title: t('是否有效'),
      customRender: ({ record }) => `${record.enabledMark ? t('有效') : t('无效')}`,
      width: 100,
    },
    {
      dataIndex: 'remark',
      title: t('备注'),
    },
  ];

  const emits = defineEmits(['success', 'register']);
  const { notification } = useMessage();

  const [registerCategoryModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({
      confirmLoading: false,
      draggable: false,
      title: data.title,
      showOkBtn: false,
      showCancelBtn: false,
      destroyOnClose: true,
      width: 800,
      fixedHeight: true,
    });
  });
  const [registerTable, { reload }] = useTable({
    api: getDicDetailList,
    beforeFetch: () => {
      return { itemId: props.dicId };
    },
    rowKey: 'id',
    columns,
    showTableSetting: true,
    bordered: true,
    actionColumn: {
      width: 80,
      title: t('操作'),
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  });

  const [registerChangeModal, { openModal: openChangeModal }] = useModal();

  const handleCreate = () => {
    openChangeModal(true, { title: t('新增' + props.title + '分类') });
  };
  const handleEdit = (record) => {
    openChangeModal(true, { title: t('修改' + props.title + '分类'), info: record });
  };
  const handleDelete = async (record) => {
    await deleteDicDetail([record.id]);
    notification.success({
      message: t('提示'),
      description: t('删除成功'),
    });
    emits('success');
    reload();
  };

  const handleSuccess = () => {
    emits('success');
    reload();
  };
</script>
<style lang="less" scoped>
  :deep(.vben-basic-table-header__toolbar) {
    justify-content: space-between;
  }
</style>
