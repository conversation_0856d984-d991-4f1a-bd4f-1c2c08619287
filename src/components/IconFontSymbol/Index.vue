<template>
  <svg class="iconfont-svg" :style="iconStyle" aria-hidden="true">
    <use :xlink:href="icon" />
  </svg>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  const props = defineProps({
    icon: {
      type: String,
      default: '',
    },
    style: {
      type: String,
      default: '',
    },
    fontSize: {
      type: String,
      default: '',
    },
    fillColor: {
      type: String,
      default: 'currentColor',
    },
  });
  const iconStyle = computed(() => {
    let style = props.style ? props.style : '';
    let fontSizeStyle = props.fontSize ? 'font-size:' + props.fontSize + 'px;' : '';
    let fillStyle = props.fillColor ? 'fill:' + props.fillColor + ';' : '';
    return style + fillStyle + fontSizeStyle;
  });
  const icon = computed(() => {
    return props.icon ? '#icon-' + props.icon : '';
  });
</script>

<style scoped>
  .iconfont-svg {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentcolor;
    overflow: hidden;
  }
</style>
