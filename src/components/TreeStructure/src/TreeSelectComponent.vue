<template>
  <div class="wrap-select-box" @click="clickData">
    <a-tree-select
      v-if="props.treeConfig.isMultiple"
      v-model:value="checkedKeys"
      show-search
      style="width: 100%; min-width: 400px"
      :dropdown-style="{ maxWidth: '400px', maxHeight: '400px', overflow: 'auto' }"
      placeholder="请选择"
      :allow-clear="false"
      :multiple="true"
      tree-default-expand-all
      :tree-data="treeState.treeData"
      tree-node-filter-prop="label"
      :field-names="{
        children: 'children',
        label: 'label',
        value: 'key',
      }"
      :disabled="disabled"
      :showCheckedStrategy="TreeSelect.SHOW_ALL"
      @blur="blurData"
      @change="changeSelectIds(checkedKeys)"
    />
    <a-tree-select
      v-else
      v-model:value="selectedKeys"
      show-search
      style="width: 100%; min-width: 400px"
      :dropdown-style="{ maxWidth: '400px', maxHeight: '400px', overflow: 'auto' }"
      placeholder="请选择"
      :allow-clear="false"
      :multiple="false"
      tree-default-expand-all
      :tree-data="treeState.treeData"
      tree-node-filter-prop="label"
      :field-names="{
        children: 'children',
        label: 'label',
        value: 'key',
      }"
      :showCheckedStrategy="TreeSelect.SHOW_ALL"
      @blur="blurData"
    />
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, watch, ref, reactive } from 'vue';
  import { TreeProps, TreeSelect } from 'ant-design-vue';
  import { getInfo } from '/@/api/system/generator/treeStructure';
  import { TreeStructureType } from '/@/enums/treeStructure';
  import { apiConfigFunc } from '/@/utils/event/design';
  import { cloneDeep } from 'lodash-es';
  import { ApiItem } from '/@/model/generator/treeStructure';
  const props = defineProps({
    value: String,
    defaultValue: String,
    disabled: Boolean,
    codeType: String,
    treeConfig: {
      default: () => {
        return {};
      },
      type: Object,
    },
  });
  const emit = defineEmits(['update:value', 'change', 'blur', 'click']);
  const selectedKeys = ref<string>();
  const checkedKeys = ref<string[] | number[]>([]);
  const treeState = reactive({
    treeData: [] as TreeProps['treeData'],
    height: 600,
    treeDataMap: new Map(),
  });
  watch(
    () => props.treeConfig.id,
    async (_val) => {
      if (_val) {
        resetData();
        await getTreeData();
      }
    },
  );
  watch(
    () => props.treeConfig.isMultiple,
    async (_val) => {
      if (_val) resetData();
    },
  );
  watch(
    () => props.value,
    (_val) => {
      if (props.value) {
        initData();
      }
    },
  );
  // watch(checkedKeys, () => {
  //   changeSelectIds(checkedKeys.value);
  // });
  // watch(selectedKeys, () => {
  //   changeSelectIds(selectedKeys.value);
  // });
  onMounted(async () => {
    await getTreeData();
    initData();
  });
  function resetData() {
    if (props.treeConfig.isMultiple) {
      checkedKeys.value = [];
      changeSelectIds(checkedKeys.value);
    } else {
      selectedKeys.value = '';
      changeSelectIds(selectedKeys.value);
    }
  }
  function initData() {
    if (props.value) {
      if (props.treeConfig.isMultiple) {
        let val = props.value.split(',');
        checkedKeys.value = val;
      } else {
        selectedKeys.value = props.value;
      }
    } else if (props.defaultValue) {
      if (props.treeConfig.isMultiple) {
        let val = props.defaultValue.split(',');
        checkedKeys.value = val;
      } else {
        selectedKeys.value = props.defaultValue;
      }
    }
  }
  async function getTreeData() {
    if (!props.treeConfig?.id) {
      return;
    }
    let formState = await getInfo(props.treeConfig?.id);
    let config = JSON.parse(formState.config);
    if (formState.type == TreeStructureType.STATIC) {
      // treeState.treeData = config.staticData;
      let data: Array<ApiItem> = [];
      config.staticData.forEach((element, index) => {
        let res = getItem(element, index, element.value);
        data.push(res);
      });
      treeState.treeData = data;
    } else {
      let data: Array<ApiItem> = [];
      let apiData = await apiConfigFunc(config.apiData.apiConfig, false, {});
      let labelKey = config.apiData.apiColumns[0].dataIndex;
      let valueKey = config.apiData.apiColumns[1].dataIndex;
      apiData.data.forEach((element) => {
        let res = getApiItem(element, labelKey, valueKey, element[valueKey]);
        data.push(res);
      });
      treeState.treeData = data;
    }
    setTreeDataMap(cloneDeep(treeState.treeData));
  }
  function getApiItem(item, labelKey, valueKey, value): ApiItem {
    let childrenArr: Array<ApiItem> = [];
    if (item.children.length > 0) {
      item.children.forEach((element) => {
        let value2 = value + '---' + element[valueKey];
        childrenArr.push(getApiItem(element, labelKey, valueKey, value2));
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    return {
      ...otherParams,
      label: item[labelKey],
      key: value,
      children: childrenArr,
    };
  }
  function getItem(item, index, rowKey): ApiItem {
    let childrenArr: Array<ApiItem> = [];
    if (item.children.length > 0) {
      item.children.forEach((element, index2) => {
        let key = index + '---' + index2;
        let rowKey2 = rowKey + '---' + element.value;
        childrenArr.push(getItem(element, key, rowKey2));
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    return {
      ...otherParams,
      label: item.label,
      index: index,
      key: rowKey,
      children: childrenArr,
    };
  }
  function setTreeDataMap(treeData) {
    if (treeData) {
      treeData.forEach((element) => {
        setTreeMapItem(element);
      });
    }
  }
  function setTreeMapItem(item) {
    if (item.children.length > 0) {
      item.children.forEach((element) => {
        setTreeMapItem(element);
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    treeState.treeDataMap.set(item.key, otherParams);
  }
  function changeSelectIds(ids) {
    console.log('ids: ', ids);
    let val = '';
    if (props.treeConfig.isMultiple) {
      val = ids.join(',');
    } else {
      val = ids;
    }
    emit('update:value', val);
    emit('change', val);
  }
  function blurData() {
    emit('blur');
  }
  function clickData() {
    emit('click');
  }
</script>
<style lang="less" scoped>
  .wrap-select-box {
    width: 100%;
    display: flex;
  }
</style>
