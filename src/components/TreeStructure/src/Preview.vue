<template>
  <div class="button-item" @click="open">
    <span v-if="previewData.visible">收起 <Icon icon="ant-design:double-left-outlined" /></span>
    <span v-else>展开 <Icon icon="ant-design:double-right-outlined" /></span>
  </div>
  <!-- 预览弹窗 -->
  <div class="box" v-if="previewData.visible">
    <div class="header vben-tree-header flex px-2 py-2 items-center justify-between pd">
      <span class="vben-basic-title">{{ formState?.name }}</span>
      <div class="button-item" @click="previewData.visible = false">
        <span>收起 <Icon icon="ant-design:double-left-outlined" /></span>
      </div>
    </div>
    <div class="model-box">
      <a-table
        v-if="previewData.visible"
        :dataSource="previewData.data"
        :columns="previewData.columns"
        defaultExpandAllRows
        rowKey="key"
        :pagination="false"
        :row-selection="{
          selectedRowKeys: previewData.selectedRowKeys,
          onChange: onSelectChange,
          type: props.type,
        }"
        :customRow="customRow"
        :scroll="{ y: '700px' }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import { TreeStructureType } from '/@/enums/treeStructure';
  import Icon from '/@/components/Icon/index';
  import {
    StaticColumnItem,
    StaticDataItem,
    TreeStructureFormState,
  } from '/@/model/generator/treeStructure';
  const props = defineProps({
    formState: Object as () => TreeStructureFormState,
    checkedKeys: Array as () => Array<string | number>,
    type: String,
  });
  const emits = defineEmits(['update:checkedKeys']);
  const previewData = reactive({
    visible: false,
    type: TreeStructureType.STATIC as TreeStructureType,
    columns: [] as Array<StaticColumnItem>,
    data: [] as Array<StaticDataItem>,
    selectedRowKeys: [] as (string | number)[],
  });
  const customRow = (record) => {
    return {
      onClick: () => {
        let selectedRowKeys = [...previewData.selectedRowKeys];
        if (props.type == 'radio') {
          if (selectedRowKeys.indexOf(record.key) >= 0) {
            let index = selectedRowKeys.indexOf(record.key);
            selectedRowKeys.splice(index, 1);
          } else {
            selectedRowKeys = [record.key];
          }
        } else {
          if (selectedRowKeys.indexOf(record.key) >= 0) {
            let index = selectedRowKeys.indexOf(record.key);
            selectedRowKeys.splice(index, 1);
          } else {
            selectedRowKeys.push(record.key);
          }
        }
        previewData.selectedRowKeys = selectedRowKeys;
        emitSelectIds();
      },
    };
  };
  const onSelectChange = (rowKey: string[]) => {
    previewData.selectedRowKeys = rowKey;
    emitSelectIds();
  };
  function emitSelectIds() {
    emits('update:checkedKeys', previewData.selectedRowKeys);
  }
  function open() {
    previewData.type =
      props.formState?.type == undefined ? TreeStructureType.STATIC : props.formState?.type;
    if (previewData.type == TreeStructureType.STATIC) {
      changeColumn(props.formState?.columns);
      if (props.formState?.config.staticData) previewData.data = props.formState?.config.staticData;
    } else {
      changeColumn(props.formState?.config.apiData.apiColumns);
      previewData.data = props.formState?.config.apiData.data;
    }
    if (props.checkedKeys) previewData.selectedRowKeys = props.checkedKeys;
    previewData.visible = true;
  }
  function changeColumn(columns) {
    previewData.columns = [];
    for (let index = 1; index < columns.length; index++) {
      if (index % 2) {
        let labelIndex = index - 1;
        previewData.columns.push({
          dataIndex: columns[labelIndex].dataIndex,
          key: columns[labelIndex].dataIndex,
          title:
            previewData.type == TreeStructureType.STATIC
              ? getTitle(columns[labelIndex].title)
              : columns[labelIndex].title,
          width: columns[labelIndex].width,
          value: columns[index].dataIndex,
        });
      }
    }
  }
  function getTitle(title) {
    if (title.includes('(')) {
      let arr = title.split('(');
      if (arr.length > 0 && arr[1].includes(')')) {
        let arr2 = arr[1].split(')');
        return arr2[0];
      }
    }
    return '';
  }
</script>

<style lang="less" scoped>
  .button-item {
    display: inline-flex;
    color: #5e95ff;
    text-align: center;
  }

  .model-box {
    margin: 20px;
    min-height: 400px;
  }

  .box {
    width: 700px;
    position: absolute;
    left: 4px;
    top: 4px;
    bottom: 0;
    z-index: 999;
    background: #fff;
    box-shadow: 4px 4px 4px 4px #ccc;
  }

  .pd {
    padding: 16px 8px;
  }
</style>
