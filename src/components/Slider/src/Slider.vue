<template>
  <div :style="style">
    <a-slider
      v-model:value="value"
      :min="min"
      :max="max"
      :step="step"
      :reverse="reverse"
      :disabled="disabled"
      @change="handleChange"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';

  const props = defineProps({
    value: {
      type: [Number, String],
      default: 0,
    },
    min: Number,
    max: Number,
    step: Number,
    reverse: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    style: Object,
  });

  const value = ref(0);
  const emit = defineEmits(['update:value', 'change']);
  watch(
    () => props.value,
    (val) => {
      value.value = Number(val);
    },
    {
      immediate: true,
    },
  );
  const handleChange = (val) => {
    emit('update:value', val);
    emit('change');
    value.value = props.value === undefined ? val : props.value;
  };
</script>
