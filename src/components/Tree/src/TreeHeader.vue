<template>
  <div :class="bem()" class="flex px-2 items-center justify-between header">
    <slot name="headerTitle" v-if="slots.headerTitle"></slot>
    <BasicTitle :helpMessage="helpMessage" v-if="!slots.headerTitle && title">
      {{ title }}
    </BasicTitle>
    <div class="cursor-pointer" v-if="toolbar">
      <Dropdown @click.prevent v-if="toolbar">
        <Icon icon="ion:ellipsis-vertical" />
        <template #overlay>
          <Menu @click="handleMenuClick">
            <template v-for="item in toolbarList" :key="item.value">
              <MenuItem v-bind="{ key: item.value }">
                {{ item.label }}
              </MenuItem>
              <MenuDivider v-if="item.divider" />
            </template>
          </Menu>
        </template>
      </Dropdown>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, useSlots } from 'vue';
  import { Dropdown, Menu, MenuItem, MenuDivider } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { BasicTitle } from '/@/components/Basic/index';
  import { useI18n } from '/@/hooks/web/useI18n';

  import { createBEM } from '/@/utils/bem';
  import { ToolbarEnum } from './tree';

  const [bem] = createBEM('tree-header');

  const props = defineProps({
    helpMessage: {
      type: [String, Array] as PropType<string | string[]>,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    toolbar: {
      type: Boolean,
      default: false,
    },
    checkable: {
      type: Boolean,
      default: false,
    },

    checkAll: {
      type: Function,
      default: undefined,
    },
    expandAll: {
      type: Function,
      default: undefined,
    },
  } as const);
  const emit = defineEmits(['strictly-change']);

  const slots = useSlots();
  const { t } = useI18n();

  const toolbarList = computed(() => {
    const { checkable } = props;
    const defaultToolbarList = [
      { label: t('展开全部'), value: ToolbarEnum.EXPAND_ALL },
      {
        label: t('折叠全部'),
        value: ToolbarEnum.UN_EXPAND_ALL,
        divider: checkable,
      },
    ];

    return checkable
      ? [
          { label: t('选择全部'), value: ToolbarEnum.SELECT_ALL },
          {
            label: t('取消选择'),
            value: ToolbarEnum.UN_SELECT_ALL,
            divider: checkable,
          },
          ...defaultToolbarList,
          { label: t('层级关联'), value: ToolbarEnum.CHECK_STRICTLY },
          { label: t('层级独立'), value: ToolbarEnum.CHECK_UN_STRICTLY },
        ]
      : defaultToolbarList;
  });

  function handleMenuClick(e) {
    const { key } = e;
    switch (key) {
      case ToolbarEnum.SELECT_ALL:
        props.checkAll?.(true);
        break;
      case ToolbarEnum.UN_SELECT_ALL:
        props.checkAll?.(false);
        break;
      case ToolbarEnum.EXPAND_ALL:
        props.expandAll?.(true);
        break;
      case ToolbarEnum.UN_EXPAND_ALL:
        props.expandAll?.(false);
        break;
      case ToolbarEnum.CHECK_STRICTLY:
        emit('strictly-change', false);
        break;
      case ToolbarEnum.CHECK_UN_STRICTLY:
        emit('strictly-change', true);
        break;
    }
  }
</script>
<style lang="less" scoped>
  .header {
    height: 60px;
    border-bottom: 1px solid #f0f0f0 !important;
    box-sizing: content-box;
  }
</style>
