<template>
  <div>
    <input
      type="color"
      :value="colorValue"
      @change="handleChange"
      :style="{ 'pointer-events': disabled ? 'none' : 'auto' }"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  const props = defineProps({
    size: String,
    value: String,
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  const colorValue = ref('#ffffff');
  const emit = defineEmits(['update:value', 'change']);
  watch(
    () => props.value,
    (val) => {
      colorValue.value = val || '#ffffff';
    },
    {
      immediate: true,
    },
  );

  const handleChange = ({ target }) => {
    emit('update:value', target.value);
    emit('change', target.value);
  };
</script>
