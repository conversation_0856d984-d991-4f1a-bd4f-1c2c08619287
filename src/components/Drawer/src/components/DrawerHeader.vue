<template>
  <BasicTitle v-if="!isDetail" :class="prefixCls">
    <slot name="title"></slot>
    {{ !$slots.title ? title : '' }}

    <div v-if="canFullscreen" class="fullscreen-icon">
      <Tooltip :title="t('还原')" placement="bottom" v-if="fullScreen">
        <FullscreenExitOutlined role="full" @click="handleFullScreen" />
      </Tooltip>
      <Tooltip :title="t('最大化')" placement="bottom" v-else>
        <FullscreenOutlined role="close" @click="handleFullScreen" />
      </Tooltip>
    </div>
  </BasicTitle>

  <div :class="[prefixCls, `${prefixCls}--detail`]" v-else>
    <span :class="`${prefixCls}__twrap`">
      <span @click="handleClose" v-if="showDetailBack">
        <ArrowLeftOutlined :class="`${prefixCls}__back`" />
      </span>
      <span v-if="title">{{ title }}</span>
    </span>

    <span :class="`${prefixCls}__toolbar`">
      <slot name="titleToolbar"></slot>
    </span>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { BasicTitle } from '/@/components/Basic';
  import {
    FullscreenExitOutlined,
    FullscreenOutlined,
    ArrowLeftOutlined,
  } from '@ant-design/icons-vue';
  import { Tooltip } from 'ant-design-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useI18n } from '/@/hooks/web/useI18n';

  import { propTypes } from '/@/utils/propTypes';
  export default defineComponent({
    name: 'BasicDrawerHeader',
    components: {
      BasicTitle,
      ArrowLeftOutlined,
      FullscreenExitOutlined,
      FullscreenOutlined,
      Tooltip,
    },
    props: {
      isDetail: propTypes.bool,
      showDetailBack: propTypes.bool,
      title: propTypes.string,
      canFullscreen: { type: Boolean, default: false },
    },
    emits: ['close', 'fullscreen'],
    setup(_, { emit }) {
      const { prefixCls } = useDesign('basic-drawer-header');
      const { t } = useI18n();
      const fullScreen = ref(false);

      function handleClose() {
        emit('close');
      }
      function handleFullScreen(e: Event) {
        e?.stopPropagation();
        e?.preventDefault();
        fullScreen.value = !fullScreen.value;
        emit('fullscreen', fullScreen.value);
      }
      return { prefixCls, handleClose, t, fullScreen, handleFullScreen };
    },
  });
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-basic-drawer-header';
  @footer-height: 60px;
  .@{prefix-cls} {
    display: flex;
    height: 100%;
    align-items: center;

    &__back {
      padding: 0 12px;
      cursor: pointer;

      &:hover {
        color: @primary-color;
      }
    }

    &__twrap {
      flex: 1;
    }

    &__toolbar {
      padding-right: 50px;
    }
  }

  .fullscreen-icon {
    position: absolute;
    right: 0;
  }
</style>
