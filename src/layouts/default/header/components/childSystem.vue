<template>
  <AMenu
    v-if="getShowContent && getShowBread"
    :selectedKeys="cur"
    mode="horizontal"
    :theme="Mtheme"
    :class="[prefixCls, `${prefixCls}--${theme}`]"
  >
    <MenuItem v-for="item in system" :key="item.id" class="item-system">
      <template #icon>
        <Tooltip>
          <template #title>{{ item.name }}</template>
          <Icon
            :icon="item.icon || 'ant-design:appstore-outlined'"
            color="#DEDFFF"
            size="26"
            style="vertical-align: -7px"
            @click="changeSystem(item.id)"
          />
        </Tooltip>
      </template>
      {{ item.name }}
    </MenuItem>
  </AMenu>
  <Tooltip title="子系统" :mouseEnterDelay="0.5">
    <Dropdown
      v-if="getShowTopMenu && !getIsMobile"
      placement="bottom"
      :trigger="['click']"
      :dropMenuList="system"
      :selectedKeys="cur"
      @menu-event="handleMenuEvent"
      overlayClassName="app-locale-picker-overlay"
    >
      <template #overlay>
        <a-menu>
          <a-menu-item :key="item.event" v-for="item in system">
            <Icon
              :icon="item.icon || 'ant-design:appstore-outlined'"
              color="#DEDFFF"
              size="26"
              style="vertical-align: -7px"
            />
            {{ item.text }}
          </a-menu-item>
        </a-menu>
      </template>
      <span class="cursor-pointer flex items-center p-2">
        <Icon icon="icon-park-outline:system" size="22" />
      </span>
    </Dropdown>
  </Tooltip>
</template>
<script lang="ts">
  import { defineComponent, onMounted, ref, watch } from 'vue';
  import type { MenuTheme } from 'ant-design-vue';
  import { Breadcrumb, Tooltip, Menu } from 'ant-design-vue';
  import Icon from '/@/components/Icon';
  import { Dropdown } from '/@/components/Dropdown';
  import type { DropMenu } from '/@/components/Dropdown';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useAppInject } from '/@/hooks/web/useAppInject';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { propTypes } from '/@/utils/propTypes';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { storeToRefs } from 'pinia';
  import { usePermissionStore } from '/@/store/modules/permission';
  export default defineComponent({
    name: 'LayoutBreadcrumb',
    components: {
      Icon,
      [Breadcrumb.name]: Breadcrumb,
      Tooltip,
      AMenu: Menu,
      MenuItem: Menu.Item,
      Dropdown,
    },
    props: {
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup() {
      const permissionStore = usePermissionStore();
      const { subSystemList } = storeToRefs(permissionStore);
      const Mtheme = ref<MenuTheme>('dark');
      const cur = ref<string[]>([permissionStore.getSubSystem]);

      const { prefixCls } = useDesign('layout-breadcrumb');
      const { getIsMobile } = useAppInject();
      const system = ref<any[]>([]);
      const { t } = useI18n();
      const { getShowTopMenu } = useMenuSetting();
      const { getShowContent, getShowBread } = useHeaderSetting();
      watch(
        () => subSystemList.value,
        (v) => {
          system.value = v;
        },
        { deep: true },
      );
      onMounted(async () => {
        await permissionStore.changeSubsystem(getShowTopMenu.value, getIsMobile.value);
        system.value = permissionStore.getSubSysList;
      });

      //切换系统
      function changeSystem(systemId: string) {
        permissionStore.setSubSystem(systemId);
        cur.value = [systemId];
      }

      function handleMenuEvent(menu: DropMenu) {
        if (cur.value[0] === menu.event) {
          return;
        }
        cur.value = [menu.event as string];

        permissionStore.setSubSystem(menu.event as string);
      }

      return {
        t,
        prefixCls,
        cur,
        changeSystem,
        system,
        Mtheme,
        getShowContent,
        getShowTopMenu,
        getShowBread,
        getIsMobile,
        handleMenuEvent,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-layout-breadcrumb';

  .@{prefix-cls} {
    &.ant-menu {
      background: none;
      border: 0;

      .ant-menu-item {
        padding: 0;

        &:hover {
          background: none;
        }

        .ant-menu-item-icon {
          background: rgb(171 170 205 / 30%);
          width: 40px;
          height: 40px;
          border-radius: 50%;
          border: 1px solid rgb(171 170 205 / 30%);
          margin: 0 5px;
          padding: 6px;
        }

        .ant-menu-title-content {
          display: none;
        }
      }

      .ant-menu-item-selected {
        background: none;

        .ant-menu-item-icon {
          border: 1px solid rgb(222 223 255);
        }

        &::after {
          border: 0;
        }
      }
    }
  }
</style>
