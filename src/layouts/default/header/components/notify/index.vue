<template>
  <div :class="prefixCls">
    <Popover
      title=""
      trigger="click"
      arrow-point-at-center
      :overlayClassName="`${prefixCls}__overlay`"
    >
      <Badge :count="count" dot :numberStyle="numberStyle">
        <Icon icon="ion:notifcations" size="22" />
      </Badge>
      <template #content>
        <Tabs>
          <template v-for="item in listData" :key="item.key">
            <TabPane>
              <template #tab>
                {{ item.name }}
                <span v-if="item.unreadNum !== 0">({{ item.unreadNum }})</span>
              </template>

              <div v-if="item.key === '4'" class="min-h-88">
                <div>
                  <div class="list-item">
                    <span class="header-title">{{ t('流程审批') }}</span>
                    <router-link
                      class="opr"
                      :to="{
                        path: '/task/processtasks',
                        query: {
                          name: 'ToDoTasks',
                        },
                      }"
                    >
                      {{ t('查看更多') }} 》
                    </router-link>
                  </div>
                  <div v-if="item.list.length > 0">
                    <div class="readed-mark" v-for="it in item.list" :key="it.id">
                      <div
                        class="list-data-item"
                        :class="it.read ? 'readed' : ''"
                        @click="ApprovalHandle(it, item.key, 1)"
                      >
                        <span class="list-item-title">{{ it.title }}</span>
                        <span class="list-item-time">{{ it.timeFormat }}</span>
                      </div>
                    </div>
                  </div>
                  <a-empty :image="simpleImage" v-else />
                </div>
                <div>
                  <div class="list-item">
                    <span class="header-title">{{ t('流程传阅') }}</span>
                    <router-link
                      class="opr"
                      :to="{
                        path: '/task/processtasks',
                        query: { name: 'MyCirculation' },
                      }"
                    >
                      {{ t('查看更多') }} 》
                    </router-link>
                  </div>
                  <div v-if="item.read!.length>0">
                    <div class="list-item readed-mark" v-for="it in item.read" :key="it.id">
                      <div
                        class="list-data-item"
                        :class="it.read ? 'readed' : ''"
                        @click="ApprovalHandle(it, item.key, 2)"
                      >
                        <span class="list-item-title">{{ it.title }}</span>
                        <span class="list-item-time">{{ it.timeFormat }}</span>
                      </div>
                    </div>
                  </div>
                  <a-empty :image="simpleImage" v-else />
                </div>
                <div class="notice-footer">
                  <span @click="setReadAll(item.key)">{{ t('全部设置已读') }}</span>
                </div>
              </div>
              <div v-else-if="item.key === '5'" class="h-88">
                <div v-if="item.list.length > 0" class="h-82">
                  <div class="list-item readed-mark" v-for="(it, index) in item.list" :key="index">
                    <a-tooltip>
                      <template #title>{{ it.messageContent }}</template>
                      <span
                        style="
                          width: 220px;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        {{ it.messageContent }}</span
                      >
                    </a-tooltip>
                    <span class="list-item-time">{{ it.sendTime }}</span>
                  </div>
                  <div class="notice-footer">
                    <span @click="prevSystemPage">{{ t('上一页') }}</span>
                    <span @click="nextSystemPage">{{ t('下一页') }}</span>
                  </div>
                </div>
                <a-empty :image="simpleImage" v-else />
              </div>
              <div v-else class="h-88">
                <div v-if="item.list.length > 0" class="h-82">
                  <div
                    class="list-item readed-mark"
                    v-for="it in item.list"
                    :key="it.id"
                    :class="it.read ? 'readed' : ''"
                    @click="
                      () => {
                        it.read = 1;
                        setReadSingle(it.id, item.key);
                      }
                    "
                  >
                    <router-link
                      class="opr"
                      :to="{
                        path: '/OA001/schedule',
                        query: { name: 'MySchedule' },
                      }"
                      v-if="item.key === '3'"
                    >
                      <a-tooltip>
                        <template #title>{{ it.title }}</template>
                        {{ it.title }}
                      </a-tooltip>
                    </router-link>
                    <span v-else class="list-item-title">
                      <a-tooltip>
                        <template #title>{{ it.title }}</template>
                        {{ it.title }}
                      </a-tooltip>
                    </span>
                    <span class="list-item-time">{{ it.timeFormat }}</span>
                  </div>
                  <div class="notice-footer"
                    ><span @click="setReadAll(item.key)">{{ t('全部设置已读') }}</span
                    ><span v-if="item.key == '1'"
                      ><router-link
                        class="opr"
                        :to="{
                          path: '/OA001/oaNews',
                        }"
                      >
                        {{ t('查看更多') }} 》
                      </router-link></span
                    ><span v-if="item.key == '2'"
                      ><router-link
                        class="opr"
                        :to="{
                          path: '/OA001/oaNotices',
                        }"
                      >
                        {{ t('查看更多') }} 》
                      </router-link></span
                    ><span v-if="item.key == '3'"
                      ><router-link
                        class="opr"
                        :to="{
                          path: '/OA001/schedule',
                        }"
                      >
                        {{ t('查看更多') }} 》
                      </router-link></span
                    ></div
                  >
                </div>
                <a-empty :image="simpleImage" v-else />
              </div>
            </TabPane>
          </template>
        </Tabs>
      </template>
    </Popover>
    <ModalPanel
      :visible="viewOpen"
      :width="800"
      :title="viewData.briefHead"
      @submit="viewOpen = false"
      @close="viewOpen = false"
    >
      <ViewModal v-if="viewOpen" :viewData="viewData" />
    </ModalPanel>
    <a-modal
      v-if="Approval.visible"
      :visible="Approval.visible"
      wrapClassName="full-modal"
      width="100%"
      :footer="null"
    >
      <ApprovalProcess
        v-if="Approval.visible"
        :taskId="Approval.taskId || ''"
        :processId="Approval.processId || ''"
        :schemaId="Approval.schemaId || ''"
        :visible="Approval.visible"
        @close="
          () => {
            Approval.visible = false;
          }
        "
      />
    </a-modal>
    <a-modal
      v-if="LookData.visible"
      :visible="LookData.visible"
      wrapClassName="full-modal"
      width="100%"
      :footer="null"
    >
      <LookProcess
        v-if="LookData.visible"
        :taskId="LookData.taskId || ''"
        :processId="LookData.processId || ''"
        :visible="LookData.visible"
        @close="
          () => {
            LookData.visible = false;
          }
        "
      />
    </a-modal>
  </div>
</template>
<script lang="ts">
  import { computed, defineComponent, ref, onUnmounted, reactive } from 'vue';
  import { Popover, Tabs, Badge } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { tabListData } from './data';

  import { useDesign } from '/@/hooks/web/useDesign';

  import {
    getOaMessage,
    getOaNews,
    setOaRead,
    setSingleRead,
    setWorkReadAll,
    getScheduleMsg,
    setScheduleRead,
    setScheduleReadAll,
    getSystemMessage,
    getApproveState,
  } from '/@/api/system/login';
  import { Empty } from 'ant-design-vue';
  import { ModalPanel } from '/@/components/ModalPanel/index';
  import ApprovalProcess from '/@/views/workflow/task/components/ApprovalProcess.vue';
  import LookProcess from '/@/views/workflow/task/components/LookProcess.vue';
  import ViewModal from '/@/views/dataconfig/oaNews/components/View.vue';
  import { getInfo } from '/@/api/system/oa';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { OaType } from '/@/enums/oa';
  import { useUserStore } from '/@/store/modules/user';

  const { t } = useI18n();
  export default defineComponent({
    components: {
      Popover,
      Tabs,
      TabPane: Tabs.TabPane,
      Badge,
      Icon,
      ApprovalProcess,
      LookProcess,
      ModalPanel,
      ViewModal,
    },
    setup() {
      const Approval = ref<{
        taskId?: string;
        processId?: string;
        schemaId?: string;
        visible: boolean;
      }>({
        visible: false,
      });
      const LookData = ref<{
        taskId?: string;
        processId?: string;
        visible: boolean;
      }>({
        visible: false,
      });
      const userStore = useUserStore();
      const loginUserId = userStore.getUserInfo?.id;

      const viewOpen = ref(false);
      let viewData = ref({ briefHead: '' });
      const { prefixCls } = useDesign('header-notify');
      let times: any = ref();
      const listData = ref(tabListData);
      const simpleImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);
      const systemPage = reactive({
        limit: 1,
        total: 10,
        size: 5,
      });
      getDatas();
      times.value = setInterval(() => {
        getDatas();
      }, 100000);
      async function getDatas() {
        listData.value.forEach((o) => {
          o.list = [];
          o.unreadNum = 0;
          if (o.read) o.read = [];
        });
        try {
          let res = await getOaNews({ type: 1, loginUserId });
          res.list.forEach((o) => {
            if (!o.readId) listData.value[0].unreadNum += 1;
            listData.value[0].list.push({
              id: o.id,
              avatar: '',
              title: o.briefHead,
              description: '',
              datetime: o.releaseTime,
              color: '',
              type: '3',
              read: o.isRead,
            });
          });
          let res1 = await getOaNews({ type: 2, loginUserId });
          res1.list.forEach((o) => {
            if (!o.readId) listData.value[1].unreadNum += 1;
            listData.value[1].list.push({
              id: o.id,
              avatar: '',
              title: o.briefHead,
              description: '',
              datetime: o.releaseTime,
              color: '',
              type: '3',
              read: o.isRead,
            });
          });
          let res2 = await getOaMessage({ loginUserId });
          res2.forEach((o) => {
            if (o.messageType === 0) {
              if (!o.isRead) listData.value[2].unreadNum += 1;
              listData.value[2].list.push({
                id: o.id,
                avatar: '',
                title: o.messageContent,
                description: '',
                datetime: o.sendTime,
                timeFormat: o.timeFormat,
                color: '',
                type: '3',
                read: o.isRead,
              });
            } else if (o.messageType == 1) {
              if (!o.isRead) listData.value[3].unreadNum += 1;
              listData.value[3].list.push({
                id: o.id,
                avatar: '',
                title: o.messageContent,
                description: '',
                datetime: o.sendTime,
                timeFormat: o.timeFormat,
                color: '',
                type: '3',
                taskId: o.objectId,
                processId: o.processId,
                schemaId: o.schemaId,
                read: o.isRead,
              });
            } else if (o.messageType == 2) {
              if (!o.isRead) listData.value[3].unreadNum += 1;
              listData.value[3].read?.push({
                id: o.id,
                avatar: '',
                title: o.messageContent,
                description: '',
                datetime: o.sendTime,
                read: o.isRead,
                timeFormat: o.timeFormat,
                color: '',
                type: '3',
                taskId: o.objectId,
                processId: o.processId,
              });
            }
          });
          let res3 = await getScheduleMsg({ loginUserId });
          res3.list.forEach((item) => (item.read = item.isRead));
          listData.value[2].unreadNum = res3.list.filter((x) => !x.isRead).length;
          listData.value[2].list.push(...res3.list);
          await getSystemList();
        } catch (error) {
          clearInterval(times.value);
        }
      }
      async function getSystemList() {
        let res4 = await getSystemMessage({
          limit: systemPage.limit,
          size: systemPage.size,
          loginUserId,
        });
        listData.value[4].list = res4.list;
        systemPage.total = res4.total;
      }
      const count = computed(() => {
        let count = 0;
        for (let i = 0; i < listData.value.length; i++) {
          count += listData.value[i].unreadNum;
        }
        return count;
      });
      async function prevSystemPage() {
        if (systemPage.limit > 1) {
          systemPage.limit--;
          await getSystemList();
        }
      }
      async function nextSystemPage() {
        if (systemPage.total - systemPage.limit * systemPage.size > 0) {
          systemPage.limit++;
          await getSystemList();
        }
      }
      async function setReadAll(type) {
        if (type == 1 || type == 2) {
          let ids: string[] = [];

          listData.value[type - 1].list.forEach((o) => {
            o.read = 1;
            ids.push(o.id);
          });

          await setOaRead(ids);
        } else if (type == 3) {
          await setScheduleReadAll();
          listData.value[type - 1].list.forEach((o) => {
            o.read = 1;
          });
        } else if (type == 4) {
          listData.value[type - 1].list.forEach((o) => {
            o.read = 1;
          });
          await setWorkReadAll();
        }
        listData.value[type - 1].unreadNum = 0;
      }

      async function setReadSingle(ids, num) {
        if (num == 3) {
          await setScheduleRead([ids]);
        } else if (num == 4) {
          await setSingleRead(ids);
        } else {
          await setOaRead([ids]);
        }
        if (listData.value[num - 1].unreadNum > 0) listData.value[num - 1].unreadNum -= 1;
        if (num == 1 || num == 2) {
          const record = await getInfo(ids);
          record.typeId = num == 1 ? OaType.NEWS : OaType.NOTICE;
          viewData.value = record;
          viewOpen.value = true;
        }
      }
      onUnmounted(() => {
        clearInterval(times.value);
      });
      async function ApprovalHandle(it, key, type) {
        if (type == 1) {
          let res = await getApproveState(it.taskId);
          if (res) {
            Approval.value = {
              taskId: it.taskId,
              processId: it.processId,
              schemaId: it.schemaId,
              visible: true,
            };
          } else {
            LookData.value = {
              taskId: it.taskId,
              processId: it.processId,
              visible: true,
            };
          }
        } else {
          LookData.value = {
            taskId: it.taskId,
            processId: it.processId,
            visible: true,
          };
        }
        it.read = 1;
        setReadSingle(it.id, key);
      }
      return {
        prefixCls,
        listData,
        count,
        setReadAll,
        simpleImage,
        numberStyle: {
          top: '18px',
          right: '8px',
        },
        setReadSingle,
        ApprovalHandle,
        Approval,
        LookData,
        t,
        viewOpen,
        viewData,
        systemPage,
        prevSystemPage,
        nextSystemPage,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-notify';

  .@{prefix-cls} {
    padding-top: 2px;

    &__overlay {
      width: 380px;
    }

    .ant-tabs-content {
      width: 300px;
    }

    .ant-badge {
      font-size: 18px;

      .ant-badge-multiple-words {
        padding: 0 4px;
      }

      svg {
        width: 0.9em;
      }
    }
  }
</style>
<style scoped lang="less">
  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f2f2f2;
    font-weight: 400;
    font-size: 12px;
    height: 36px;
    cursor: pointer;

    .header-title {
      font-weight: 700;
      font-size: 14px;
    }

    .opr {
      color: #02a7f0;
      cursor: pointer;
      font-size: 12px;
    }

    &.readed {
      color: gray;
    }
  }

  .list-data-item {
    display: flex;
    height: 36px;
    cursor: pointer;
    border-bottom: 1px solid #f2f2f2;
    justify-content: space-between;
    align-items: center;

    &.readed {
      color: gray;
    }
  }

  .list-item-title {
    width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .list-item-time {
    text-align: right;
  }

  .readed-mark {
    color: #02a7f0;
  }

  .notice-footer {
    display: flex;
    justify-content: space-around;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #f2f2f2;
    font-size: 12px;
    color: #02a7f0;
    cursor: pointer;
  }
</style>
