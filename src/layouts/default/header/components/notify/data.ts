import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();
export interface ListItem {
  id: string;
  avatar?: string;
  // 通知的标题内容
  title: string;
  // 是否在标题上显示删除线
  titleDelete?: boolean;
  datetime?: string;
  type?: string;
  read?: number;
  description?: string;
  clickClose?: boolean;
  extra?: string;
  color?: string;
  taskId?: string;
  processId?: string;
  schemaId?: string;
  timeFormat?: string;
}

export interface TabItem {
  key: string;
  name: string;
  unreadNum: number;
  list: ListItem[];
  read?: ListItem[];
  unreadlist?: ListItem[];
}

export const tabListData: TabItem[] = [
  {
    key: '1',
    name: t('新闻'),
    list: [],
    unreadNum: 0,
  },
  {
    key: '2',
    name: t('通知公告'),
    list: [],
    unreadNum: 0,
  },
  {
    key: '3',
    name: t('日程'),
    list: [],
    unreadNum: 0,
  },
  {
    key: '4',
    name: t('工作流'),
    list: [],
    read: [],
    unreadNum: 0,
  },
  {
    key: '5',
    name: t('系统通知'),
    list: [],
    read: [],
    unreadNum: 0,
  },
];
