<template>
  <Dropdown placement="bottomRight" :overlayClassName="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls, `${prefixCls}--${theme}`]" class="flex">
      <span
        style="border-left: 1px solid rgb(255 255 255 / 30%); height: 30px; padding-right: 15px"
      ></span>
      <div style="margin-right: 12px; height: 30px; margin-top: -12px">
        <a-image
          :width="24"
          :height="24"
          :src="getUserInfo.avatar"
          fallback="src/assets/images/header.jpg"
        />
      </div>
      <span :class="`${prefixCls}__info hidden md:block`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo.name }}
        </span>
      </span>
    </span>

    <template #overlay>
      <AMenu @click="handleMenuClick">
        <MenuItem key="usercenter" :text="t('用户中心')" icon="ant-design:user-switch-outlined" />
        <MenuItem
          v-if="getUseLockPage"
          key="lock"
          :text="t('锁定屏幕')"
          icon="ion:lock-closed-outline"
        />
        <MenuItem key="logout" :text="t('退出系统')" icon="ion:power-outline" />
      </AMenu>
    </template>
  </Dropdown>
  <LockAction @register="register" />
</template>
<script lang="ts">
  // components
  import { Dropdown, Menu } from 'ant-design-vue';

  import { defineComponent, computed, inject } from 'vue';

  import { DOC_URL } from '/@/settings/siteSetting';

  import { useUserStore } from '/@/store/modules/user';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useModal } from '/@/components/Modal';

  import headerImg from '/@/assets/images/header.jpg';
  import { propTypes } from '/@/utils/propTypes';
  import { openWindow } from '/@/utils';

  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useGo } from '/@/hooks/web/usePage';

  // type MenuEvent = 'logout' | 'doc' | 'lock' | 'usercenter';

  export default defineComponent({
    name: 'UserDropdown',
    components: {
      Dropdown,
      AMenu: Menu,
      MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
      MenuDivider: Menu.Divider,
      LockAction: createAsyncComponent(() => import('../lock/LockModal.vue')),
    },
    props: {
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup() {
      const { prefixCls } = useDesign('header-user-dropdown');
      const { t } = useI18n();
      const { getShowDoc, getUseLockPage } = useHeaderSetting();
      const userStore = useUserStore();
      const go = useGo();
      const keycloak = inject('keycloak');

      const getUserInfo = computed(() => {
        const { name = '', avatar, desc } = userStore.getUserInfo || {};
        return { name, avatar: avatar || headerImg, desc };
      });

      const [register, { openModal }] = useModal();

      function handleLock() {
        openModal(true);
      }

      //  login out
      function handleLoginOut() {
        userStore.confirmLoginOut();

        if (keycloak) {
          keycloak.logout();
        }
      }

      // open doc
      function openDoc() {
        openWindow(DOC_URL);
      }

      // updatePwd
      function openUserCenter() {
        go(PageEnum.USER_CENTER);
      }

      function handleMenuClick(e) {
        switch (e.key) {
          case 'logout':
            handleLoginOut();
            break;
          case 'doc':
            openDoc();
            break;
          case 'lock':
            handleLock();
            break;
          case 'usercenter':
            openUserCenter();
            break;
        }
      }

      return {
        prefixCls,
        t,
        getUserInfo,
        handleMenuClick,
        getShowDoc,
        register,
        getUseLockPage,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    height: @header-height;
    padding: 0 0 0 10px;
    padding-right: 10px;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;
    align-items: center;

    &__name {
      font-size: 14px;
    }

    // &--dark {
    //   &:hover {
    //     background-color: @header-dark-bg-hover-color;
    //   }
    // }

    &--light {
      &:hover {
        background-color: @header-light-bg-hover-color;
      }

      .@{prefix-cls}__name {
        color: @text-color-base;
      }

      .@{prefix-cls}__desc {
        color: @header-light-desc-color;
      }
    }

    &-dropdown-overlay {
      .ant-dropdown-menu-item {
        min-width: 160px;
      }
    }
  }
</style>
