<template>
  <Tooltip :title="getTitle" placement="bottom" :mouseEnterDelay="0.5">
    <span @click="toggle">
      <Icon icon="material-symbols:fit-screen-rounded" size="26" v-if="!isFullscreen" />
      <Icon icon="mingcute:fullscreen-exit-fill" size="24" v-else />
    </span>
  </Tooltip>
</template>
<script lang="ts">
  import { defineComponent, computed, unref } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useFullscreen } from '@vueuse/core';
  import { Icon } from '/@/components/Icon';

  export default defineComponent({
    name: 'FullScreen',
    components: { Icon, Tooltip },

    setup() {
      const { t } = useI18n();
      const { toggle, isFullscreen } = useFullscreen();

      const getTitle = computed(() => {
        return unref(isFullscreen) ? t('退出全屏') : t('全屏');
      });

      return {
        getTitle,
        isFullscreen,
        toggle,
      };
    },
  });
</script>
