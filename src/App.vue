<template>
  <ConfigProvider :locale="antdLocal">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';
  import en from 'ant-design-vue/lib/locale-provider/en_US';
  import zhTW from 'ant-design-vue/lib/locale-provider/zh_TW';
  import { ConfigProvider } from 'ant-design-vue';
  import { AppProvider } from '/@/components/Application';
  import { useTitle } from '/@/hooks/web/useTitle';
  import { useLocale } from '/@/locales/useLocale';
  import { useAppStore } from '/@/store/modules/app';
  import 'dayjs/locale/zh-cn';
  import { computed } from 'vue';
  import { getLogoInfo } from './api/system/login';
  import { onBeforeUnmount } from 'vue';
  // support Multi-language

  const { getLocale } = useLocale();
  const appStore = useAppStore();
  getLogoInfo().then((res) => {
    appStore.setLogoConfig({
      companyName: res.companyName,
      shortName: res.shortName,
      refreshLogoUrl: res.refreshLogoUrl,
      backgroundLogoUrl: res.backgroundLogoUrl,
      designerLogoUrl: res.designerLogoUrl,
      loginLogoUrl: res.loginLogoUrl,
      menuLogoUrl: res.menuLogoUrl,
    });
  });
  const antdLocal = computed(() => {
    if (getLocale.value === 'zh_CN') {
      return zhCN;
    }
    if (getLocale.value === 'en') {
      return en;
    }
    if (getLocale.value === 'zh_TW') {
      return zhTW;
    }
    return zhCN;
  });

  const beforeUnloadHandler = (e) => {
    if (e) {
      e.returnValue = '关闭提示';
    }
    return '关闭提示';
  };

  window.addEventListener('beforeunload', beforeUnloadHandler);

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', beforeUnloadHandler);
  });

  // Listening to page changes and dynamically changing site titles
  useTitle();
</script>
<style lang="less" scoped>
  :deep(.ant-tree-treenode),
  :deep(.ant-tree-node-content-wrapper) {
    width: 100%;
    height: 35px;
    line-height: 35px;
  }

  :deep(.ant-tree .ant-tree-node-content-wrapper) {
    height: 35px;
    line-height: 35px;
  }

  .ant-input-affix-wrapper::before {
    width: 0;
    visibility: hidden;
    content: '' !important;
  }
</style>
