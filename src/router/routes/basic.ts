import type { AppRouteRecordRaw } from '/@/router/types';
import { t } from '/@/hooks/web/useI18n';
import {
  REDIRECT_NAME,
  LAYOUT,
  EXCEPTION_COMPONENT,
  PAGE_FOUND_NAME,
  PAGE_NOT_FOUND_NAME,
} from '/@/router/constant';
import { PageEnum } from '/@/enums/pageEnum';

// 404 on a page
export const PAGE_NOT_FOUND_ROUTE: AppRouteRecordRaw = {
  path: '/:path(.*)*',
  component: LAYOUT,
  name: PAGE_NOT_FOUND_NAME,
  meta: {
    title: 'ErrorPage',
    hideBreadcrumb: true,
    hideMenu: true,
  },
  children: [
    {
      path: '/:path(.*)*',
      component: EXCEPTION_COMPONENT,
      meta: {
        title: 'ErrorPage',
        hideBreadcrumb: true,
        hideMenu: true,
      },
    },
  ],
};

export const REDIRECT_ROUTE: AppRouteRecordRaw = {
  path: '/redirect',
  component: LAYOUT,
  name: 'RedirectTo',
  meta: {
    title: REDIRECT_NAME,
    hideBreadcrumb: true,
    hideMenu: true,
  },
  children: [
    {
      path: '/redirect/:path(.*)',
      name: REDIRECT_NAME,
      component: () => import('/@/views/sys/redirect/index.vue'),
      meta: {
        title: REDIRECT_NAME,
        hideBreadcrumb: true,
      },
    },
  ],
};

export const ERROR_LOG_ROUTE: AppRouteRecordRaw = {
  path: '/error-log',
  name: 'ErrorLog',
  component: LAYOUT,
  redirect: '/error-log/list',
  meta: {
    title: 'ErrorLog',
    hideBreadcrumb: true,
    hideChildrenInMenu: true,
  },
  children: [
    {
      path: 'list',
      name: 'ErrorLogList',
      component: () => import('/@/views/sys/error-log/index.vue'),
      meta: {
        title: t('错误日志列表'),
        hideBreadcrumb: true,
        currentActiveMenu: '/error-log',
      },
    },
  ],
};

export const SYSTEM_ROUTE: AppRouteRecordRaw = {
  path: '/dashboard',
  name: 'Dashboard',
  component: LAYOUT,
  redirect: '/dashboard/analysis',
  meta: {
    orderNo: 10,
    icon: 'ion:grid-outline',
    title: t('首页'),
  },
  children: [
    {
      path: 'analysis',
      name: 'Analysis',
      component: () => import('/@/views/dashboard/analysis/index.vue'),
      meta: {
        affix: true,
        title: t('首页'),
      },
    },
    {
      path: 'workbench',
      name: 'Workbench',
      component: () => import('/@/views/dashboard/workbench/index.vue'),
      meta: {
        title: t('工作台'),
      },
    },
  ],
};

export const USERCENTER_ROUTE: AppRouteRecordRaw = {
  path: '/user-center',
  name: 'UserCenter',
  component: LAYOUT,
  redirect: PageEnum.USER_CENTER,
  meta: {
    title: t('用户中心'),
  },
  children: [
    {
      path: 'info',
      name: 'UserInfo',
      component: () => import('/@/views/system/setting/index.vue'),
      meta: {
        title: t('用户中心'),
        icon: 'ant-design:user-outlined',
      },
    },
  ],
};

// export const IM_ROUTE: AppRouteRecordRaw = {
//   path: '/im',
//   name: 'IM',
//   component: LAYOUT,
//   redirect: PageEnum.IM_PAGE,
//   meta: {
//     title: 'IM',
//   },
//   children: [
//     {
//       path: 'info',
//       name: 'Info',
//       component: () => import('/@/views/dataconfig/im/index.vue'),
//       meta: {
//         title: 'IM',
//       },
//     },
//   ],
// };

// export const CUSTOMFORM_ROUTE: AppRouteRecordRaw = {
//   path: '/custom-form/:id',
//   name: 'CustomForm',
//   component: LAYOUT,
//   // redirect: PageEnum.CUSTOM_FORM,
//   meta: {
//     title: t('自定义表单'),
//   },
//   children: [
//     {
//       path: 'list',
//       name: 'CustomFormList',
//       component: () => import('/@/views/form/template/index.vue'),
//       meta: {
//         title: `自定义表单`,
//         icon: 'ant-design:user-outlined',
//       },
//     },
//   ],
// };
