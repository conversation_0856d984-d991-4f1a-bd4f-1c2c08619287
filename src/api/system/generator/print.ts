import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  PageResult,
  PrintBasicData,
  PrintConfigData,
  SearchParams,
} from '/@/model/generator/print';

enum Api {
  Page = '/print/page',
  List = '/print/list',
  Info = '/print/info',
  PrintDesign = '/print',
  EnabledDesign = '/print/enabled',
  PrintConfig = '/print/config',
  PrintConfigInfo = '/print/config/info',
}
/**
 * @description: 查询不分页列表
 */

export async function getDesignPrintList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PrintBasicData[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询分页列表
 */

export async function getDesignPrintPage(params: SearchParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取信息
 */
export async function getDesignInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增设计
 */
export async function addDesign(params: PrintBasicData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.PrintDesign,
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 编辑设计
 */
export async function editDesign(params: PrintBasicData, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.PrintDesign,
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 禁用设计
 */
export async function disabledDesign(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.EnabledDesign,
      params: { id, enabledMark: 0 },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 启用设计
 */
export async function enabledDesign(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.EnabledDesign,
      params: { id, enabledMark: 1 },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除（批量删除）
 */
export async function deleteDesign(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.PrintDesign,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 设置菜单按钮api配置
 */
export async function addPrintConfig(params: PrintConfigData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.PrintConfig,
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取菜单按钮api配置信息
 */
export async function getPrintConfigInfo(
  buttonCode: String,
  menuId: String,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.PrintConfigInfo,
      params: { buttonCode, menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
