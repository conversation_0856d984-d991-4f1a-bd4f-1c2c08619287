import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';
import { PageResult, BasicData, SearchParams } from '/@/model/generator/messageTemplate';

enum Api {
  Page = '/system/messageTemplate/page',
  Design = '/system/messageTemplate',
  Info = '/system/messageTemplate/info',
}
/**
 * @description: 查询分页列表
 */

export async function getPage(params: SearchParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<PageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增
 */
export async function add(params: BasicData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Design,
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑
 */
export async function edit(params: BasicData, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Design,
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除（批量删除）
 */
export async function deleteDesign(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Design,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取信息
 */
export async function getInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
