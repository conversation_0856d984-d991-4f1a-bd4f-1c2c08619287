import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface authPageListParams {
  keyword?: string; //关键字
}

/**
 * @description: Request list interface parameters
 */
export type AuthPageListSearchModel = BasicPageParams & authPageListParams;

export interface AuthPageListModel {
  code: string; //编码
  name: string; //名称
  authType: string; //对象类型
  createUserName: number; //创建人
  remark: string; //备注
}

/**
 * @description: Request list return value
 */
export type AuthPageListResultModel = BasicFetchResult<AuthPageListModel>;

export interface AuthModel {
  data: authConfig;
}
interface authConfig {
  name: string;
  objectIdList: string;
  remark?: string;
  code: string;
  authType: number;
  authMethod: number;
  authFormula: string;
}
