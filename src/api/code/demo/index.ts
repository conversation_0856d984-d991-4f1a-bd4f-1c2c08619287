import { DemoModel, DemoPageParams, DemoPageResult } from './model/DemoModel';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  Page = '/system/demo/page',
  List = '/system/demo/list',
  Info = '/system/demo/info',
  Demo = '/system/demo',
}

/**
 * @description: 查询Demo分页列表
 */
export async function getDemoPage(params: DemoPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DemoPageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取Demo信息
 */
export async function getDemo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DemoModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增Demo
 */
export async function addDemo(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Demo,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新Demo
 */
export async function updateDemo(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Demo,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除Demo（批量删除）
 */
export async function deleteDemo(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Demo,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
