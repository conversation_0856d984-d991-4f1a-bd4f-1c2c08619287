import { TaskResult } from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

import { TaskTypeUrl } from '/@/enums/workflowEnum';
import { BasicPageParams } from '../model/baseModel';
import { FlowInfo } from '/@/model/workflow/bpmnConfig';

enum Api {
  MoveRecycle = '/workflow/execute/my-process/move-recycle',
  Draft = '/workflow/execute/draft',
  DraftInfo = '/workflow/execute/draft/info',
  DeleteRecycle = '/workflow/execute/my-process/recycle',
  AddMaintain = '/system/stamp/add-maintain',
  SelfRecord = '/workflow/execute/process/all-record',
  Withdraw = '/workflow/execute/withdraw-process-info',
}
/**
 * @description: 撤回流程任务
 */

export async function getWithdrawTasks(
  taskId: String,
  withdrawNodeId: String,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FlowInfo>(
    {
      url: Api.Withdraw,
      params: { taskId, withdrawNodeId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询流程任务
 */

export async function getTasks(
  taskUrl: TaskTypeUrl,
  params: BasicPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<TaskResult>(
    {
      url: taskUrl,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询流程任务各种列表
 */

export async function getSchemaTask(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<TaskResult>(
    {
      url: params.taskUrl,
      params: params.data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 我的流程 移入回收站
 */

export async function moveRecycle(processId, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.MoveRecycle,
      params: { processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 保存草稿
 */
export async function postDraft(
  schemaId: string,
  formData: Array<Recordable>,
  dataId?: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.Draft,
      params: { formData, schemaId, dataId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除回收站
 */
export async function deleteRecycle(processIds: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.DeleteRecycle,
      data: { processIds },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 修改草稿
 */
export async function putDraft(
  schemaId: string,
  formData: Array<Recordable>,
  id: string,
  dataId?: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.Draft,
      params: { formData, schemaId, id, dataId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除草稿
 */
export async function deleteDraft(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Draft,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取草稿详情
 */
export async function getDraftInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<{
    formData: string;
    name: string;
    remark: string | null;
    schemaId: string;
  }>(
    {
      url: Api.DraftInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询仅本人查看的流转记录
 */

export async function getSelfRecords(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.SelfRecord,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
