import { ErrorTypeEnum } from '/@/enums/exceptionEnum';
import { MenuModeEnum, MenuTypeEnum } from '/@/enums/menuEnum';
import { RoleInfo } from '/@/api/sys/model/userModel';

// Lock screen information
export interface LockInfo {
  // Password required
  pwd?: string | undefined;
  // Is it locked?
  isLock?: boolean;
}

// Error-log information
export interface ErrorLogInfo {
  // Type of error
  type: ErrorTypeEnum;
  // Error file
  file: string;
  // Error name
  name?: string;
  // Error message
  message: string;
  // Error stack
  stack?: string;
  // Error detail
  detail: string;
  // Error url
  url: string;
  // Error time
  time?: string;
}

export interface UserInfo {
  // 用户id
  id: string;
  // 真实名字
  name: string;
  // 编码
  code: string;
  // 手机
  mobile: string;
  //角色
  roles: RoleInfo[];
  // 用户名
  userName: string;
  // 头像
  avatar: string;
  //部门id
  departmentId: string;
  //部门名称
  departmentName: string;
  //备注
  remark: string;
  //邮箱
  email: string;
  //地址
  address: string;
  desc?: string;
  homePath?: string;
  //性别
  gender: string;
  nickName: string;
  //密码
  password: string;
  //岗位id
  postId: string;
  //岗位名称
  postName: string;
  //角色名称
  roleName: string;
  //角色id
  roleId: string;
  //所有部门
  departments: DepartmentInfo[];
  //所有部门
  posts: PostInfo[];
  desktopSchema: DesktopData;
}

export interface BeforeMiniState {
  menuCollapsed?: boolean;
  menuSplit?: boolean;
  menuMode?: MenuModeEnum;
  menuType?: MenuTypeEnum;
}
